from pydantic import BaseModel
from typing import Dict, List, Optional, Any, Union

class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[Dict[str, Any]]]  # 支持字符串或多媒体数组格式

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    stream: Optional[bool] = False
    temperature: Optional[float] = 1.0
    max_tokens: Optional[int] = None
    top_p: Optional[float] = 1.0
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0
    # 新增参数
    thinking_budget: Optional[int] = None
    enable_thinking: Optional[bool] = None

class AdminLoginRequest(BaseModel):
    password: str

class ConfigUpdateRequest(BaseModel):
    config: Dict[str, Any]

class ClientLoginRequest(BaseModel):
    password: str

class ImageUploadResponse(BaseModel):
    success: bool
    image_url: Optional[str] = None  # Base64格式的数据URL
    local_path: Optional[str] = None  # 本地文件路径
    error: Optional[str] = None 