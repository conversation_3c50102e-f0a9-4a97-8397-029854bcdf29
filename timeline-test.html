<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线测试</title>
    <style>
        :root {
            --primary: #667eea;
            --primary-rgb: 102, 126, 234;
            --accent: #4facfe;
            --accent-rgb: 79, 172, 254;
            --bg-main: #ffffff;
            --bg-secondary: #f8fafc;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-tertiary: #a0aec0;
            --border: #e2e8f0;
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
            --radius: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-main);
            margin: 0;
            padding: 20px;
            color: var(--text-primary);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-secondary);
            border-radius: var(--radius);
            padding: 20px;
            position: relative;
            min-height: 500px;
        }

        .message {
            margin: 20px 0;
            padding: 15px;
            border-radius: var(--radius);
            background: white;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .message.user {
            background: rgba(var(--primary-rgb), 0.1);
            border-color: var(--primary);
        }

        .message.timeline-highlight {
            background: linear-gradient(90deg, 
                rgba(var(--primary-rgb), 0.1) 0%, 
                rgba(var(--primary-rgb), 0.05) 50%, 
                rgba(var(--primary-rgb), 0.1) 100%);
            border-left: 3px solid var(--primary);
            transform: translateX(3px);
            box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
        }

        .controls {
            margin-bottom: 20px;
        }

        button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--radius);
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            opacity: 0.9;
        }

        /* 简洁时间线样式 */
        .simple-timeline {
            position: fixed;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
            z-index: 1000;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .simple-timeline:hover {
            opacity: 1;
        }

        .timeline-track {
            position: relative;
            height: 70vh;
            min-height: 300px;
            max-height: 600px;
            width: 20px;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .timeline-track::-webkit-scrollbar {
            display: none;
        }

        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(var(--primary-rgb), 0.2);
            transform: translateX(-50%);
        }

        .timeline-dots {
            position: relative;
            height: 100%;
            padding: 10px 0;
        }

        .timeline-dot {
            position: absolute;
            left: 50%;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            transform: translateX(-50%);
            cursor: pointer;
            transition: all 0.2s ease;
            background: var(--primary);
        }

        .timeline-dot.user {
            background: var(--accent);
        }

        .timeline-dot:hover {
            width: 6px;
            height: 6px;
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
        }

        .timeline-dot.user:hover {
            box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
        }

        .timeline-dot.milestone {
            width: 6px;
            height: 6px;
            position: relative;
        }

        .timeline-dot.milestone::after {
            content: attr(data-number);
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--bg-main);
            color: var(--text-secondary);
            font-size: 9px;
            font-weight: 600;
            padding: 1px 3px;
            border-radius: 2px;
            border: 1px solid var(--border);
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .timeline-dot.milestone:hover::after {
            opacity: 1;
        }

        /* 预览弹窗 */
        .timeline-preview {
            position: fixed;
            right: 40px;
            z-index: 1001;
            max-width: 280px;
            background: var(--bg-main);
            border: 1px solid var(--border);
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateX(10px);
            transition: all 0.2s ease;
            pointer-events: none;
        }

        .timeline-preview.show {
            opacity: 1;
            transform: translateX(0);
            pointer-events: auto;
        }

        .preview-content {
            padding: 8px;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            font-size: 11px;
        }

        .preview-role {
            font-weight: 600;
            color: var(--primary);
            padding: 2px 6px;
            background: rgba(var(--primary-rgb), 0.1);
            border-radius: 8px;
            font-size: 10px;
        }

        .preview-role.user {
            color: var(--accent);
            background: rgba(var(--accent-rgb), 0.1);
        }

        .preview-time {
            color: var(--text-tertiary);
            font-size: 10px;
        }

        .preview-text {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.3;
            max-height: 80px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <button onclick="toggleTimeline()">切换时间线</button>
            <button onclick="addMessage()">添加消息</button>
            <button onclick="addManyMessages()">添加20条消息</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <div id="messages">
            <div class="message user">用户消息 1: 你好，这是第一条用户消息</div>
            <div class="message">AI回复 1: 你好！很高兴为您服务，这是第一条AI回复</div>
            <div class="message user">用户消息 2: 请帮我解释一下时间线功能</div>
            <div class="message">AI回复 2: 时间线功能可以让您快速浏览和跳转到对话中的任意消息，非常方便！</div>
            <div class="message user">用户消息 3: 太棒了，这个功能很实用</div>
            <div class="message">AI回复 3: 谢谢您的认可！如果您有其他问题，随时可以问我。</div>
        </div>
    </div>

    <!-- 简洁时间线 -->
    <div class="simple-timeline" id="simpleTimeline" style="display: none;">
        <div class="timeline-track">
            <div class="timeline-line"></div>
            <div class="timeline-dots" id="timelineDots"></div>
        </div>
    </div>
    
    <!-- 时间线预览弹窗 -->
    <div class="timeline-preview" id="timelinePreview" style="display: none;">
        <div class="preview-content">
            <div class="preview-header">
                <span class="preview-role"></span>
                <span class="preview-time"></span>
            </div>
            <div class="preview-text"></div>
        </div>
    </div>

    <script>
        let timelineVisible = false;
        let messageCount = 6;

        function toggleTimeline() {
            const timeline = document.getElementById('simpleTimeline');
            timelineVisible = !timelineVisible;
            
            if (timelineVisible) {
                timeline.style.display = 'block';
                updateTimeline();
            } else {
                timeline.style.display = 'none';
            }
        }

        function updateTimeline() {
            const timelineDots = document.getElementById('timelineDots');
            const messages = document.querySelectorAll('#messages .message');

            timelineDots.innerHTML = '';

            const messageCount = messages.length;
            if (messageCount === 0) return;

            // 计算可用高度
            const trackHeight = timelineDots.parentElement.clientHeight - 20; // 减去padding
            const spacing = Math.max(12, Math.min(25, trackHeight / messageCount)); // 动态间距

            messages.forEach((message, index) => {
                const isUser = message.classList.contains('user');
                const messageNumber = index + 1;
                const dot = document.createElement('div');

                // 基础类名
                let className = `timeline-dot ${isUser ? 'user' : ''}`;

                // 10的倍数添加里程碑标记
                if (messageNumber % 10 === 0) {
                    className += ' milestone';
                    dot.dataset.number = messageNumber.toString();
                }

                dot.className = className;
                dot.dataset.messageIndex = index;

                // 计算节点位置（均匀分布）
                const position = 10 + (index * spacing);
                dot.style.top = `${position}px`;

                dot.addEventListener('mouseenter', (e) => showPreview(e, message, index));
                dot.addEventListener('mouseleave', hidePreview);
                dot.addEventListener('click', () => scrollToMessage(index));

                timelineDots.appendChild(dot);
            });
        }

        function showPreview(event, message, index) {
            const preview = document.getElementById('timelinePreview');
            const roleElement = preview.querySelector('.preview-role');
            const timeElement = preview.querySelector('.preview-time');
            const textElement = preview.querySelector('.preview-text');
            
            const isUser = message.classList.contains('user');
            roleElement.textContent = isUser ? '用户' : 'AI';
            roleElement.className = `preview-role ${isUser ? 'user' : ''}`;
            
            timeElement.textContent = `消息 ${index + 1}`;
            textElement.textContent = message.textContent;
            
            const dotRect = event.target.getBoundingClientRect();
            preview.style.top = `${dotRect.top + window.scrollY - preview.offsetHeight / 2}px`;
            
            preview.classList.add('show');
        }

        function hidePreview() {
            const preview = document.getElementById('timelinePreview');
            preview.classList.remove('show');
        }

        function scrollToMessage(index) {
            const messages = document.querySelectorAll('#messages .message');
            if (index >= 0 && index < messages.length) {
                const targetMessage = messages[index];
                
                messages.forEach(el => el.classList.remove('timeline-highlight'));
                targetMessage.classList.add('timeline-highlight');
                
                targetMessage.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                
                setTimeout(() => {
                    targetMessage.classList.remove('timeline-highlight');
                }, 3000);
                
                updateActiveState(index);
            }
        }

        function updateActiveState(activeIndex) {
            const dots = document.querySelectorAll('.timeline-dot');
            dots.forEach((dot, index) => {
                if (index === activeIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        function addMessage() {
            const messagesContainer = document.getElementById('messages');
            const isUser = messageCount % 2 === 1;
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : ''}`;
            messageDiv.textContent = `${isUser ? '用户' : 'AI'}消息 ${Math.ceil(messageCount / 2)}: 这是第${messageCount}条消息`;
            messagesContainer.appendChild(messageDiv);
            messageCount++;

            if (timelineVisible) {
                updateTimeline();
            }
        }

        function addManyMessages() {
            for (let i = 0; i < 20; i++) {
                addMessage();
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 1;
            if (timelineVisible) {
                updateTimeline();
            }
        }
    </script>
</body>
</html>
