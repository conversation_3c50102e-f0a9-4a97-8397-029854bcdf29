#!/usr/bin/env python3
"""
云端储存功能测试脚本
测试新的云端组分类储存、去重检测和批量操作功能
"""

import sys
import os
import json
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from cloud_storage import cloud_storage

def test_cloud_group_creation():
    """测试云端组创建功能"""
    print("=== 测试云端组创建 ===")
    
    try:
        # 创建测试组
        group1 = cloud_storage.create_cloud_group("测试组1", "test_user")
        print(f"✅ 成功创建组1: {group1['group_id']}")
        
        group2 = cloud_storage.create_cloud_group("工作记录", "test_user")
        print(f"✅ 成功创建组2: {group2['group_id']}")
        
        # 列出用户的组
        groups = cloud_storage.list_cloud_groups("test_user")
        print(f"✅ 用户组列表: {len(groups)} 个组")
        for group in groups:
            print(f"   - {group['group_name']} ({group['group_id']})")
            
        return groups[0]['group_id'] if groups else None
        
    except Exception as e:
        print(f"❌ 云端组创建测试失败: {e}")
        return None

def test_conversation_save_and_duplicate():
    """测试对话保存和去重检测"""
    print("\n=== 测试对话保存和去重检测 ===")
    
    group_id = test_cloud_group_creation()
    if not group_id:
        print("❌ 无法获取测试组ID，跳过对话测试")
        return
    
    try:
        # 创建测试对话数据
        test_conversation = {
            "id": "test_conv_001",
            "title": "测试对话",
            "messages": [
                {"role": "user", "content": "你好"},
                {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"}
            ],
            "timestamp": int(datetime.now().timestamp() * 1000),
            "is_favorite": False
        }
        
        # 第一次保存
        result1 = cloud_storage.save_conversation(test_conversation, group_id=group_id)
        print(f"✅ 第一次保存: {result1}")
        
        # 第二次保存相同内容（应该检测到重复）
        result2 = cloud_storage.save_conversation(test_conversation, group_id=group_id)
        print(f"✅ 第二次保存: {result2}")
        
        if result2.get('is_duplicate'):
            print("✅ 去重检测正常工作")
        else:
            print("⚠️ 去重检测可能有问题")
            
        # 保存不同内容的对话
        test_conversation2 = test_conversation.copy()
        test_conversation2["id"] = "test_conv_002"
        test_conversation2["messages"] = [
            {"role": "user", "content": "今天天气怎么样？"},
            {"role": "assistant", "content": "我无法获取实时天气信息。"}
        ]
        
        result3 = cloud_storage.save_conversation(test_conversation2, group_id=group_id)
        print(f"✅ 保存不同对话: {result3}")
        
        return [result1.get('conversation_id'), result3.get('conversation_id')]
        
    except Exception as e:
        print(f"❌ 对话保存测试失败: {e}")
        return []

def test_batch_operations():
    """测试批量操作"""
    print("\n=== 测试批量操作 ===")
    
    group_id = test_cloud_group_creation()
    if not group_id:
        print("❌ 无法获取测试组ID，跳过批量操作测试")
        return
    
    try:
        # 创建多个测试对话
        conversations = []
        for i in range(3):
            conv = {
                "id": f"batch_test_{i}",
                "title": f"批量测试对话 {i+1}",
                "messages": [
                    {"role": "user", "content": f"这是第{i+1}个测试消息"},
                    {"role": "assistant", "content": f"收到第{i+1}个测试消息"}
                ],
                "timestamp": int(datetime.now().timestamp() * 1000),
                "is_favorite": i == 1  # 第二个设为收藏
            }
            conversations.append(conv)
        
        # 批量保存
        batch_result = cloud_storage.batch_save_conversations(conversations, group_id)
        print(f"✅ 批量保存结果: {batch_result}")
        
        # 批量删除
        conv_ids = [f"batch_test_{i}" for i in range(3)]
        delete_result = cloud_storage.batch_delete_conversations(conv_ids, group_id)
        print(f"✅ 批量删除结果: {delete_result}")
        
    except Exception as e:
        print(f"❌ 批量操作测试失败: {e}")

def test_conversation_listing():
    """测试对话列表功能"""
    print("\n=== 测试对话列表功能 ===")
    
    group_id = test_cloud_group_creation()
    if not group_id:
        print("❌ 无法获取测试组ID，跳过列表测试")
        return
    
    try:
        # 列出指定组的对话
        conversations = cloud_storage.list_conversations(group_id=group_id)
        print(f"✅ 组内对话数量: {len(conversations)}")
        
        # 列出所有对话
        all_conversations = cloud_storage.list_conversations()
        print(f"✅ 总对话数量: {len(all_conversations)}")
        
        for conv in conversations[:3]:  # 只显示前3个
            print(f"   - {conv.get('title', '未命名')} (ID: {conv.get('cloud_id', 'N/A')})")
            
    except Exception as e:
        print(f"❌ 对话列表测试失败: {e}")

def test_storage_stats():
    """测试储存统计功能"""
    print("\n=== 测试储存统计 ===")
    
    try:
        stats = cloud_storage.get_storage_stats()
        print(f"✅ 储存统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"❌ 储存统计测试失败: {e}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        # 删除测试组（这会删除组内的所有对话）
        groups = cloud_storage.list_cloud_groups("test_user")
        for group in groups:
            if "测试" in group['group_name'] or "工作记录" in group['group_name']:
                # 删除组内所有对话
                conversations = cloud_storage.list_conversations(group_id=group['group_id'])
                for conv in conversations:
                    try:
                        cloud_storage.delete_conversation(conv['cloud_id'], group['group_id'])
                    except:
                        pass
                
                # 删除组元数据文件
                import os
                metadata_file = os.path.join(cloud_storage.metadata_path, f"{group['group_id']}.json")
                if os.path.exists(metadata_file):
                    os.remove(metadata_file)
                
                # 删除组文件夹
                group_folder = cloud_storage.get_group_folder(group['group_id'])
                if os.path.exists(group_folder):
                    import shutil
                    shutil.rmtree(group_folder)
                
                print(f"✅ 已清理测试组: {group['group_name']}")
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始云端储存功能测试")
    print("=" * 50)
    
    # 运行所有测试
    test_cloud_group_creation()
    test_conversation_save_and_duplicate()
    test_batch_operations()
    test_conversation_listing()
    test_storage_stats()
    
    # 清理测试数据
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print("✅ 云端储存功能测试完成")

if __name__ == "__main__":
    main()
