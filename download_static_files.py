#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态资源下载脚本
检查并下载HTML文件中引用的所有外部资源到static目录
"""

import os
import requests
import re
from urllib.parse import urlparse
from pathlib import Path
import time

class StaticFilesDownloader:
    def __init__(self, html_file="chat.html", static_dir="static"):
        self.html_file = html_file
        self.static_dir = static_dir
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 确保static目录结构存在
        self.ensure_directories()
        
        # 定义需要下载的资源
        self.resources = self.extract_resources()
    
    def ensure_directories(self):
        """确保static目录结构存在"""
        directories = [
            os.path.join(self.static_dir, "css"),
            os.path.join(self.static_dir, "js"),
            os.path.join(self.static_dir, "fonts"),
            os.path.join(self.static_dir, "img"),  # 添加图片目录
            os.path.join(self.static_dir, "js", "languages")  # highlight.js语言包
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 确保目录存在: {directory}")
    
    def extract_resources(self):
        """从HTML文件提取所有外部资源URL"""
        if not os.path.exists(self.html_file):
            print(f"❌ HTML文件不存在: {self.html_file}")
            return []
        
        print(f"📖 读取HTML文件: {self.html_file}")
        
        with open(self.html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        resources = []
        
        # CSS文件
        css_resources = [
            # Bootstrap CSS
            ("https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "css/bootstrap.min.css"),
            # KaTeX CSS
            ("https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css", "css/katex.min.css"),
            # Highlight.js CSS (浅色主题)
            ("https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css", "css/github.min.css"),
            # Highlight.js CSS (暗色主题)
            ("https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github-dark.min.css", "css/github-dark.min.css"),
            # FontAwesome CSS
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/css/all.min.css", "css/all.min.css"),
        ]
        
        # JavaScript文件
        js_resources = [
            # Bootstrap JS
            ("https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "js/bootstrap.bundle.min.js"),
            # Marked JS
            ("https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js", "js/marked.min.js"),
            # KaTeX JS
            ("https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js", "js/katex.min.js"),
            # KaTeX Auto-render
            ("https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js", "js/auto-render.min.js"),
            # Highlight.js Core
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/highlight.min.js", "js/highlight.min.js"),
            # Highlight.js 语言包
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/javascript.min.js", "js/languages/javascript.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/python.min.js", "js/languages/python.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/java.min.js", "js/languages/java.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/cpp.min.js", "js/languages/cpp.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/sql.min.js", "js/languages/sql.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/languages/json.min.js", "js/languages/json.min.js"),
            # Mermaid JS
            ("https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js", "js/mermaid.min.js"),
        ]
        
        # 字体文件 (Google Fonts - 需要特殊处理)
        font_resources = [
            ("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Noto+Sans+SC:wght@300;400;500;600&display=swap", "css/google-fonts.css"),
        ]
        
        # 图片资源
        img_resources = [
            ("https://imgbed.killerbest.com/file/1749487165619_image.png", "img/logo.png"),
        ]
        
        resources.extend(css_resources)
        resources.extend(js_resources)
        resources.extend(font_resources)
        resources.extend(img_resources)
        
        # FontAwesome字体文件 (从CSS中提取)
        resources.extend(self.get_fontawesome_fonts())
        
        return resources
    
    def get_fontawesome_fonts(self):
        """获取FontAwesome字体文件列表 - 只下载必要格式"""
        font_files = [
            # 只下载 woff2 格式（现代浏览器支持最好，文件最小）和 ttf 作为回退
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-solid-900.woff2", "fonts/fa-solid-900.woff2"),
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-regular-400.woff2", "fonts/fa-regular-400.woff2"),
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-brands-400.woff2", "fonts/fa-brands-400.woff2"),
            # TTF 作为最后回退（兼容性最好）
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-solid-900.ttf", "fonts/fa-solid-900.ttf"),
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-regular-400.ttf", "fonts/fa-regular-400.ttf"),
            ("https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/webfonts/fa-brands-400.ttf", "fonts/fa-brands-400.ttf"),
        ]
        return font_files
    
    def download_file(self, url, local_path, retries=3):
        """下载单个文件"""
        full_path = os.path.join(self.static_dir, local_path)
        
        # 检查文件是否已存在
        if os.path.exists(full_path):
            file_size = os.path.getsize(full_path)
            if file_size > 0:
                print(f"✓ 文件已存在: {local_path} ({file_size} bytes)")
                return True
        
        print(f"⬇️  下载: {url}")
        print(f"   → {local_path}")
        
        for attempt in range(retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                
                # 确保目录存在
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                # 写入文件
                with open(full_path, 'wb') as f:
                    f.write(response.content)
                
                file_size = len(response.content)
                print(f"✅ 下载成功: {local_path} ({file_size} bytes)")
                return True
                
            except Exception as e:
                print(f"❌ 下载失败 (尝试 {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(2)  # 等待2秒后重试
        
        print(f"💥 下载彻底失败: {url}")
        return False
    
    def download_google_fonts(self):
        """下载Google Fonts字体文件 - 优化版本，只下载需要的字体"""
        print("\n📚 处理Google Fonts...")
        
        # 清理旧的字体文件
        self.clean_font_files()
        
        # 只下载必要的字体文件，而不是从CSS中提取所有URL
        essential_fonts = [
            # Inter 字体 - 只下载常用权重
            ("https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2", "fonts/inter-regular.woff2"),
            ("https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7SUc.woff2", "fonts/inter-medium.woff2"),
            # Noto Sans SC 字体 - 只下载基础中文字体包
            ("https://fonts.gstatic.com/s/notosanssc/v38/k3kXo84MPvpLmixcA63oeALhLOCT-xWNm8Hqd37g1OkDRZe7lR4sg1IzSy-MNbE9VH8V.22.woff2", "fonts/noto-sans-sc-regular.woff2"),
            ("https://fonts.gstatic.com/s/notosanssc/v38/k3kXo84MPvpLmixcA63oeALhLOCT-xWNm8Hqd37g1OkDRZe7lR4sg1IzSy-MNbE9VH8V.23.woff2", "fonts/noto-sans-sc-medium.woff2"),
        ]
        
        # 下载字体文件
        for font_url, local_path in essential_fonts:
            self.download_file(font_url, local_path)
        
        # 创建简化的CSS文件
        css_content = """/* Google Fonts 本地回退 */
@font-face {
    font-family: 'Inter-Fallback';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/inter-regular.woff2') format('woff2');
}

@font-face {
    font-family: 'Inter-Fallback';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../fonts/inter-medium.woff2') format('woff2');
}

@font-face {
    font-family: 'Noto-Sans-SC-Fallback';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../fonts/noto-sans-sc-regular.woff2') format('woff2');
}

@font-face {
    font-family: 'Noto-Sans-SC-Fallback';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('../fonts/noto-sans-sc-medium.woff2') format('woff2');
}
"""
        
        # 保存CSS文件
        css_path = os.path.join(self.static_dir, "css", "google-fonts.css")
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        print(f"✅ Google Fonts CSS保存: css/google-fonts.css")
    
    def clean_font_files(self):
        """清理多余的字体文件"""
        fonts_dir = os.path.join(self.static_dir, "fonts")
        if not os.path.exists(fonts_dir):
            return
        
        print("🧹 清理多余的字体文件...")
        
        # 要保留的字体文件模式
        keep_patterns = [
            "fa-solid-900",
            "fa-regular-400", 
            "fa-brands-400",
            "inter-regular",
            "inter-medium",
            "noto-sans-sc-regular",
            "noto-sans-sc-medium"
        ]
        
        cleaned_count = 0
        for filename in os.listdir(fonts_dir):
            file_path = os.path.join(fonts_dir, filename)
            if os.path.isfile(file_path):
                # 检查是否是要保留的文件
                should_keep = any(pattern in filename for pattern in keep_patterns)
                if not should_keep:
                    try:
                        os.remove(file_path)
                        print(f"🗑️  删除多余文件: {filename}")
                        cleaned_count += 1
                    except Exception as e:
                        print(f"❌ 删除文件失败 {filename}: {e}")
        
        if cleaned_count > 0:
            print(f"✅ 清理完成，删除了 {cleaned_count} 个多余文件")
        else:
            print("✅ 无需清理，字体文件已优化")
    
    def fix_fontawesome_css(self):
        """修复FontAwesome CSS中的字体路径"""
        css_path = os.path.join(self.static_dir, "css", "all.min.css")
        
        if not os.path.exists(css_path):
            print("⚠️  FontAwesome CSS文件不存在，跳过路径修复")
            return
        
        print("🔧 修复FontAwesome CSS中的字体路径...")
        
        try:
            with open(css_path, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            # 替换字体路径
            css_content = re.sub(
                r'\.\.\/webfonts\/',
                '../fonts/',
                css_content
            )
            
            with open(css_path, 'w', encoding='utf-8') as f:
                f.write(css_content)
            
            print("✅ FontAwesome CSS路径修复完成")
            
        except Exception as e:
            print(f"❌ FontAwesome CSS路径修复失败: {e}")
    
    def download_all(self):
        """下载所有资源"""
        print("🚀 开始下载所有静态资源...\n")
        
        successful = 0
        failed = 0
        
        for url, local_path in self.resources:
            if "google" in url.lower():
                continue  # Google Fonts单独处理
            
            if self.download_file(url, local_path):
                successful += 1
            else:
                failed += 1
            
            time.sleep(0.5)  # 避免请求过于频繁
        
        # 处理Google Fonts
        self.download_google_fonts()
        
        # 修复FontAwesome CSS路径
        self.fix_fontawesome_css()
        
        print(f"\n📊 下载完成统计:")
        print(f"✅ 成功: {successful}")
        print(f"❌ 失败: {failed}")
        print(f"📁 静态文件目录: {os.path.abspath(self.static_dir)}")
    
    def check_files(self):
        """检查所有文件是否存在"""
        print("🔍 检查静态文件状态...\n")
        
        missing_files = []
        existing_files = []
        
        for url, local_path in self.resources:
            full_path = os.path.join(self.static_dir, local_path)
            if os.path.exists(full_path) and os.path.getsize(full_path) > 0:
                file_size = os.path.getsize(full_path)
                existing_files.append((local_path, file_size))
                print(f"✅ {local_path} ({file_size} bytes)")
            else:
                missing_files.append((url, local_path))
                print(f"❌ {local_path} - 缺失")
        
        print(f"\n📊 文件状态统计:")
        print(f"✅ 存在: {len(existing_files)}")
        print(f"❌ 缺失: {len(missing_files)}")
        
        return missing_files

def main():
    downloader = StaticFilesDownloader()
    
    print("=" * 60)
    print("           静态资源下载器")
    print("=" * 60)
    
    # 显示菜单
    print("\n请选择操作:")
    print("1. 检查文件状态")
    print("2. 下载缺失文件")
    print("3. 重新下载所有文件")
    print("4. 清理多余文件")
    print("5. 完整优化（清理+下载）")
    
    choice = input("\n请输入选项 (1-5): ").strip()
    
    if choice == "1":
        downloader.check_files()
    elif choice == "2":
        missing_files = downloader.check_files()
        if missing_files:
            print(f"\n发现 {len(missing_files)} 个缺失文件")
            confirm = input("是否下载缺失的文件? (y/n): ").lower().strip()
            if confirm in ['y', 'yes']:
                downloader.download_all()
        else:
            print("\n🎉 所有文件都已存在!")
    elif choice == "3":
        confirm = input("确定要重新下载所有文件? (y/n): ").lower().strip()
        if confirm in ['y', 'yes']:
            downloader.download_all()
    elif choice == "4":
        downloader.clean_font_files()
        print("✅ 清理完成")
    elif choice == "5":
        print("🚀 开始完整优化...")
        downloader.clean_font_files()
        downloader.download_all()
        print("✅ 优化完成")
    else:
        print("无效选项")

if __name__ == "__main__":
    main()
