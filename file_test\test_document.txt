这是一个测试文档，用于测试文件上传功能。

文档内容包括：

1. 基本信息
   - 文档名称：测试文档
   - 创建时间：2025年8月11日
   - 用途：测试Responses API的文件上传功能

2. 技术规格
   - 文件格式：纯文本 (.txt)
   - 编码：UTF-8
   - 大小：约1KB

3. 测试场景
   - 文件上传到服务器
   - 通过Responses API引用文件
   - 模型分析文件内容
   - 返回基于文件内容的回答

4. 预期结果
   - 文件成功上传
   - 模型能够读取并理解文件内容
   - 基于文件内容生成相关回答

5. 示例问题
   - 这个文档的主要内容是什么？
   - 文档中提到了哪些技术规格？
   - 这个文档的用途是什么？

6. 附加信息
   - 支持的文件类型：.txt, .md, .pdf, .docx, .json, .csv等
   - 最大文件大小：通常为10MB
   - 处理时间：根据文件大小和复杂度而定

这个文档将用于验证Responses API的文件处理能力，确保系统能够正确处理用户上传的文件并基于文件内容进行智能回答。
