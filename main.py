import os
import time
import logging
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles

# 导入模块化的组件
from config import load_config, save_config, get_timestamp
from stats import load_stats, save_stats, get_stats, increment_total_requests, increment_successful_requests, increment_failed_requests
from auth import create_auth_dependency
from api_proxy import review_content, proxy_openai_request
from models import ChatCompletionRequest
from routes.admin import create_admin_routes
from routes.client import create_client_routes
from cloud_routes import cloud_router
from prompt_manager import PromptManager, CharacterManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 全局配置对象 - 立即加载配置
config = load_config()

def set_log_level(level: str = "INFO"):
    """设置日志级别"""
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    log_level = level_map.get(level.upper(), logging.INFO)
    logging.getLogger().setLevel(log_level)
    logger.info(f"日志级别已设置为: {level}")

def get_config():
    """获取当前配置"""
    global config
    # 重新加载配置确保获取最新数据
    config = load_config()
    return config

# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("🚀 正在启动AI代理服务...")
    
    # 配置已经在模块级别加载，这里只需要加载统计数据
    load_stats()
    
    # 初始化系统提示词tokens缓存
    logger.info("🔄 正在初始化系统提示词tokens缓存...")
    try:
        from utils import update_all_prompt_tokens_cache
        update_all_prompt_tokens_cache(config)
        logger.info("✅ 系统提示词tokens缓存初始化完成")
    except Exception as e:
        logger.warning(f"⚠️ 系统提示词tokens缓存初始化失败: {e}")
    
    # 设置默认日志级别
    log_level = config.get('debug_config', {}).get('log_level', 'INFO')
    set_log_level(log_level)
    
    logger.info(f"✅ 服务启动完成 - 监听端口: 7766")
    logger.info(f"📊 当前统计: {get_stats()}")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 正在关闭服务...")
    save_stats()
    logger.info("✅ 服务关闭完成")

# 创建应用
app = FastAPI(
    title="灵星逸 AI代理服务", 
    description="OpenAI API代理服务 - 模块化架构", 
    version="4.2.0",
    lifespan=lifespan
)

# CORS设置 - 更宽松的设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# 静态文件服务
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static", html=True), name="static")

# 创建认证依赖
verify_api_key, verify_client_token, verify_admin_token = create_auth_dependency(get_config)

# 创建管理器
prompt_manager = PromptManager(get_config)
character_manager = CharacterManager(get_config)

# 创建路由
admin_router = create_admin_routes(get_config, verify_admin_token)
client_router = create_client_routes(get_config, verify_client_token, review_content, proxy_openai_request)

# 注册路由
app.include_router(admin_router)
app.include_router(client_router)
app.include_router(cloud_router)

# 基础路由
@app.get("/")
async def root():
    """根路径重定向到index.html"""
    if os.path.exists("index.html"):
        return FileResponse("index.html")
    return {
        "message": "灵星逸 AI代理服务正在运行", 
        "status": "online",
        "version": "2.0.0",
        "architecture": "模块化"
    }

@app.get("/index")
async def get_index_redirect():
    """重定向到index.html"""
    if os.path.exists("index.html"):
        return FileResponse("index.html")
    return {"message": "index.html文件不存在"}

@app.get("/index.html")
async def get_index():
    if os.path.exists("index.html"):
        return FileResponse("index.html")
    return {"message": "index.html文件不存在"}

@app.get("/admin")
async def get_admin_redirect():
    """重定向到admin.html"""
    if os.path.exists("admin.html"):
        return FileResponse("admin.html")
    return {"message": "admin.html文件不存在"}

@app.get("/admin.html") 
async def get_admin():
    if os.path.exists("admin.html"):
        return FileResponse("admin.html")
    return {"message": "admin.html文件不存在"}

@app.get("/chat")
async def get_chat_page():
    """返回聊天页面"""
    if os.path.exists("chat.html"):
        return FileResponse("chat.html")
    return {"message": "chat.html文件不存在"}

@app.get("/chat.html")
async def get_chat_html():
    """返回聊天页面HTML"""
    if os.path.exists("chat.html"):
        return FileResponse("chat.html")
    return {"message": "chat.html文件不存在"}

@app.get("/demo")
async def get_demo_page():
    """返回角色演示页面"""
    if os.path.exists("roles_demo.html"):
        return FileResponse("roles_demo.html")
    return {"message": "roles_demo.html文件不存在"}

@app.get("/roles_demo")
async def get_roles_demo_redirect():
    """重定向到角色演示页面"""
    if os.path.exists("roles_demo.html"):
        return FileResponse("roles_demo.html")
    return {"message": "roles_demo.html文件不存在"}

@app.get("/roles_demo.html")
async def get_roles_demo_html():
    """返回角色演示页面HTML"""
    if os.path.exists("roles_demo.html"):
        return FileResponse("roles_demo.html")
    return {"message": "roles_demo.html文件不存在"}

@app.options("/{path:path}")
async def options_handler(path: str):
    """处理所有OPTIONS请求"""
    return Response(
        content="",
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Expose-Headers": "*",
            "Access-Control-Allow-Credentials": "true"
        }
    )

@app.get("/api/status")
async def api_status():
    """API状态检查端点"""
    stats = get_stats()
    return {
        "status": "online",
        "service": "灵星逸 AI 代理服务",
        "version": "2.0.0",
        "architecture": "模块化",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime": stats.get('start_time'),
        "config_loaded": bool(config),
        "modules": {
            "config": "✅ 已加载",
            "stats": "✅ 已加载", 
            "auth": "✅ 已加载",
            "image_manager": "✅ 已加载",
            "api_proxy": "✅ 已加载",
            "routes": "✅ 已加载"
        },
        "stats": {
            "total_requests": stats.get('total_requests', 0),
            "successful_requests": stats.get('successful_requests', 0),
            "failed_requests": stats.get('failed_requests', 0),
            "review_blocked": stats.get('review_blocked', 0)
        }
    }

# OpenAI兼容接口
@app.get("/v1/models")
async def list_models():
    """获取模型列表（实际返回角色列表）"""
    try:
        # 获取活跃角色列表
        active_characters = character_manager.get_active_characters()
        
        logger.info(f"获取到活跃角色数量: {len(active_characters)}")
        
        models = []
        for character in active_characters:
            # 使用model_id作为用户面向的标识符
            model_id = character.get("model_id", character["id"])
            model_data = {
                "id": model_id,
                "object": "model",
                "created": int(time.time()),
                "owned_by": "killerbest",
                "permission": [],
                "root": model_id,
                "parent": None,
                "display_name": character.get("display_name", character["name"]),
                "description": character.get("description", ""),
                "avatar_url": character.get("avatar_url", ""),
                "category": character.get("category", ""),
                "tags": character.get("tags", []),
                "is_default": character.get("is_default", False)
            }
            models.append(model_data)
            logger.info(f"添加角色到模型列表: {model_id} (内部ID: {character['id']}) - {character['name']}")
        
        logger.info(f"返回模型列表，共 {len(models)} 个角色")
        return {
            "object": "list",
            "data": models
        }
    except Exception as e:
        logger.error(f"获取角色列表失败: {e}", exc_info=True)
        # 返回空列表而不是降级到model_config
        return {
            "object": "list",
            "data": []
        }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest, authorized: bool = verify_api_key):
    """OpenAI兼容的聊天完成接口"""
    increment_total_requests()
    
    try:
        # 获取角色配置
        character = character_manager.get_character(request.model)
        if not character:
            # 如果角色不存在，尝试使用默认角色
            character = character_manager.get_default_character()
            if not character:
                raise HTTPException(status_code=400, detail=f"角色 '{request.model}' 不存在且没有默认角色")
        
        # 获取角色对应的提示词
        system_prompt_id = character.get('system_prompt_id')
        if system_prompt_id:
            prompt_data = prompt_manager.get_prompt(system_prompt_id)
            if prompt_data:
                # 添加或替换系统提示词（包括空内容的情况）
                system_message = {"role": "system", "content": prompt_data.get('content', '')}
                
                # 检查是否已有系统消息，如果有就替换，否则插入到开头
                has_system = False
                for i, msg in enumerate(request.messages):
                    if msg.role == "system":
                        request.messages[i] = system_message
                        has_system = True
                        break
                
                if not has_system:
                    request.messages.insert(0, system_message)
        
        # 更新请求的模型为后端模型
        original_model = request.model
        request.model = character.get('backend_model', request.model)
        
        logger.info(f"角色 '{character['name']}' 使用后端模型: {request.model}")
        # 意图审查
        if config.get('review_enabled', False):
            review_result = await review_content(request.messages, config)
            if review_result.strip() != "可":
                from stats import increment_review_blocked
                increment_review_blocked()
                save_stats()
                
                # 返回正常的聊天响应格式，告知用户审查不通过
                response_content = f"抱歉，我检测到您的请求可能涉及不当内容。请换个话题聊聊吧！😊"
                
                if request.stream:
                    # 流式响应格式
                    import uuid
                    import json
                    async def blocked_stream():
                        # 模拟OpenAI流式响应格式
                        chunk_data = {
                            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                            "object": "chat.completion.chunk",
                            "created": int(time.time()),
                            "model": original_model,
                            "choices": [{
                                "index": 0,
                                "delta": {"content": response_content},
                                "finish_reason": None
                            }]
                        }
                        yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n".encode('utf-8')
                        
                        # 结束标记
                        end_chunk = {
                            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                            "object": "chat.completion.chunk", 
                            "created": int(time.time()),
                            "model": original_model,
                            "choices": [{
                                "index": 0,
                                "delta": {},
                                "finish_reason": "stop"
                            }]
                        }
                        yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n".encode('utf-8')
                        yield "data: [DONE]\n\n".encode('utf-8')
                    
                    from fastapi.responses import StreamingResponse
                    return StreamingResponse(
                        blocked_stream(),
                        media_type="text/event-stream",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Headers": "*",
                            "Access-Control-Allow-Methods": "*",
                            "Access-Control-Expose-Headers": "*"
                        }
                    )
                else:
                    # 非流式响应格式
                    import uuid
                    return {
                        "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                        "object": "chat.completion",
                        "created": int(time.time()),
                        "model": original_model,
                        "choices": [{
                            "index": 0,
                            "message": {
                                "role": "assistant",
                                "content": response_content
                            },
                            "finish_reason": "stop"
                        }],
                        "usage": {
                            "prompt_tokens": 10,
                            "completion_tokens": 20,
                            "total_tokens": 30
                        }
                    }
        
        # 代理请求到主API
        result = await proxy_openai_request(request, config, original_model_id=original_model)
        
        # 注意：这里不能直接统计成功，需要在具体成功返回时统计
        # 因为proxy_openai_request可能返回流式响应或普通响应
        if request.stream:
            # 流式响应的成功统计在proxy_openai_request内部处理
            return result
        else:
            # 非流式响应在这里统计
            increment_successful_requests()
            save_stats()
            return result
        
    except HTTPException as e:
        increment_failed_requests()
        save_stats()
        logger.error(f"聊天完成接口HTTPException: {e.detail}")
        raise e
    except Exception as e:
        increment_failed_requests()
        save_stats()
        logger.error(f"聊天完成接口异常: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

# 调试和管理接口
@app.post("/debug/reload-config")
async def reload_config():
    """重新加载配置文件（调试用）"""
    global config
    try:
        config = load_config()
        logger.info("配置文件已重新加载")
        return {"success": True, "message": "配置文件已重新加载"}
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}")
        return {"success": False, "error": str(e)}

@app.post("/debug/set-log-level/{level}")
async def set_debug_log_level(level: str):
    """设置日志级别（调试用）"""
    try:
        set_log_level(level)
        return {"success": True, "message": f"日志级别已设置为: {level}"}
    except Exception as e:
        logger.error(f"设置日志级别失败: {e}")
        return {"success": False, "error": str(e)}

@app.get("/debug/memory-info")
async def get_memory_info():
    """获取内存使用情况（调试用）"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "rss": memory_info.rss / 1024 / 1024,  # MB
            "vms": memory_info.vms / 1024 / 1024,  # MB
            "percent": process.memory_percent(),
            "cpu_percent": process.cpu_percent()
        }
    except ImportError:
        return {"error": "psutil not installed"}
    except Exception as e:
        logger.error(f"获取内存信息失败: {e}")
        return {"error": str(e)}

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"全局异常 - URL: {request.url} - 异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"服务器内部错误: {str(exc)}"},
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "*"
        }
    )

if __name__ == "__main__":
    import uvicorn
    logger.info(f"[{get_timestamp()}] 🚀 正在启动灵星逸 AI代理服务 v4.2.0")
    logger.info(f"[{get_timestamp()}] 📁 架构: 模块化设计")
    logger.info(f"[{get_timestamp()}] 🌐 监听地址: http://127.0.0.1:7766")
    
    # 开发环境配置
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=7766,
        reload=True,
        log_level="info"
    )