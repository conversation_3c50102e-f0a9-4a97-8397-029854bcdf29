<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>灵星逸 - AI伙伴</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⭐</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            color: #ffffff;
            position: relative;
        }

        /* 增强版粒子背景 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .particle-star {
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.8);
            animation: twinkle 3s ease-in-out infinite;
        }

        .particle-dot {
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.6);
            animation: float-random 8s ease-in-out infinite;
        }

        .particle-large {
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.3) 100%);
            animation: pulse-float 6s ease-in-out infinite;
        }

        .particle-shooting {
            width: 2px;
            height: 12px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
            border-radius: 50%;
            animation: shooting-star 4s linear infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        @keyframes float-random {
            0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
            25% { transform: translateY(-15px) translateX(10px) rotate(90deg); }
            50% { transform: translateY(-30px) translateX(-5px) rotate(180deg); }
            75% { transform: translateY(-10px) translateX(-15px) rotate(270deg); }
        }

        @keyframes pulse-float {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
            33% { transform: translateY(-20px) scale(1.1); opacity: 1; }
            66% { transform: translateY(-10px) scale(0.9); opacity: 0.8; }
        }

        @keyframes shooting-star {
            0% { transform: translateX(-100px) translateY(-100px) rotate(45deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(100vw) translateY(100vh) rotate(45deg); opacity: 0; }
        }

        /* 魔法轨迹效果 */
        .magic-trail {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(168, 237, 234, 0.8);
            border-radius: 50%;
            animation: magic-trail 12s linear infinite;
        }

        @keyframes magic-trail {
            0% { transform: translateX(0) translateY(0); opacity: 0; }
            5% { opacity: 1; }
            95% { opacity: 1; }
            100% { transform: translateX(100vw) translateY(50vh); opacity: 0; }
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
            z-index: 1000;
            animation: slideDown 0.8s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #ffffff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3)); }
            50% { filter: drop-shadow(0 0 15px rgba(168, 237, 234, 0.6)); }
        }

        .admin-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            color: white;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .admin-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .admin-btn:hover::before {
            left: 100%;
        }

        .admin-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* 主要内容区域 */
        .main-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 100px 20px 50px;
            position: relative;
            z-index: 10;
        }

        /* 卡片容器 - 增大尺寸 */
        .card-container {
            position: relative;
            width: 500px;
            height: 750px;
            perspective: 1000px;
            animation: cardEntrance 1.2s ease-out;
        }

        @keyframes cardEntrance {
            from { transform: scale(0.8) rotateY(90deg); opacity: 0; }
            to { transform: scale(1) rotateY(0deg); opacity: 1; }
        }

        /* 3D卡片 */
        .character-card {
            position: relative;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
            transform-style: preserve-3d;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .character-card:hover {
            transform: rotateY(5deg) rotateX(3deg) scale(1.02);
            box-shadow: 0 35px 70px rgba(0, 0, 0, 0.4);
        }

        /* 螺旋装饰 - 增强效果 */
        .spiral-bg {
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent, rgba(168, 237, 234, 0.1), transparent);
            border-radius: 50%;
            animation: spiral-rotate 15s linear infinite;
            pointer-events: none;
        }

        .spiral-bg::after {
            content: '';
            position: absolute;
            top: 25%;
            left: 25%;
            width: 50%;
            height: 50%;
            background: conic-gradient(from 180deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            border-radius: 50%;
            animation: spiral-rotate 10s linear infinite reverse;
        }

        @keyframes spiral-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 角色头像 - 使用真实图片 */
        .character-avatar {
            position: relative;
            width: 220px;
            height: 220px;
            margin: 40px auto 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 4px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            animation: avatarFloat 4s ease-in-out infinite;
        }

        @keyframes avatarFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .character-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .character-avatar:hover img {
            transform: scale(1.1);
            filter: brightness(1.1);
        }

        .character-avatar::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #667eea, #764ba2, #a8edea, #fed6e3, #667eea);
            animation: avatar-border-rotate 3s linear infinite;
            z-index: -1;
        }

        @keyframes avatar-border-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 角色信息 */
        .character-info {
            text-align: center;
            padding: 0 40px;
            position: relative;
            z-index: 2;
        }

        .character-name {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffffff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: nameShine 2s ease-in-out infinite;
        }

        @keyframes nameShine {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3)); }
            50% { filter: drop-shadow(0 0 15px rgba(168, 237, 234, 0.6)); }
        }

        .character-title {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 25px;
            font-weight: 400;
        }

        .character-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .character-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 25px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 18px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.05);
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-size: 22px;
            font-weight: 600;
            color: #ffffff;
        }

        /* 交互按钮 */
        .interaction-btn {
            width: calc(100% - 80px);
            padding: 18px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: block;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .interaction-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .interaction-btn:hover::before {
            left: 100%;
        }

        .interaction-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .interaction-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        /* 浮动装饰元素 */
        .floating-element {
            position: fixed;
            pointer-events: none;
            font-size: 20px;
            z-index: 5;
        }

        .floating-element:nth-child(1) {
            top: 15%;
            left: 10%;
            animation: float-diagonal 12s ease-in-out infinite;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 25%;
            right: 15%;
            animation: float-spiral 10s ease-in-out infinite;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 35%;
            left: 20%;
            animation: float-bounce 8s ease-in-out infinite;
            animation-delay: 4s;
        }

        .floating-element:nth-child(4) {
            bottom: 15%;
            right: 10%;
            animation: float-wave 14s ease-in-out infinite;
            animation-delay: 6s;
        }

        .floating-element:nth-child(5) {
            top: 50%;
            left: 5%;
            animation: float-zigzag 16s ease-in-out infinite;
            animation-delay: 8s;
        }

        .floating-element:nth-child(6) {
            top: 70%;
            right: 25%;
            animation: float-circle 18s ease-in-out infinite;
            animation-delay: 10s;
        }

        @keyframes float-diagonal {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(15px, -20px) rotate(90deg); }
            50% { transform: translate(30px, -10px) rotate(180deg); }
            75% { transform: translate(15px, 10px) rotate(270deg); }
        }

        @keyframes float-spiral {
            0% { transform: translate(0, 0) rotate(0deg) scale(1); }
            25% { transform: translate(20px, -15px) rotate(90deg) scale(1.2); }
            50% { transform: translate(0, -30px) rotate(180deg) scale(0.8); }
            75% { transform: translate(-20px, -15px) rotate(270deg) scale(1.1); }
            100% { transform: translate(0, 0) rotate(360deg) scale(1); }
        }

        @keyframes float-bounce {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-25px) scale(1.15); }
        }

        @keyframes float-wave {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); }
            25% { transform: translateX(10px) translateY(-15px) rotate(15deg); }
            50% { transform: translateX(0px) translateY(-30px) rotate(0deg); }
            75% { transform: translateX(-10px) translateY(-15px) rotate(-15deg); }
        }

        @keyframes float-zigzag {
            0%, 100% { transform: translate(0, 0); }
            20% { transform: translate(15px, -10px); }
            40% { transform: translate(-10px, -20px); }
            60% { transform: translate(20px, -30px); }
            80% { transform: translate(-15px, -15px); }
        }

        @keyframes float-circle {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(15px, 0) rotate(90deg); }
            50% { transform: translate(15px, 15px) rotate(180deg); }
            75% { transform: translate(0, 15px) rotate(270deg); }
            100% { transform: translate(0, 0) rotate(360deg); }
        }

        /* ============ 优化的弹窗样式 ============ */
        .custom-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(15px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 20px;
        }

        .custom-modal.show {
            display: flex;
        }

        @keyframes modalFadeIn {
            from { 
                opacity: 0; 
                backdrop-filter: blur(0px);
            }
            to { 
                opacity: 1; 
                backdrop-filter: blur(15px);
            }
        }

        @keyframes modalFadeOut {
            from { 
                opacity: 1; 
                backdrop-filter: blur(15px);
            }
            to { 
                opacity: 0; 
                backdrop-filter: blur(0px);
            }
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 24px;
            padding: 0;
            max-width: 700px;
            width: 100%;
            max-height: 90vh;
            position: relative;
            animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 8px 16px rgba(255, 255, 255, 0.1) inset;
            overflow: hidden;
        }

        .modal-content.closing {
            animation: modalSlideOut 0.3s cubic-bezier(0.4, 0, 1, 1);
        }

        @keyframes modalSlideIn {
            from { 
                transform: scale(0.8) translateY(-40px) rotateX(10deg); 
                opacity: 0; 
            }
            to { 
                transform: scale(1) translateY(0) rotateX(0deg); 
                opacity: 1; 
            }
        }

        @keyframes modalSlideOut {
            from { 
                transform: scale(1) translateY(0) rotateX(0deg); 
                opacity: 1; 
            }
            to { 
                transform: scale(0.9) translateY(-20px) rotateX(-5deg); 
                opacity: 0; 
            }
        }

        /* 弹窗头部 */
        .modal-header {
            padding: 30px 30px 20px;
            text-align: center;
            position: relative;
            background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-title {
            font-size: 28px;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #ffffff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
            letter-spacing: 0.5px;
        }

        .modal-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 8px;
            font-weight: 400;
        }

        /* 弹窗内容区域 */
        .modal-body {
            padding: 25px 30px;
            max-height: 60vh;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.3) transparent;
        }

        .modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        .modal-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 1.6;
            margin: 0;
        }

        /* API指南特殊样式 */
        .api-guide-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .api-section {
            margin-bottom: 25px;
        }

        .api-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .api-code-block {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
            font-family: 'Monaco', 'Consolas', 'Liberation Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #e8e8e8;
            position: relative;
            overflow-x: auto;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .api-code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(168, 237, 234, 0.5), transparent);
        }

        .api-highlight {
            color: #a8edea;
            font-weight: 500;
        }

        .api-endpoints {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
        }

        .api-endpoint {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #a8edea;
        }

        .api-method {
            font-weight: 600;
            color: #a8edea;
            margin-right: 12px;
            min-width: 45px;
        }

        .api-contact {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(168, 237, 234, 0.1));
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        /* 弹窗底部按钮区域 */
        .modal-footer {
            padding: 20px 30px 30px;
            text-align: center;
            background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-btn {
            padding: 14px 28px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 0 8px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            min-width: 120px;
        }

        .modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .modal-btn:hover::before {
            left: 100%;
        }

        .modal-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
        }

        .modal-btn:active {
            transform: translateY(0) scale(0.98);
            transition: all 0.1s ease;
        }

        .modal-btn.secondary {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
        }

        .modal-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .modal-btn.copy-btn {
            background: linear-gradient(135deg, #a8edea, #667eea);
            position: relative;
        }

        .modal-btn.copy-btn.copied {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            transform: scale(1.05);
        }

        /* 关闭按钮优化 */
        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            z-index: 10001; /* 确保在最顶层 */
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: rotate(90deg) scale(1.1);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .modal-close:active {
            transform: rotate(90deg) scale(0.95);
        }

        /* 添加按钮容器样式 */
        .interaction-buttons {
            display: flex;
            gap: 12px;
            margin: 25px auto 0;
            width: calc(100% - 80px);
        }

        .interaction-btn.primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .interaction-btn.secondary {
            background: linear-gradient(45deg, #a8edea, #667eea);
        }

        /* 图片预览模式 - 增强视觉冲击效果 */
        .modal-image {
            width: 100%;
            max-width: 80%;
            max-height: 70vh;
            border-radius: 20px;
            box-shadow: 
                0 25px 80px rgba(0, 0, 0, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset,
                0 10px 30px rgba(255, 255, 255, 0.1) inset;
            margin: 20px auto;
            display: block;
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            cursor: zoom-in;
            object-fit: contain;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .modal-image:hover {
            transform: scale(1.02);
            box-shadow: 
                0 30px 100px rgba(0, 0, 0, 0.7),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset,
                0 15px 40px rgba(255, 255, 255, 0.15) inset;
        }

        /* 全屏图片预览 */
        .fullscreen-image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 20000;
            animation: fullscreenFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 40px;
            cursor: zoom-out;
        }

        .fullscreen-image-modal.show {
            display: flex;
        }

        @keyframes fullscreenFadeIn {
            from { 
                opacity: 0; 
                backdrop-filter: blur(0px);
                transform: scale(0.9);
            }
            to { 
                opacity: 1; 
                backdrop-filter: blur(20px);
                transform: scale(1);
            }
        }

        .fullscreen-image {
            max-width: 90%;
            max-height: 90%;
            border-radius: 15px;
            box-shadow: 
                0 50px 120px rgba(0, 0, 0, 0.8),
                0 0 0 2px rgba(255, 255, 255, 0.1) inset;
            animation: imageZoomIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
            object-fit: contain;
            cursor: default;
        }

        @keyframes imageZoomIn {
            from { 
                transform: scale(0.3) rotate(-10deg); 
                opacity: 0;
                filter: blur(10px);
            }
            50% {
                transform: scale(1.1) rotate(2deg);
                opacity: 0.8;
                filter: blur(2px);
            }
            to { 
                transform: scale(1) rotate(0deg); 
                opacity: 1;
                filter: blur(0px);
            }
        }

        /* 全屏模式关闭按钮 */
        .fullscreen-close {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(15px);
            font-weight: bold;
            z-index: 20001;
        }

        .fullscreen-close:hover {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            transform: rotate(90deg) scale(1.15);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .fullscreen-close:active {
            transform: rotate(90deg) scale(0.95);
        }

        /* 图片信息标签 */
        .image-info {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 24px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: infoSlideUp 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.3s both;
        }

        @keyframes infoSlideUp {
            from { 
                transform: translateX(-50%) translateY(50px); 
                opacity: 0; 
            }
            to { 
                transform: translateX(-50%) translateY(0); 
                opacity: 1; 
            }
        }

        /* 头像点击效果增强 */
        .character-avatar {
            cursor: pointer;
            position: relative;
            overflow: visible;
        }

        .character-avatar::after {
            content: '🔍';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            opacity: 0;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .character-avatar:hover::after {
            opacity: 0.5;
            transform: translate(-50%, -50%) scale(1.1);
        }

        /* 侧边图片悬浮效果增强 */
        .gallery-item {
            position: relative;
            cursor: pointer;
        }

        .gallery-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            border-radius: 15px;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1;
        }

        .gallery-item:hover::before {
            opacity: 1;
            animation: shimmer 1s ease-in-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .fullscreen-image-modal {
                padding: 20px;
            }

            .fullscreen-close {
                top: 20px;
                right: 20px;
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .fullscreen-image {
                max-width: 95%;
                max-height: 85%;
            }

            .image-info {
                bottom: 20px;
                font-size: 12px;
                padding: 10px 20px;
            }

            .modal-image {
                max-width: 90%;
                max-height: 60vh;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                max-width: 95%;
                border-radius: 16px;
            }

            .modal-title {
                font-size: 20px;
            }

            .modal-body {
                padding: 15px;
            }

            .modal-btn {
                width: 100%;
                margin: 5px 0;
            }

            .api-section-title {
                font-size: 14px;
            }

            .api-code-block {
                font-size: 11px;
                padding: 10px;
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* 额外的动画效果 */
        .glow-effect {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 25px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            animation: glowSweep 4s ease-in-out infinite;
        }

        @keyframes glowSweep {
            0%, 90%, 100% { opacity: 0; transform: translateX(-100%); }
            10%, 80% { opacity: 1; transform: translateX(100%); }
        }

        /* 侧边滚动图片 */
        .side-gallery {
            position: fixed;
            top: 0;
            left: 0;
            width: 120px;
            height: 100vh;
            overflow: hidden;
            z-index: 999;
            pointer-events: auto;
        }

        .side-gallery-right {
            left: auto;
            right: 0;
        }

        .gallery-track {
            display: flex;
            flex-direction: column;
            animation: scrollUp 60s linear infinite; 
            gap: 20px;
        }

        .gallery-track-reverse {
            animation: scrollDown 75s linear infinite; 
        }

        @keyframes scrollUp {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
        }

        @keyframes scrollDown {
            0% { transform: translateY(-50%); }
            100% { transform: translateY(0); }
        }

        .gallery-item {
            width: 80px;
            height: 80px;
            border-radius: 15px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            margin: 0 auto;
            cursor: pointer;
            z-index: 1000;
            pointer-events: auto;
        }

        .gallery-item:hover {
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            pointer-events: auto;
            cursor: pointer;
            position: relative;
             z-index: 1001;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .card-container {
                width: 90%;
                max-width: 400px;
                height: 650px;
            }

            .character-avatar {
                width: 180px;
                height: 180px;
            }

            .character-name {
                font-size: 28px;
            }

            .character-title {
                font-size: 16px;
            }

            .navbar {
                padding: 0 20px;
            }

            .logo {
                font-size: 20px;
            }

            .character-info {
                padding: 0 30px;
            }

            .floating-element {
                font-size: 16px;
            }

            .side-gallery {
                width: 80px;
            }

            .gallery-item {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 480px) {
            .card-container {
                height: 600px;
            }

            .character-avatar {
                width: 150px;
                height: 150px;
            }

            .character-name {
                font-size: 24px;
            }

            .character-stats {
                gap: 10px;
            }

            .stat-item {
                padding: 15px;
            }

            .floating-element {
                display: none; /* 在小屏幕上隐藏浮动元素 */
            }

            .side-gallery {
                display: none; /* 在小屏幕上隐藏侧边图片 */
            }
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 侧边滚动图片 - 左侧 -->
    <div class="side-gallery">
        <div class="gallery-track" id="galleryLeft">
            <!-- 图片会通过JavaScript动态添加 -->
        </div>
    </div>

    <!-- 侧边滚动图片 - 右侧 -->
    <div class="side-gallery side-gallery-right">
        <div class="gallery-track gallery-track-reverse" id="galleryRight">
            <!-- 图片会通过JavaScript动态添加 -->
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="logo">灵星逸 AI</div>
        <a href="admin.html" class="admin-btn">管理员</a>
    </nav>

    <!-- 浮动装饰元素 -->
    <div class="floating-element">⭐</div>
    <div class="floating-element">✨</div>
    <div class="floating-element">🌟</div>
    <div class="floating-element">💫</div>
    <div class="floating-element">🌠</div>
    <div class="floating-element">💎</div>

    <!-- 主要内容 -->
    <div class="main-container">
        <div class="card-container">
            <div class="character-card" id="characterCard">
                <div class="spiral-bg"></div>
                <div class="glow-effect"></div>
                
                <div class="character-avatar">
                    <img src="https://imgbed.killerbest.com/file/1749487165619_image.png" alt="灵星逸" loading="lazy">
                </div>

                <div class="character-info">
                    <h1 class="character-name">灵星逸</h1>
                    <p class="character-title">智慧与温度并存的AI伙伴</p>
                    <p class="character-description">充满洞察力的AI助手，激发创造热情与自信心，与你共同成长，分享喜悦</p>

                    <div class="character-stats">
                        <div class="stat-item">
                            <div class="stat-label">智慧指数</div>
                            <div class="stat-value">95</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">温度指数</div>
                            <div class="stat-value">98</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">洞察力</div>
                            <div class="stat-value">92</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">创造力</div>
                            <div class="stat-value">89</div>
                        </div>
                    </div>

                    <div class="interaction-buttons">
                        <button class="interaction-btn primary" onclick="startChat()">
                            💬 开始聊天
                        </button>
                        <button class="interaction-btn secondary" onclick="showApiGuide()">
                            🔧 API 指南
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 优化的弹窗 -->
    <div class="custom-modal" id="customModal">
        <div class="modal-content" id="modalContent">
            <button class="modal-close" onclick="closeModal()" aria-label="关闭" type="button">
                ×
            </button>
            
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">标题</h2>
                <p class="modal-subtitle" id="modalSubtitle"></p>
            </div>
            
            <div class="modal-body">
                <div class="modal-text" id="modalText">内容</div>
            </div>
            
            <div class="modal-footer" id="modalFooter">
                <!-- 按钮会动态添加 -->
            </div>
        </div>
    </div>

    <script>
        // 创建侧边滚动图片
        function createSideGallery() {
            const images = [
                'https://imgbed.killerbest.com/file/1749487616455_image.png',
                'https://imgbed.killerbest.com/file/1749487641750_image.png',
                'https://imgbed.killerbest.com/file/1749487669648_image.png',
                'https://imgbed.killerbest.com/file/1749487770532_image.png',
                'https://imgbed.killerbest.com/file/1749487788200_image.png',
                'https://imgbed.killerbest.com/file/1749487797230_image.png',
                'https://imgbed.killerbest.com/file/1749487808585_image.png',
                'https://imgbed.killerbest.com/file/1749487815482_image.png',
                'https://imgbed.killerbest.com/file/1749487834823_image.png'
            ];
            
            // 创建对应的图片名称数组
            const imageNames = [
                '星光闪耀',
                '梦幻花园',
                '月夜精灵',
                '晨曦之光',
                '彩虹之约',
                '魔法森林',
                '水晶之心',
                '天使之翼',
                '银河漫步'
            ];

            const galleryLeft = document.getElementById('galleryLeft');
            const galleryRight = document.getElementById('galleryRight');

            const repeatCount = 6;

            for (let i = 0; i < repeatCount; i++) {
                images.forEach((imageSrc, index) => {
                    const imageName = imageNames[index]; // 使用自定义名称
                    
                    // 左侧图片
                    const itemLeft = document.createElement('div');
                    itemLeft.className = 'gallery-item';
                    itemLeft.innerHTML = `<img src="${imageSrc}" alt="${imageName}" loading="lazy">`;
                    
                    // 鼠标事件处理
                    itemLeft.addEventListener('mouseenter', (e) => {
                        e.stopPropagation();
                        galleryLeft.style.animationPlayState = 'paused';
                    });
                    
                    itemLeft.addEventListener('mouseleave', (e) => {
                        e.stopPropagation();
                        galleryLeft.style.animationPlayState = 'running';
                    });
                    
                    // 点击事件 - 使用自定义名称
                    itemLeft.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        showImageModal(imageSrc, imageName);
                        
                        // 点击反馈
                        itemLeft.style.transform = 'scale(0.9)';
                        setTimeout(() => {
                            itemLeft.style.transform = '';
                        }, 150);
                    });
                    
                    galleryLeft.appendChild(itemLeft);

                    // 右侧图片
                    const itemRight = document.createElement('div');
                    itemRight.className = 'gallery-item';
                    itemRight.innerHTML = `<img src="${imageSrc}" alt="${imageName}" loading="lazy">`;
                    
                    // 鼠标事件处理
                    itemRight.addEventListener('mouseenter', (e) => {
                        e.stopPropagation();
                        galleryRight.style.animationPlayState = 'paused';
                    });
                    
                    itemRight.addEventListener('mouseleave', (e) => {
                        e.stopPropagation();
                        galleryRight.style.animationPlayState = 'running';
                    });
                    
                    // 点击事件 - 使用自定义名称
                    itemRight.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        showImageModal(imageSrc, imageName);
                        
                        // 点击反馈
                        itemRight.style.transform = 'scale(0.9)';
                        setTimeout(() => {
                            itemRight.style.transform = '';
                        }, 150);
                    });
                    
                    galleryRight.appendChild(itemRight);
                });
            }
        }
        
        // 添加图片退出动画CSS
        function addImageAnimations() {
            const style = document.createElement('style');
            style.textContent = `
                @keyframes imageZoomOut {
                    from { 
                        transform: scale(1) rotate(0deg); 
                        opacity: 1;
                        filter: blur(0px);
                    }
                    to { 
                        transform: scale(0.3) rotate(10deg); 
                        opacity: 0;
                        filter: blur(10px);
                    }
                }

                @keyframes fullscreenFadeOut {
                    from { 
                        opacity: 1; 
                        backdrop-filter: blur(20px);
                        transform: scale(1);
                    }
                    to { 
                        opacity: 0; 
                        backdrop-filter: blur(0px);
                        transform: scale(1.1);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // ESC键关闭全屏图片
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const fullscreenModal = document.getElementById('fullscreenModal');
                if (fullscreenModal && fullscreenModal.classList.contains('show')) {
                    closeFullscreenModal();
                } else {
                    const modal = document.getElementById('customModal');
                    if (modal.classList.contains('show')) {
                        closeModal();
                    }
                }
            }
        });

        // 页面加载完成后执行（更新版本）
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            createSideGallery();
            hideLoading();
            init3DEffect();
            initAvatarClick(); // 初始化头像点击
            addImageAnimations(); // 添加动画样式
        });

        // ============ 增强的图片预览功能 ============
        
        // 创建全屏图片模态框
        function createFullscreenModal() {
            const fullscreenModal = document.createElement('div');
            fullscreenModal.className = 'fullscreen-image-modal';
            fullscreenModal.id = 'fullscreenModal';
            fullscreenModal.innerHTML = `
                <button class="fullscreen-close" onclick="closeFullscreenModal()" aria-label="关闭全屏">×</button>
                <img class="fullscreen-image" id="fullscreenImage" alt="">
                <div class="image-info" id="imageInfo"></div>
            `;
            document.body.appendChild(fullscreenModal);

            // 点击背景关闭
            fullscreenModal.addEventListener('click', (e) => {
                if (e.target === fullscreenModal) {
                    closeFullscreenModal();
                }
            });
        }

        // 显示全屏图片
        function showFullscreenImage(imageSrc, title) {
            const fullscreenModal = document.getElementById('fullscreenModal');
            const fullscreenImage = document.getElementById('fullscreenImage');
            const imageInfo = document.getElementById('imageInfo');
            
            if (!fullscreenModal) {
                createFullscreenModal();
                return showFullscreenImage(imageSrc, title);
            }

            fullscreenImage.src = imageSrc;
            fullscreenImage.alt = title;
            imageInfo.textContent = title;
            
            fullscreenModal.classList.add('show');
            document.body.style.overflow = 'hidden';

            // 预加载图片以确保平滑显示
            const img = new Image();
            img.onload = () => {
                fullscreenImage.style.opacity = '1';
            };
            img.src = imageSrc;
        }

        // 关闭全屏图片
        function closeFullscreenModal() {
            const fullscreenModal = document.getElementById('fullscreenModal');
            const fullscreenImage = document.getElementById('fullscreenImage');
            
            if (!fullscreenModal) return; // 添加安全检查
            
            // 添加退出动画
            fullscreenImage.style.animation = 'imageZoomOut 0.4s cubic-bezier(0.4, 0, 1, 1)';
            fullscreenModal.style.animation = 'fullscreenFadeOut 0.4s cubic-bezier(0.4, 0, 1, 1)';
            
            setTimeout(() => {
                fullscreenModal.classList.remove('show');
                fullscreenModal.style.animation = '';
                fullscreenImage.style.animation = '';
                fullscreenImage.style.opacity = '';
                fullscreenImage.src = ''; // 清除图片源，释放内存
                document.body.style.overflow = 'auto';
            }, 400);
        }

        // 图片模态框显示函数
        function showImageModal(imageSrc, title) {
            // 对字符串进行转义，防止特殊字符破坏HTML结构
            const escapedSrc = imageSrc.replace(/'/g, "\\'");
            const escapedTitle = title.replace(/'/g, "\\'");
            
            const subtitle = '点击图片进入全屏预览模式';
            const content = `<img src="${imageSrc}" class="modal-image" alt="${title}" onclick="showFullscreenImage('${escapedSrc}', '${escapedTitle}')">`;
            const buttons = [
                {
                    text: '🔍 全屏预览',
                    className: 'modal-btn',
                    onClick: `showFullscreenImage('${escapedSrc}', '${escapedTitle}')`
                },
                {
                    text: '了解了',
                    className: 'modal-btn secondary',
                    onClick: 'closeModal()'
                }
            ];
            
            showModal(title, content, buttons, subtitle);
        }

        // 头像点击事件处理
        function initAvatarClick() {
            const avatar = document.querySelector('.character-avatar');
            const avatarImg = avatar.querySelector('img');
            
            avatar.addEventListener('click', (e) => {
                e.stopPropagation();
                const imageSrc = avatarImg.src;
                const title = '灵星逸 - AI伙伴';
                showImageModal(imageSrc, title);
                
                // 添加点击反馈效果
                avatar.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    avatar.style.transform = '';
                }, 150);
            });
        }

        // 主要的弹窗显示函数
        function showModal(title, content, buttons = null, subtitle = '', isApiGuide = false) {
            const modal = document.getElementById('customModal');
            const modalContent = document.getElementById('modalContent');
            
            // 清空现有内容
            modalContent.innerHTML = '';
            
            // 创建关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.className = 'modal-close';
            closeBtn.innerHTML = '×';
            closeBtn.setAttribute('onclick', 'closeModal()');
            closeBtn.setAttribute('aria-label', '关闭');
            closeBtn.setAttribute('type', 'button');
            modalContent.appendChild(closeBtn);
            
            // 创建头部
            const header = document.createElement('div');
            header.className = 'modal-header';
            header.innerHTML = `
                <h2 class="modal-title">${title}</h2>
                ${subtitle ? `<p class="modal-subtitle">${subtitle}</p>` : ''}
            `;
            modalContent.appendChild(header);
            
            // 创建内容区域
            const body = document.createElement('div');
            body.className = 'modal-body';
            
            const textDiv = document.createElement('div');
            if (isApiGuide) {
                textDiv.innerHTML = createApiGuideContent();
                textDiv.className = 'modal-text api-guide-content';
            } else {
                textDiv.innerHTML = content;
                textDiv.className = 'modal-text';
            }
            body.appendChild(textDiv);
            modalContent.appendChild(body);
            
            // 创建底部按钮区域
            const footer = document.createElement('div');
            footer.className = 'modal-footer';
            
            if (buttons && buttons.length > 0) {
                buttons.forEach(button => {
                    const btn = document.createElement('button');
                    btn.className = button.className || 'modal-btn';
                    btn.textContent = button.text;
                    btn.setAttribute('onclick', button.onClick);
                    if (button.id) btn.id = button.id;
                    footer.appendChild(btn);
                });
            } else if (isApiGuide) {
                // API指南的默认按钮
                footer.innerHTML = `
                    <button class="modal-btn copy-btn" onclick="copyApiUrl()">📋 复制API地址</button>
                    <button class="modal-btn" onclick="closeModal()">了解了</button>
                `;
            } else {
                // 默认按钮
                footer.innerHTML = `<button class="modal-btn" onclick="closeModal()">了解了</button>`;
            }
            modalContent.appendChild(footer);
            
            // 显示弹窗
            modal.classList.add('show');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // 重置动画
            modalContent.classList.remove('closing');
        }

        // 创建API指南内容
        function createApiGuideContent() {
            return `
                <div class="api-section">
                    <div class="api-section-title">
                        🎯 API 基础地址
                    </div>
                    <div class="api-code-block">https://role.killerbest.cn/v1</div>
                </div>
                
                <div class="api-section">
                    <div class="api-section-title">
                        🔑 认证方式
                    </div>
                    <div class="api-code-block">Authorization: Bearer your-api-key</div>
                </div>
                
                <div class="api-section">
                    <div class="api-section-title">
                        💡 使用示例
                    </div>
                    <div class="api-code-block">curl -X POST "https://role.killerbest.cn/v1/chat/completions" \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "<span class="api-highlight">model</span>": "<span class="api-highlight">astra</span>",
    "<span class="api-highlight">messages</span>": [{"<span class="api-highlight">role</span>": "<span class="api-highlight">user</span>", "<span class="api-highlight">content</span>": "<span class="api-highlight">你好</span>"}]
  }'</div>
                </div>
                
                <div class="api-section">
                    <div class="api-section-title">
                        📚 支持的接口
                    </div>
                    <div class="api-endpoints">
                        <div class="api-endpoint">
                            <span class="api-method">GET</span>
                            <span>/v1/models - 获取模型列表</span>
                        </div>
                        <div class="api-endpoint">
                            <span class="api-method">POST</span>
                            <span>/v1/chat/completions - 聊天完成</span>
                        </div>
                    </div>
                </div>
                
                <div class="api-contact">
                    <div style="color: rgba(255,255,255,0.8); font-size: 14px; line-height: 1.5;">
                        <strong>✉ 需要API密钥？</strong><br>
                        请联系管理员获取访问权限<br>
                        <span style="color: #a8edea;"><EMAIL></span>
                    </div>
                </div>
            `;
        }

        // 优化的关闭弹窗函数
        function closeModal() {
            const modal = document.getElementById('customModal');
            const modalContent = document.getElementById('modalContent');
            
            // 添加关闭动画
            modalContent.classList.add('closing');
            modal.style.animation = 'modalFadeOut 0.3s cubic-bezier(0.4, 0, 1, 1)';
            
            setTimeout(() => {
                modal.classList.remove('show');
                modal.style.display = 'none';
                modal.style.animation = '';
                modalContent.classList.remove('closing');
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // 复制API地址函数
        function copyApiUrl() {
            const url = 'https://role.killerbest.cn/v1';
            navigator.clipboard.writeText(url).then(() => {
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                
                btn.textContent = '✅ 已复制';
                btn.classList.add('copied');
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('copied');
                }, 2000);
            }).catch(() => {
                // 降级处理
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    const btn = document.querySelector('.copy-btn');
                    btn.textContent = '✅ 已复制';
                    btn.classList.add('copied');
                    
                    setTimeout(() => {
                        btn.textContent = '📋 复制API地址';
                        btn.classList.remove('copied');
                    }, 2000);
                } catch (err) {
                    alert('复制失败，请手动复制：' + url);
                }
                
                document.body.removeChild(textArea);
            });
        }

        // 开始对话功能
        function startChat() {
            // 跳转到聊天页面
            window.location.href = '/chat';
        }

        function showApiGuide() {
            showModal(
                'API 使用指南',
                '', 
                null, 
                '通过以下信息开始使用灵星逸 AI', 
                true  
            );
        }

        // 创建增强版粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleTypes = ['star', 'dot', 'large', 'shooting'];
            const particleCount = 80; // 增加粒子数量

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                const type = particleTypes[Math.floor(Math.random() * particleTypes.length)];
                
                particle.classList.add('particle', `particle-${type}`);
                
                // 随机位置
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                
                // 随机动画延迟和持续时间
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (3 + Math.random() * 8) + 's';
                
                particlesContainer.appendChild(particle);
            }

            // 创建魔法轨迹
            for (let i = 0; i < 8; i++) {
                const trail = document.createElement('div');
                trail.classList.add('magic-trail');
                trail.style.animationDelay = Math.random() * 12 + 's';
                trail.style.animationDuration = (8 + Math.random() * 8) + 's';
                
                // 随机轨迹路径
                const randomPath = `
                    @keyframes magic-trail-${i} {
                        0% { transform: translateX(0) translateY(${Math.random() * 100}vh); opacity: 0; }
                        5% { opacity: 1; }
                        95% { opacity: 1; }
                        100% { 
                            transform: translateX(${80 + Math.random() * 40}vw) 
                                       translateY(${Math.random() * 100}vh) 
                                       rotate(${Math.random() * 360}deg); 
                            opacity: 0; 
                        }
                    }
                `;
                
                // 动态添加样式
                const style = document.createElement('style');
                style.textContent = randomPath;
                document.head.appendChild(style);
                
                trail.style.animationName = `magic-trail-${i}`;
                particlesContainer.appendChild(trail);
            }
        }

        // 移除加载动画
        function hideLoading() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            setTimeout(() => {
                loadingOverlay.classList.add('hidden');
            }, 1500);
        }

        // 增强版3D效果
        function init3DEffect() {
            const card = document.getElementById('characterCard');
            
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = ((y - centerY) / centerY) * 8;
                const rotateY = ((x - centerX) / centerX) * 8;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
            });

            // 点击效果
            card.addEventListener('click', (e) => {
                if (e.target.closest('.interaction-btn')) return;
                
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(0.98)';
                setTimeout(() => {
                    card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)';
                }, 150);
            });
        }

        // 事件监听器优化
        
        // 点击外部关闭弹窗
        document.getElementById('customModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.getElementById('customModal');
                if (modal.classList.contains('show')) {
                    closeModal();
                }
            }
        });

        // 防止弹窗内容区域的点击事件冒泡
        document.getElementById('modalContent').addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            createSideGallery();
            hideLoading();
            init3DEffect();
        });

        // 检查API状态
        async function checkApiStatus() {
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    const data = await response.json();
                } else {
                    console.log('API状态检查失败');
                }
            } catch (error) {
                console.error('API连接失败:', error);
            }
        }

        // 初始化检查
        checkApiStatus();

        // 图片加载失败处理
        document.querySelector('.character-avatar img').onerror = function() {
            this.style.display = 'none';
            this.parentElement.innerHTML += '<div style="font-size: 80px; animation: glow 2s ease-in-out infinite alternate;">✨</div>';
        };

        // 触摸设备优化
        if ('ontouchstart' in window) {
            document.querySelectorAll('.stat-item, .interaction-btn').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                
                element.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        }

        // 性能优化：减少不必要的动画
        const reduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        if (reduceMotion.matches) {
            document.querySelectorAll('*').forEach(el => {
                el.style.animationDuration = '0.01ms !important';
                el.style.animationIterationCount = '1 !important';
            });
        }
    </script>
</body>
</html>