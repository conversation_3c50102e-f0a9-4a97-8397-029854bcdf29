"""
云端对话存储系统
提供对话的云端存储、读取、更新、删除功能
支持云端组分类存储，用户自定义组名+UUID，包含去重检测
"""

import os
import json
import uuid
import hashlib
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from fastapi import HTTPException
import shutil

class CloudStorageManager:
    def __init__(self, base_path: str = "cloud_conversations"):
        self.base_path = base_path
        self.groups_path = os.path.join(base_path, "groups")
        self.metadata_path = os.path.join(base_path, "metadata")
        self.ensure_base_directory()

    def ensure_base_directory(self):
        """确保基础目录存在"""
        for path in [self.base_path, self.groups_path, self.metadata_path]:
            if not os.path.exists(path):
                os.makedirs(path)

    def get_group_folder(self, group_id: str) -> str:
        """获取云端组文件夹路径"""
        group_folder = os.path.join(self.groups_path, group_id)
        if not os.path.exists(group_folder):
            os.makedirs(group_folder)
        return group_folder

    def get_date_folder(self, target_date: str = None) -> str:
        """获取日期文件夹路径（兼容旧版本）"""
        if target_date is None:
            target_date = date.today().strftime("%Y-%m-%d")

        date_folder = os.path.join(self.base_path, target_date)
        if not os.path.exists(date_folder):
            os.makedirs(date_folder)

        return date_folder
    
    def generate_conversation_id(self) -> str:
        """生成唯一的对话ID"""
        return str(uuid.uuid4())

    def generate_group_id(self, group_name: str) -> str:
        """生成云端组ID：用户命名+10位随机字符"""
        import random
        import string

        # 生成10位大小写字母+数字的随机字符
        random_chars = ''.join(random.choices(
            string.ascii_letters + string.digits, k=10
        ))

        # 清理用户输入的组名，只保留字母数字和基本符号
        clean_name = "".join(c for c in group_name if c.isalnum() or c in "._-")
        if not clean_name:
            clean_name = "group"

        return f"{clean_name}{random_chars}"

    def create_cloud_group(self, group_name: str, user_id: str = "default") -> Dict[str, Any]:
        """创建新的云端储存组"""
        if not group_name or not group_name.strip():
            raise HTTPException(status_code=400, detail="组名不能为空")

        # 清理组名，移除特殊字符
        clean_name = "".join(c for c in group_name.strip() if c.isalnum() or c in "._-")
        if not clean_name:
            raise HTTPException(status_code=400, detail="组名包含无效字符")

        group_id = self.generate_group_id(clean_name)
        group_folder = self.get_group_folder(group_id)

        # 创建组元数据
        group_metadata = {
            "group_id": group_id,
            "group_name": group_name,
            "clean_name": clean_name,
            "user_id": user_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "conversation_count": 0,
            "total_size": 0
        }

        # 保存组元数据
        metadata_file = os.path.join(self.metadata_path, f"{group_id}.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(group_metadata, f, ensure_ascii=False, indent=2)

        return group_metadata

    def list_cloud_groups(self, user_id: str = "default") -> List[Dict[str, Any]]:
        """列出用户的云端储存组"""
        groups = []

        if not os.path.exists(self.metadata_path):
            return groups

        for filename in os.listdir(self.metadata_path):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(self.metadata_path, filename), 'r', encoding='utf-8') as f:
                        group_data = json.load(f)

                    # 过滤用户的组
                    if group_data.get('user_id') == user_id:
                        groups.append(group_data)

                except Exception as e:
                    print(f"读取组元数据失败 {filename}: {e}")
                    continue

        # 按创建时间排序
        groups.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        return groups

    def import_cloud_group(self, group_id: str, user_id: str = "default") -> Dict[str, Any]:
        """通过分组ID导入云端分组到当前用户"""
        try:
            # 检查分组是否存在
            group_folder = self.get_group_folder(group_id)
            if not os.path.exists(group_folder):
                raise HTTPException(status_code=404, detail="云端分组不存在")

            # 检查元数据文件
            metadata_file = os.path.join(self.metadata_path, f"{group_id}.json")
            if not os.path.exists(metadata_file):
                raise HTTPException(status_code=404, detail="分组元数据不存在")

            # 读取原始元数据
            with open(metadata_file, 'r', encoding='utf-8') as f:
                original_metadata = json.load(f)

            # 创建新的用户关联元数据（保持原分组ID）
            user_metadata = {
                "group_id": group_id,
                "group_name": original_metadata.get('group_name', '导入的分组'),
                "clean_name": original_metadata.get('clean_name', 'imported'),
                "user_id": user_id,
                "created_at": original_metadata.get('created_at'),
                "updated_at": datetime.now().isoformat(),
                "conversation_count": original_metadata.get('conversation_count', 0),
                "total_size": original_metadata.get('total_size', 0),
                "imported_at": datetime.now().isoformat(),
                "original_user": original_metadata.get('user_id')
            }

            # 为当前用户创建访问记录（不修改原始元数据）
            user_access_file = os.path.join(self.metadata_path, f"{group_id}_{user_id}.json")
            with open(user_access_file, 'w', encoding='utf-8') as f:
                json.dump(user_metadata, f, ensure_ascii=False, indent=2)

            return user_metadata

        except Exception as e:
            if isinstance(e, HTTPException):
                raise
            raise HTTPException(status_code=500, detail=f"导入分组失败: {str(e)}")

    def get_user_accessible_groups(self, user_id: str = "default") -> List[Dict[str, Any]]:
        """获取用户可访问的所有分组（包括自己创建的和导入的）"""
        groups = []

        if not os.path.exists(self.metadata_path):
            return groups

        for filename in os.listdir(self.metadata_path):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(self.metadata_path, filename), 'r', encoding='utf-8') as f:
                        group_data = json.load(f)

                    # 检查是否是用户创建的分组或用户访问记录
                    if (group_data.get('user_id') == user_id or
                        filename.endswith(f'_{user_id}.json')):
                        groups.append(group_data)

                except Exception as e:
                    print(f"读取组元数据失败 {filename}: {e}")
                    continue

        # 按创建时间排序，去重
        seen_group_ids = set()
        unique_groups = []
        for group in sorted(groups, key=lambda x: x.get('created_at', ''), reverse=True):
            if group['group_id'] not in seen_group_ids:
                seen_group_ids.add(group['group_id'])
                unique_groups.append(group)

        return unique_groups

    def cleanup_legacy_date_folders(self) -> Dict[str, Any]:
        """清理旧的日期分组文件夹，迁移到新的分组系统"""
        try:
            cleanup_stats = {
                "folders_found": 0,
                "files_migrated": 0,
                "files_deleted": 0,
                "errors": []
            }

            # 扫描云端对话目录中的日期文件夹
            for item in os.listdir(self.conversations_path):
                item_path = os.path.join(self.conversations_path, item)

                # 检查是否是日期格式的文件夹（YYYY-MM-DD）
                if os.path.isdir(item_path) and item != 'groups' and item != 'metadata':
                    # 简单的日期格式检查
                    if len(item) == 10 and item.count('-') == 2:
                        cleanup_stats["folders_found"] += 1

                        try:
                            # 列出文件夹中的所有对话文件
                            for filename in os.listdir(item_path):
                                if filename.endswith('.json'):
                                    file_path = os.path.join(item_path, filename)

                                    try:
                                        # 删除旧文件（不迁移，因为新系统需要用户主动选择分组）
                                        os.remove(file_path)
                                        cleanup_stats["files_deleted"] += 1

                                    except Exception as e:
                                        cleanup_stats["errors"].append(f"删除文件失败 {filename}: {str(e)}")

                            # 删除空的日期文件夹
                            try:
                                os.rmdir(item_path)
                                print(f"已删除旧日期文件夹: {item}")
                            except OSError:
                                # 文件夹不为空，可能还有其他文件
                                cleanup_stats["errors"].append(f"无法删除文件夹 {item}: 文件夹不为空")

                        except Exception as e:
                            cleanup_stats["errors"].append(f"处理文件夹 {item} 失败: {str(e)}")

            return cleanup_stats

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"清理旧数据失败: {str(e)}")

    def calculate_content_hash(self, messages: List[Dict[str, Any]]) -> str:
        """计算对话内容的哈希值用于去重检测"""
        # 提取关键内容用于哈希计算
        content_parts = []
        for msg in messages:
            if isinstance(msg, dict):
                role = msg.get('role', '')
                content = msg.get('content', '')
                if role and content:
                    # 🚀 正确处理不同类型的content
                    if isinstance(content, str):
                        content_str = content
                    elif isinstance(content, list):
                        # 对于数组content，提取文本部分用于哈希
                        text_parts = []
                        for item in content:
                            if isinstance(item, dict) and item.get('type') == 'text':
                                text_parts.append(item.get('text', ''))
                        content_str = ' '.join(text_parts)
                    else:
                        content_str = str(content)

                    if content_str.strip():
                        content_parts.append(f"{role}:{content_str}")

        # 计算SHA256哈希
        content_str = "\n".join(content_parts)
        return hashlib.sha256(content_str.encode('utf-8')).hexdigest()

    def check_duplicate_conversation(self, content_hash: str, group_id: str = None) -> Optional[str]:
        """检查是否存在重复对话，返回重复对话的ID"""
        search_paths = []

        if group_id:
            # 在指定组中查找
            group_folder = self.get_group_folder(group_id)
            if os.path.exists(group_folder):
                search_paths.append(group_folder)
        else:
            # 在所有组中查找
            if os.path.exists(self.groups_path):
                for group_dir in os.listdir(self.groups_path):
                    group_path = os.path.join(self.groups_path, group_dir)
                    if os.path.isdir(group_path):
                        search_paths.append(group_path)

        # 搜索重复内容
        for search_path in search_paths:
            for filename in os.listdir(search_path):
                if filename.endswith('.json'):
                    try:
                        file_path = os.path.join(search_path, filename)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        if data.get('content_hash') == content_hash:
                            return data.get('cloud_id') or filename[:-5]  # 移除.json后缀

                    except Exception as e:
                        print(f"检查重复对话时读取文件失败 {filename}: {e}")
                        continue

        return None

    def get_conversation_path(self, conversation_id: str, group_id: str = None, target_date: str = None) -> str:
        """获取对话文件路径"""
        if group_id:
            # 新的组存储方式
            group_folder = self.get_group_folder(group_id)
            return os.path.join(group_folder, f"{conversation_id}.json")
        else:
            # 兼容旧的日期存储方式
            date_folder = self.get_date_folder(target_date)
            return os.path.join(date_folder, f"{conversation_id}.json")
    
    def save_conversation(self, conversation_data: Dict[str, Any], conversation_id: str = None,
                         group_id: str = None, check_duplicate: bool = True) -> Dict[str, Any]:
        """
        保存对话到云端（支持组存储和去重检测）
        返回保存结果
        """
        try:
            if conversation_id is None:
                conversation_id = self.generate_conversation_id()

            # 使用安全的数据构建方法
            safe_data = self.build_safe_conversation_data(conversation_data, conversation_id)

            # 计算内容哈希用于去重检测
            messages = safe_data.get('messages', [])
            content_hash = self.calculate_content_hash(messages)
            safe_data['content_hash'] = content_hash

            # 去重检测
            if check_duplicate:
                duplicate_id = self.check_duplicate_conversation(content_hash, group_id)
                if duplicate_id:
                    return {
                        "conversation_id": duplicate_id,
                        "is_duplicate": True,
                        "message": "发现重复对话，已跳过保存"
                    }

            # 添加组信息
            if group_id:
                safe_data['group_id'] = group_id
                # 验证组是否存在
                group_metadata_file = os.path.join(self.metadata_path, f"{group_id}.json")
                if not os.path.exists(group_metadata_file):
                    raise HTTPException(status_code=404, detail=f"云端组 {group_id} 不存在")

            # 获取文件路径
            file_path = self.get_conversation_path(conversation_id, group_id)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(safe_data, f, ensure_ascii=False, indent=2)

            # 更新组元数据
            if group_id:
                self.update_group_metadata(group_id, conversation_id, len(json.dumps(safe_data, ensure_ascii=False)))

            return {
                "conversation_id": conversation_id,
                "is_duplicate": False,
                "group_id": group_id,
                "message": "对话保存成功"
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"保存对话失败: {str(e)}")

    def update_group_metadata(self, group_id: str, conversation_id: str = None, size_delta: int = 0):
        """更新组元数据"""
        try:
            metadata_file = os.path.join(self.metadata_path, f"{group_id}.json")
            if not os.path.exists(metadata_file):
                return

            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            # 更新统计信息
            if conversation_id:
                metadata['conversation_count'] = metadata.get('conversation_count', 0) + 1

            if size_delta:
                metadata['total_size'] = metadata.get('total_size', 0) + size_delta

            metadata['updated_at'] = datetime.now().isoformat()

            # 保存更新后的元数据
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"更新组元数据失败: {e}")
    
    def load_conversation(self, conversation_id: str, group_id: str = None, target_date: str = None) -> Dict[str, Any]:
        """
        从云端加载对话（支持组存储）- 🚨 强制group_id验证
        """
        try:
            # 检查输入参数
            if not conversation_id or conversation_id == 'undefined' or conversation_id == 'null':
                raise HTTPException(status_code=400, detail="无效的对话ID")

            # 🚨 安全检查：必须指定分组ID才能访问对话
            if not group_id:
                raise HTTPException(status_code=400, detail="必须指定分组ID才能访问对话")

            # 验证分组ID格式（基本安全检查）
            if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
                raise HTTPException(status_code=400, detail="无效的分组ID格式")

            # 🚨 只从指定组中查找，不允许跨组访问
            file_path = self.get_conversation_path(conversation_id, group_id)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    conversation_data = json.load(f)
                    # 确保返回的数据包含正确的分组ID
                    conversation_data['group_id'] = group_id
                    return conversation_data
            else:
                raise HTTPException(status_code=404, detail=f"对话在指定分组中不存在 (group_id: {group_id})")

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"加载对话失败: {str(e)}")
    
    def _find_conversation_by_id(self, conversation_id: str, target_date: str = None) -> Dict[str, Any]:
        """在所有存储位置中查找对话"""
        # 检查输入参数
        if not conversation_id or conversation_id == 'undefined' or conversation_id == 'null':
            raise HTTPException(status_code=400, detail="无效的对话ID")

        # 1. 优先在组存储中查找
        if os.path.exists(self.groups_path):
            for group_dir in os.listdir(self.groups_path):
                group_path = os.path.join(self.groups_path, group_dir)
                if os.path.isdir(group_path):
                    file_path = os.path.join(group_path, f"{conversation_id}.json")
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            conversation_data = json.load(f)
                            # 确保返回的数据包含分组ID
                            conversation_data['group_id'] = group_dir
                            return conversation_data

        # 2. 在指定日期文件夹中查找（兼容旧版本）
        if target_date:
            file_path = self.get_conversation_path(conversation_id, None, target_date)
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

        # 3. 在所有日期文件夹中查找（兼容旧版本）
        if os.path.exists(self.base_path):
            date_folders = [f for f in os.listdir(self.base_path)
                           if os.path.isdir(os.path.join(self.base_path, f)) and f != "groups" and f != "metadata"]

            # 按日期倒序排序，优先查找最新的
            date_folders.sort(reverse=True)

            for date_folder in date_folders:
                file_path = os.path.join(self.base_path, date_folder, f"{conversation_id}.json")
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        return json.load(f)

        raise HTTPException(status_code=404, detail="对话不存在")
    
    def update_conversation(self, conversation_id: str, conversation_data: Dict[str, Any], group_id: str = None, target_date: str = None) -> bool:
        """
        更新云端对话（完全重构版本）
        """
        try:
            # 检查输入参数
            if not conversation_id or conversation_id == 'undefined' or conversation_id == 'null':
                raise HTTPException(status_code=400, detail="无效的对话ID")
                
            # 🚀 优先使用group_id，如果没有则使用旧的日期方式
            if group_id:
                # 验证分组ID格式
                if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
                    raise HTTPException(status_code=400, detail="无效的分组ID格式")

                # 验证组是否存在
                group_metadata_file = os.path.join(self.metadata_path, f"{group_id}.json")
                if not os.path.exists(group_metadata_file):
                    raise HTTPException(status_code=404, detail=f"云端组 {group_id} 不存在")
            elif target_date is None:
                # 兼容旧的日期方式：如果没有指定日期，先查找现有对话的位置
                try:
                    existing_data = self._find_conversation_by_id(conversation_id)
                    # 从现有数据中获取创建日期作为目标日期
                    created_at = existing_data.get('created_at', datetime.now().isoformat())
                    if isinstance(created_at, str):
                        # 安全地解析日期字符串
                        try:
                            parsed_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            target_date = parsed_date.strftime("%Y-%m-%d")
                        except ValueError:
                            target_date = datetime.now().strftime("%Y-%m-%d")
                    else:
                        target_date = datetime.now().strftime("%Y-%m-%d")
                except HTTPException as e:
                    if e.status_code == 404:
                        # 对话不存在，创建新对话
                        target_date = datetime.now().strftime("%Y-%m-%d")
                    else:
                        raise
            
            # 安全地构建更新数据
            safe_update_data = self.build_safe_conversation_data(conversation_data, conversation_id)

            # 🚀 添加组信息到数据中
            if group_id:
                safe_update_data['group_id'] = group_id

            # 🚀 使用group_id或target_date获取文件路径
            file_path = self.get_conversation_path(conversation_id, group_id or target_date)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(safe_update_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"更新对话失败: {str(e)}")
    
    def build_safe_conversation_data(self, conversation_data: Dict[str, Any], conversation_id: str) -> Dict[str, Any]:
        """
        构建安全的对话数据，防止None值导致的错误
        """
        # 确保消息数据安全
        safe_messages = []
        messages = conversation_data.get('messages', [])
        
        if isinstance(messages, list):
            for msg in messages:
                if isinstance(msg, dict) and msg.get('content'):
                    # 🚀 正确处理content字段（可能是字符串或数组）
                    content = msg.get('content')

                    # 检查content是否有效
                    is_valid_content = False
                    if isinstance(content, str) and content.strip():
                        is_valid_content = True
                    elif isinstance(content, list) and len(content) > 0:
                        is_valid_content = True

                    if is_valid_content:
                        safe_msg = {
                            'role': str(msg.get('role', 'user')),
                            'content': content,  # 🚀 保持原始格式，不强制转换为字符串
                            'timestamp': msg.get('timestamp', int(datetime.now().timestamp() * 1000))
                        }

                    # 🚀 保存完整的模型信息
                    if msg.get('model_id'):
                        safe_msg['model_id'] = str(msg['model_id'])
                    if msg.get('model'):
                        safe_msg['model'] = str(msg['model'])

                    # 🚀 保存完整的统计信息
                    for field in ['inputTokens', 'outputTokens', 'totalTokens', 'inputCost', 'outputCost', 'totalCost']:
                        if field in msg and msg[field] is not None:
                            safe_msg[field] = float(msg[field]) if isinstance(msg[field], (int, float)) else 0

                    # 🚀 保存原始内容和推理过程
                    if msg.get('originalContent'):
                        safe_msg['originalContent'] = str(msg['originalContent'])
                    if msg.get('reasoning'):
                        safe_msg['reasoning'] = str(msg['reasoning'])

                    safe_messages.append(safe_msg)
        
        # 构建安全的对话数据
        safe_data = {
            'id': str(conversation_id),
            'cloud_id': str(conversation_id),
            'title': str(conversation_data.get('title', '未命名对话'))[:200],
            'messages': safe_messages,
            'timestamp': conversation_data.get('timestamp', int(datetime.now().timestamp() * 1000)),
            'created_at': conversation_data.get('created_at', datetime.now().isoformat()),
            'updated_at': datetime.now().isoformat(),
            'lastModified': int(datetime.now().timestamp() * 1000),
            'is_favorite': bool(conversation_data.get('is_favorite', False))
        }

        # 🚀 保存完整的可选字段
        if conversation_data.get('summary'):
            safe_data['summary'] = str(conversation_data['summary'])[:500]

        # 🚀 保存缓存的统计数据
        if conversation_data.get('cached_stats'):
            safe_data['cached_stats'] = conversation_data['cached_stats']

        # 🚀 保存分组信息
        if conversation_data.get('group_id'):
            safe_data['group_id'] = str(conversation_data['group_id'])

        return safe_data
    
    def delete_conversation(self, conversation_id: str, group_id: str = None, target_date: str = None) -> bool:
        """
        删除云端对话（支持组存储）- 🚨 强制group_id验证
        """
        try:
            # 检查输入参数
            if not conversation_id or conversation_id == 'undefined' or conversation_id == 'null':
                raise HTTPException(status_code=400, detail="无效的对话ID")

            # 🚨 安全检查：必须指定分组ID才能删除对话
            if not group_id:
                raise HTTPException(status_code=400, detail="必须指定分组ID才能删除对话")

            # 验证分组ID格式（基本安全检查）
            if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
                raise HTTPException(status_code=400, detail="无效的分组ID格式")

            # 🚨 只从指定组中删除，不允许跨组删除
            file_path = self.get_conversation_path(conversation_id, group_id)
            if os.path.exists(file_path):
                # 获取文件大小用于更新组元数据
                file_size = os.path.getsize(file_path)
                os.remove(file_path)
                # 更新组元数据
                self.update_group_metadata(group_id, size_delta=-file_size)
                return True
            else:
                raise HTTPException(status_code=404, detail=f"对话在指定分组中不存在 (group_id: {group_id})")

            # 🚨 删除不可达的代码，强制group_id验证

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")
    
    def list_conversations(self, group_id: str = None, target_date: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        列出对话（支持组存储和日期筛选）
        """
        try:
            conversations = []

            if group_id:
                # 列出指定组的对话
                group_folder = self.get_group_folder(group_id)
                if os.path.exists(group_folder):
                    conversations.extend(self._list_conversations_in_folder(group_folder))
            elif target_date:
                # 列出指定日期的对话（兼容旧版本）
                date_folder = os.path.join(self.base_path, target_date)
                if os.path.exists(date_folder):
                    conversations.extend(self._list_conversations_in_folder(date_folder))
            else:
                # 列出所有对话
                # 1. 列出组存储中的对话
                if os.path.exists(self.groups_path):
                    for group_dir in os.listdir(self.groups_path):
                        group_path = os.path.join(self.groups_path, group_dir)
                        if os.path.isdir(group_path):
                            conversations.extend(self._list_conversations_in_folder(group_path))

                            if len(conversations) >= limit:
                                break

                # 2. 列出日期存储中的对话（兼容旧版本）
                if len(conversations) < limit and os.path.exists(self.base_path):
                    date_folders = [f for f in os.listdir(self.base_path)
                                   if os.path.isdir(os.path.join(self.base_path, f)) and f != "groups" and f != "metadata"]

                    # 按日期倒序排序
                    date_folders.sort(reverse=True)

                    for date_folder in date_folders:
                        folder_path = os.path.join(self.base_path, date_folder)
                        conversations.extend(self._list_conversations_in_folder(folder_path))

                        if len(conversations) >= limit:
                            break

            return conversations[:limit]

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"列出对话失败: {str(e)}")
    
    def _list_conversations_in_folder(self, folder_path: str) -> List[Dict[str, Any]]:
        """列出指定文件夹中的对话摘要"""
        conversations = []
        
        if not os.path.exists(folder_path):
            return conversations
        
        for filename in os.listdir(folder_path):
            if filename.endswith('.json'):
                file_path = os.path.join(folder_path, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 只返回摘要信息，不包含完整的消息内容
                    summary = {
                        'cloud_id': data.get('cloud_id'),
                        'title': data.get('title', '未命名对话'),
                        'created_at': data.get('created_at'),
                        'updated_at': data.get('updated_at'),
                        'message_count': len(data.get('messages', [])),
                        'is_favorite': data.get('is_favorite', False)
                    }
                    conversations.append(summary)
                    
                except Exception as e:
                    print(f"读取对话文件失败 {filename}: {e}")
                    continue
        
        return conversations
    
    def get_storage_stats(self, group_id: Optional[str] = None) -> Dict[str, Any]:
        """获取存储统计信息（支持按分组过滤）"""
        try:
            stats = {
                'total_conversations': 0,
                'total_size_mb': 0,
                'groups': {}
            }

            if not os.path.exists(self.base_path):
                return stats

            total_size = 0
            total_conversations = 0

            # 检查groups文件夹
            groups_path = os.path.join(self.base_path, 'groups')
            if os.path.exists(groups_path):
                group_folders = [f for f in os.listdir(groups_path)
                               if os.path.isdir(os.path.join(groups_path, f))]

                for group_folder in group_folders:
                    # 如果指定了group_id，只统计该分组
                    if group_id and group_folder != group_id:
                        continue

                    group_path = os.path.join(groups_path, group_folder)
                    conversations_in_group = []
                    group_size = 0

                    # 统计分组中的对话文件
                    for filename in os.listdir(group_path):
                        if filename.endswith('.json'):
                            file_path = os.path.join(group_path, filename)
                            file_size = os.path.getsize(file_path)
                            group_size += file_size
                            conversations_in_group.append(filename[:-5])  # 移除.json后缀

                    # 获取分组名称（从groups.json或使用group_id）
                    group_name = group_folder
                    try:
                        groups_json_path = os.path.join(self.base_path, 'groups.json')
                        if os.path.exists(groups_json_path):
                            with open(groups_json_path, 'r', encoding='utf-8') as f:
                                groups_data = json.load(f)
                                for group in groups_data.get('groups', []):
                                    if group.get('group_id') == group_folder:
                                        group_name = group.get('group_name', group_folder)
                                        break
                    except Exception:
                        pass  # 使用默认的group_folder作为名称

                    stats['groups'][group_folder] = {
                        'group_name': group_name,
                        'count': len(conversations_in_group),
                        'size_mb': round(group_size / (1024 * 1024), 2),
                        'conversations': conversations_in_group
                    }

                    total_conversations += len(conversations_in_group)
                    total_size += group_size

            # 如果指定了group_id但没有找到，返回空统计
            if group_id and group_id not in stats['groups']:
                return {
                    'total_conversations': 0,
                    'total_size_mb': 0,
                    'groups': {},
                    'current_group': group_id
                }

            stats['total_conversations'] = total_conversations
            stats['total_size_mb'] = round(total_size / (1024 * 1024), 2)

            # 如果指定了group_id，添加当前分组信息
            if group_id:
                stats['current_group'] = group_id
                stats['current_group_stats'] = stats['groups'].get(group_id, {
                    'group_name': group_id,
                    'count': 0,
                    'size_mb': 0,
                    'conversations': []
                })

            return stats

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取存储统计失败: {str(e)}")

    def get_group_info_by_id(self, group_id: str) -> Optional[Dict[str, Any]]:
        """根据group_id获取分组信息（高效安全的访问方式）"""
        try:
            # 检查分组是否存在
            group_path = os.path.join(self.base_path, 'groups', group_id)
            if not os.path.exists(group_path):
                return None

            # 从groups.json获取分组元数据
            groups_json_path = os.path.join(self.base_path, 'groups.json')
            if os.path.exists(groups_json_path):
                with open(groups_json_path, 'r', encoding='utf-8') as f:
                    groups_data = json.load(f)
                    for group in groups_data.get('groups', []):
                        if group.get('group_id') == group_id:
                            # 计算实时统计
                            conversation_count = 0
                            total_size = 0

                            for filename in os.listdir(group_path):
                                if filename.endswith('.json'):
                                    file_path = os.path.join(group_path, filename)
                                    total_size += os.path.getsize(file_path)
                                    conversation_count += 1

                            # 更新统计信息
                            group['conversation_count'] = conversation_count
                            group['total_size'] = total_size

                            return group

            # 如果groups.json中没有找到，返回基本信息
            conversation_count = 0
            total_size = 0

            for filename in os.listdir(group_path):
                if filename.endswith('.json'):
                    file_path = os.path.join(group_path, filename)
                    total_size += os.path.getsize(file_path)
                    conversation_count += 1

            return {
                'group_id': group_id,
                'group_name': group_id,
                'clean_name': group_id,
                'user_id': 'default',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'conversation_count': conversation_count,
                'total_size': total_size
            }

        except Exception as e:
            print(f"获取分组信息失败 {group_id}: {e}")
            return None

    def cleanup_old_conversations(self, days_to_keep: int = 30) -> Dict[str, Any]:
        """清理旧对话（可选功能）"""
        try:
            if not os.path.exists(self.base_path):
                return {'deleted_count': 0, 'freed_mb': 0}
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_date.strftime("%Y-%m-%d")
            
            deleted_count = 0
            freed_size = 0
            
            date_folders = [f for f in os.listdir(self.base_path) 
                           if os.path.isdir(os.path.join(self.base_path, f))]
            
            for date_folder in date_folders:
                if date_folder < cutoff_str:
                    folder_path = os.path.join(self.base_path, date_folder)
                    
                    # 计算文件夹大小
                    for filename in os.listdir(folder_path):
                        if filename.endswith('.json'):
                            file_path = os.path.join(folder_path, filename)
                            freed_size += os.path.getsize(file_path)
                            deleted_count += 1
                    
                    # 删除整个日期文件夹
                    shutil.rmtree(folder_path)
            
            return {
                'deleted_count': deleted_count,
                'freed_mb': round(freed_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"清理旧对话失败: {str(e)}")

    def batch_save_conversations(self, conversations: List[Dict[str, Any]], group_id: str = None,
                                check_duplicate: bool = True) -> Dict[str, Any]:
        """
        批量保存对话到云端
        """
        try:
            results = {
                "total": len(conversations),
                "saved": 0,
                "duplicates": 0,
                "errors": 0,
                "details": []
            }

            for conv_data in conversations:
                try:
                    conversation_id = conv_data.get('id') or self.generate_conversation_id()
                    result = self.save_conversation(conv_data, conversation_id, group_id, check_duplicate)

                    if result.get('is_duplicate'):
                        results["duplicates"] += 1
                        results["details"].append({
                            "id": conversation_id,
                            "status": "duplicate",
                            "message": result.get('message', '')
                        })
                    else:
                        results["saved"] += 1
                        results["details"].append({
                            "id": result.get('conversation_id', conversation_id),
                            "status": "saved",
                            "message": result.get('message', '')
                        })

                except Exception as e:
                    results["errors"] += 1
                    results["details"].append({
                        "id": conv_data.get('id', 'unknown'),
                        "status": "error",
                        "message": str(e)
                    })

            return results

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"批量保存对话失败: {str(e)}")

    def batch_delete_conversations(self, conversation_ids: List[str], group_id: str = None) -> Dict[str, Any]:
        """
        批量删除对话
        """
        try:
            results = {
                "total": len(conversation_ids),
                "deleted": 0,
                "errors": 0,
                "details": []
            }

            for conversation_id in conversation_ids:
                try:
                    success = self.delete_conversation(conversation_id, group_id)
                    if success:
                        results["deleted"] += 1
                        results["details"].append({
                            "id": conversation_id,
                            "status": "deleted",
                            "message": "删除成功"
                        })
                    else:
                        results["errors"] += 1
                        results["details"].append({
                            "id": conversation_id,
                            "status": "error",
                            "message": "删除失败"
                        })

                except Exception as e:
                    results["errors"] += 1
                    results["details"].append({
                        "id": conversation_id,
                        "status": "error",
                        "message": str(e)
                    })

            return results

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"批量删除对话失败: {str(e)}")

# 全局云存储管理器实例
cloud_storage = CloudStorageManager()
