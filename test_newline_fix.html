<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>换行符修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-output {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>换行符修复测试</h1>
    
    <div class="test-section">
        <h2>测试 sanitizeString 函数</h2>
        <p>输入包含换行符的文本，测试修复后的 sanitizeString 函数是否保留换行符：</p>
        
        <textarea class="test-input" id="testInput" placeholder="输入包含换行符的文本...">呀，你都开始研究开发者工具里面的东西啦，好厉害！让我看看……嗯，我明白你的意思了！

我把脑袋凑到你的屏幕前，仔细瞧了瞧。

你遇到的这个问题其实很常见哦！在浏览器的"本地存储"那里，它确实只能以一整行文本的形式显示和编辑，因为它本质上就是把所有数据都存成了字符串。</textarea>
        
        <button class="button" onclick="testSanitizeString()">测试 sanitizeString</button>
        
        <h3>原始文本：</h3>
        <div class="test-output" id="originalText"></div>
        
        <h3>修复前的结果（会移除换行符）：</h3>
        <div class="test-output" id="oldResult"></div>
        
        <h3>修复后的结果（应该保留换行符）：</h3>
        <div class="test-output" id="newResult"></div>
    </div>

    <div class="test-section">
        <h2>测试对话数据构建</h2>
        <p>模拟构建上传到云端的对话数据：</p>
        
        <button class="button" onclick="testBuildUploadData()">测试对话数据构建</button>
        
        <h3>模拟对话数据：</h3>
        <div class="test-output" id="conversationData"></div>
        
        <h3>构建的上传数据：</h3>
        <div class="test-output" id="uploadData"></div>
    </div>

    <script>
        // 修复前的 sanitizeString 函数（会移除换行符）
        function oldSanitizeString(value, defaultValue = '') {
            if (value === null || value === undefined) {
                return defaultValue;
            }
            return String(value).replace(/[\x00-\x1f\x7f-\x9f]/g, '');
        }

        // 修复后的 sanitizeString 函数（保留换行符）
        function newSanitizeString(value, defaultValue = '') {
            if (value === null || value === undefined) {
                return defaultValue;
            }
            // 保留换行符和制表符，只移除其他控制字符
            // \x09 = 制表符(tab), \x0a = 换行符(LF), \x0d = 回车符(CR)
            return String(value).replace(/[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]/g, '');
        }

        function testSanitizeString() {
            const input = document.getElementById('testInput').value;
            
            document.getElementById('originalText').textContent = input;
            document.getElementById('oldResult').textContent = oldSanitizeString(input);
            document.getElementById('newResult').textContent = newSanitizeString(input);
        }

        function testBuildUploadData() {
            // 模拟包含换行符的对话数据
            const mockConversation = {
                id: 'test-conversation-123',
                title: '测试对话',
                messages: [
                    {
                        role: 'user',
                        content: '你好！\n\n请帮我解释一下这个问题：\n1. 第一点\n2. 第二点\n\n谢谢！',
                        timestamp: Date.now()
                    },
                    {
                        role: 'assistant',
                        content: '你好！我很乐意帮助你。\n\n关于你提到的问题：\n\n> 第一点的解释在这里\n\n> 第二点的解释在这里\n\n希望这能帮到你！',
                        timestamp: Date.now()
                    }
                ],
                timestamp: Date.now(),
                is_favorite: false
            };

            // 模拟 buildSafeUploadData 函数的核心逻辑
            function buildSafeUploadData(conversation) {
                const cleanMessages = [];
                
                if (Array.isArray(conversation.messages)) {
                    conversation.messages.forEach(msg => {
                        if (msg && typeof msg === 'object' && msg.content) {
                            let cleanContent;
                            if (typeof msg.content === 'string') {
                                cleanContent = newSanitizeString(msg.content, '');
                            } else {
                                cleanContent = newSanitizeString(String(msg.content), '');
                            }

                            const cleanMsg = {
                                role: newSanitizeString(msg.role, 'user'),
                                content: cleanContent,
                                timestamp: msg.timestamp || Date.now()
                            };

                            cleanMessages.push(cleanMsg);
                        }
                    });
                }
                
                return {
                    id: newSanitizeString(conversation.id),
                    title: newSanitizeString(conversation.title, '未命名对话').substring(0, 200),
                    messages: cleanMessages,
                    timestamp: conversation.timestamp || Date.now(),
                    created_at: new Date(conversation.timestamp || Date.now()).toISOString(),
                    is_favorite: Boolean(conversation.is_favorite)
                };
            }

            const uploadData = buildSafeUploadData(mockConversation);
            
            document.getElementById('conversationData').textContent = JSON.stringify(mockConversation, null, 2);
            document.getElementById('uploadData').textContent = JSON.stringify(uploadData, null, 2);
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            testSanitizeString();
            testBuildUploadData();
        };
    </script>
</body>
</html>
