#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件上传测试脚本 - 测试 Responses API 的PDF文件处理功能

根据错误信息，API似乎只支持PDF文件，让我们测试一下
"""

import requests
import json
import base64
import os
import time

# API 配置
API_BASE_URL = "https://api.killerbest.com"
API_KEY = "sk-46BpPGmCYirz7sJ5uuMLizcDWA3E1xcSKGQhnz8F3APW05MB"
MODEL = "ali-gpt-5-mini"

# 测试文件路径
TEST_PDF_PATH = "test.pdf"

def test_pdf_upload_methods():
    """
    测试多种PDF上传方法
    """
    print("🚀 开始PDF文件上传测试")
    print("=" * 50)
    
    if not os.path.exists(TEST_PDF_PATH):
        print(f"❌ PDF文件不存在: {TEST_PDF_PATH}")
        return
    
    print(f"📄 测试文件: {TEST_PDF_PATH}")
    print(f"📏 文件大小: {os.path.getsize(TEST_PDF_PATH)} bytes")
    print()
    
    # 方法1: 尝试OpenAI标准文件上传接口
    test_openai_files_api()
    
    # 方法2: 尝试客户端上传接口
    test_client_upload_api()
    
    # 方法3: 尝试图片上传接口（虽然是PDF）
    test_image_upload_api()
    
    # 方法4: 直接在Responses API中使用base64
    test_responses_with_base64()

def test_openai_files_api():
    """
    测试OpenAI标准文件上传接口
    """
    print("🧪 测试1: OpenAI标准文件上传接口 (/v1/files)")
    
    try:
        with open(TEST_PDF_PATH, 'rb') as f:
            files = {'file': (os.path.basename(TEST_PDF_PATH), f, 'application/pdf')}
            headers = {'Authorization': f'Bearer {API_KEY}'}
            
            response = requests.post(
                f"{API_BASE_URL}/v1/files",
                headers=headers,
                files=files,
                data={'purpose': 'assistants'}
            )
        
        print(f"📤 响应状态码: {response.status_code}")
        print(f"📤 响应头: {dict(response.headers)}")
        print(f"📤 响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 上传成功: {result}")
                return result.get('id')
            except:
                print(f"❌ 响应不是JSON格式")
        else:
            print(f"❌ 上传失败")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    return None

def test_client_upload_api():
    """
    测试客户端上传接口
    """
    print("🧪 测试2: 客户端上传接口 (/client/upload)")
    
    try:
        with open(TEST_PDF_PATH, 'rb') as f:
            files = {'file': (os.path.basename(TEST_PDF_PATH), f, 'application/pdf')}
            headers = {'Authorization': f'Bearer {API_KEY}'}
            
            response = requests.post(
                f"{API_BASE_URL}/client/upload",
                headers=headers,
                files=files
            )
        
        print(f"📤 响应状态码: {response.status_code}")
        print(f"📤 响应头: {dict(response.headers)}")
        print(f"📤 响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 上传成功: {result}")
                return result.get('url') or result.get('file_id')
            except:
                print(f"❌ 响应不是JSON格式")
        else:
            print(f"❌ 上传失败")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    return None

def test_image_upload_api():
    """
    测试图片上传接口（用PDF试试）
    """
    print("🧪 测试3: 图片上传接口 (/client/upload with image field)")
    
    try:
        with open(TEST_PDF_PATH, 'rb') as f:
            files = {'image': (os.path.basename(TEST_PDF_PATH), f, 'application/pdf')}
            headers = {'Authorization': f'Bearer {API_KEY}'}
            
            response = requests.post(
                f"{API_BASE_URL}/client/upload",
                headers=headers,
                files=files
            )
        
        print(f"📤 响应状态码: {response.status_code}")
        print(f"📤 响应头: {dict(response.headers)}")
        print(f"📤 响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 上传成功: {result}")
                return result.get('url') or result.get('file_id')
            except:
                print(f"❌ 响应不是JSON格式")
        else:
            print(f"❌ 上传失败")
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    return None

def test_responses_with_base64():
    """
    测试在Responses API中直接使用base64编码的PDF
    """
    print("🧪 测试4: Responses API中使用base64编码的PDF")
    
    try:
        # 读取PDF文件并转换为base64
        with open(TEST_PDF_PATH, 'rb') as f:
            pdf_content = f.read()
        
        base64_content = base64.b64encode(pdf_content).decode('utf-8')
        
        # 尝试不同的格式
        formats_to_try = [
            {
                "name": "input_file with file_data (base64)",
                "payload": {
                    "model": MODEL,
                    "input": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_text",
                                    "text": "请分析这个PDF文档的内容"
                                },
                                {
                                    "type": "input_file",
                                    "file_data": base64_content,
                                    "filename": "test.pdf"
                                }
                            ]
                        }
                    ],
                    "stream": False
                }
            },
            {
                "name": "input_file with data URL",
                "payload": {
                    "model": MODEL,
                    "input": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_text",
                                    "text": "请分析这个PDF文档的内容"
                                },
                                {
                                    "type": "input_file",
                                    "file_data": f"data:application/pdf;base64,{base64_content}",
                                    "filename": "test.pdf"
                                }
                            ]
                        }
                    ],
                    "stream": False
                }
            }
        ]
        
        for format_test in formats_to_try:
            print(f"📤 尝试格式: {format_test['name']}")
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {API_KEY}'
            }
            
            response = requests.post(
                f"{API_BASE_URL}/v1/responses",
                headers=headers,
                json=format_test['payload'],
                timeout=60
            )
            
            print(f"📤 响应状态码: {response.status_code}")
            print(f"📤 响应内容: {response.text[:500]}...")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"✅ 请求成功!")
                    
                    # 提取回答内容
                    if 'output' in result and len(result['output']) > 0:
                        for output_item in result['output']:
                            if output_item.get('type') == 'message':
                                content = output_item.get('content', [])
                                for content_item in content:
                                    if content_item.get('type') == 'output_text':
                                        print(f"🤖 AI回答:")
                                        print(f"   {content_item.get('text', '无回答内容')[:200]}...")
                                        print()
                    return True
                except:
                    print(f"❌ 响应不是JSON格式")
            else:
                print(f"❌ 请求失败")
            
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    print()
    return False

def main():
    """
    主函数
    """
    test_pdf_upload_methods()
    print("🎉 PDF上传测试完成!")

if __name__ == "__main__":
    main()
