#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传测试脚本 - 测试 Responses API 的文件输入功能

根据 OpenAI Responses API 文档，支持以下文件输入格式：
{
  "type": "input_file",
  "file_id": "文件ID",
  "file_data": "文件内容",
  "filename": "文件名"
}

测试场景：
1. 上传文件到服务器
2. 使用 Responses API 分析文件内容
3. 验证模型能够理解文件内容并回答问题
"""

import requests
import json
import base64
import os
import time

# API 配置
API_BASE_URL = "https://api.killerbest.com"  # 请填入你的API地址
API_KEY = "sk-46BpPGmCYirz7sJ5uuMLizcDWA3E1xcSKGQhnz8F3APW05MB"  # 请填入你的API密钥
MODEL = "ali-gpt-5-mini"

# 测试文件路径
TEST_FILE_PATH = "test_document.txt"

def upload_file_to_server(file_path):
    """
    上传文件到服务器 - 先尝试通过客户端上传接口
    """
    print(f"📁 正在上传文件: {file_path}")

    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None

    try:
        # 尝试通过客户端上传接口
        with open(file_path, 'rb') as f:
            files = {'image': (os.path.basename(file_path), f, 'text/plain')}
            headers = {'Authorization': f'Bearer {API_KEY}'}

            response = requests.post(
                f"{API_BASE_URL}/client/upload",
                headers=headers,
                files=files
            )

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                file_ref = result.get('url', '')
                print(f"✅ 文件上传成功!")
                print(f"   文件引用: {file_ref}")
                return file_ref
            else:
                print(f"❌ 文件上传失败: {result.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 上传文件时发生错误: {e}")
        return None

def read_file_as_base64(file_path):
    """
    读取文件并转换为base64格式
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转换为base64
        content_bytes = content.encode('utf-8')
        base64_content = base64.b64encode(content_bytes).decode('utf-8')
        
        return content, base64_content
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None, None

def test_file_input_with_file_id(file_id):
    """
    测试使用 file_id 的文件输入
    """
    print(f"\n🧪 测试1: 使用 file_id 格式")
    
    payload = {
        "model": MODEL,
        "input": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": "请分析这个文档的内容，告诉我文档的主要内容是什么？"
                    },
                    {
                        "type": "input_file",
                        "file_id": file_id
                    }
                ]
            }
        ],
        "stream": False
    }
    
    return send_responses_request(payload, "file_id格式")

def test_file_input_with_file_data(file_content, filename):
    """
    测试使用 file_data 的文件输入 - 直接在文本中包含文件内容
    """
    print(f"\n🧪 测试2: 使用文本内容格式")

    # 直接在文本中包含文件内容
    combined_text = f"""请分析以下文档内容，总结一下文档中提到的技术规格有哪些？

文档内容：
```
{file_content}
```

请详细分析这个文档。"""

    payload = {
        "model": MODEL,
        "input": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": combined_text
                    }
                ]
            }
        ],
        "stream": False
    }

    return send_responses_request(payload, "文本内容格式")

def test_file_input_with_base64(base64_content, filename):
    """
    测试使用图片上传接口的文件引用
    """
    print(f"\n🧪 测试3: 使用图片上传接口格式")

    # 先上传文件作为"图片"
    print("📁 尝试通过图片上传接口上传文件...")

    try:
        # 将文本文件作为图片上传
        with open("test_document.txt", 'rb') as f:
            files = {'image': (filename, f, 'text/plain')}
            headers = {'Authorization': f'Bearer {API_KEY}'}

            response = requests.post(
                f"{API_BASE_URL}/client/upload",
                headers=headers,
                files=files
            )

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                file_ref = result.get('url', '')
                print(f"✅ 文件上传成功: {file_ref}")

                # 使用文件引用
                payload = {
                    "model": MODEL,
                    "input": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_text",
                                    "text": f"这个文档的用途是什么？请详细说明。文档引用: {file_ref}"
                                }
                            ]
                        }
                    ],
                    "stream": False
                }

                return send_responses_request(payload, "文件引用格式")
            else:
                print(f"❌ 文件上传失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 上传文件时发生错误: {e}")
        return False

def send_responses_request(payload, test_name):
    """
    发送 Responses API 请求
    """
    print(f"📤 发送请求 ({test_name})...")
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {API_KEY}'
        }
        
        response = requests.post(
            f"{API_BASE_URL}/v1/responses",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功 ({test_name})")
            
            # 提取回答内容
            if 'output' in result and len(result['output']) > 0:
                for output_item in result['output']:
                    if output_item.get('type') == 'message':
                        content = output_item.get('content', [])
                        for content_item in content:
                            if content_item.get('type') == 'output_text':
                                print(f"🤖 AI回答:")
                                print(f"   {content_item.get('text', '无回答内容')}")
                                print()
            
            # 显示使用统计
            if 'usage' in result:
                usage = result['usage']
                print(f"📊 Token使用统计:")
                print(f"   输入: {usage.get('input_tokens', 0)} tokens")
                print(f"   输出: {usage.get('output_tokens', 0)} tokens")
                print(f"   总计: {usage.get('total_tokens', 0)} tokens")
                print()
            
            return True
        else:
            print(f"❌ 请求失败 ({test_name}): {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 发送请求时发生错误 ({test_name}): {e}")
        return False

def main():
    """
    主函数 - 执行所有测试
    """
    print("🚀 开始文件上传测试")
    print("=" * 50)
    
    # 检查测试文件是否存在
    if not os.path.exists(TEST_FILE_PATH):
        print(f"❌ 测试文件不存在: {TEST_FILE_PATH}")
        print("请确保 test_document.txt 文件在当前目录中")
        return
    
    # 读取文件内容
    file_content, base64_content = read_file_as_base64(TEST_FILE_PATH)
    if not file_content:
        return
    
    print(f"📄 测试文件: {TEST_FILE_PATH}")
    print(f"📏 文件大小: {len(file_content)} 字符")
    print()
    
    # 测试1: 上传文件并使用 file_id (可能失败，因为服务配置问题)
    print("⚠️  注意：文件上传可能因为服务配置问题失败，这是正常的")
    file_id = upload_file_to_server(TEST_FILE_PATH)
    if file_id:
        time.sleep(1)  # 等待文件处理
        test_file_input_with_file_id(file_id)

    # 测试2: 使用文本内容 (最可靠的方法)
    test_file_input_with_file_data(file_content, os.path.basename(TEST_FILE_PATH))

    # 测试3: 使用图片上传接口
    test_file_input_with_base64(base64_content, os.path.basename(TEST_FILE_PATH))
    
    print("🎉 所有测试完成!")

if __name__ == "__main__":
    main()
