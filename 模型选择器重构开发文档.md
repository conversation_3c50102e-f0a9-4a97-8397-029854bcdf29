# 模型选择器重构开发文档

## 项目概述

### 目标
将现有的简单下拉列表模型选择器重构为现代化的分组卡片式展示界面，提供更好的用户体验和信息展示。

### 核心需求
1. **分组展示**：按模型类别（category）进行分组
2. **卡片布局**：每个模型以卡片形式展示详细信息
3. **搜索功能**：支持实时搜索和过滤
4. **定价展示**：显示输入/输出价格信息
5. **响应式设计**：适配不同屏幕尺寸
6. **主题适配**：完美适配现有主题系统

## 开发阶段

### 阶段一：后端API增强

#### 当前API结构
```
GET https://ai.killerbest.cn/client/models
```

#### 现有数据字段
```json
{
    "id": "astra",
    "display_name": "灵星逸",
    "description": "最完整真实的小灵星啦~",
    "avatar_url": "https://imgbed.killerbest.com/file/1749487165619_image.png",
    "category": "虚拟恋人",
    "tags": ["女友", "陪伴", "活泼"],
    "is_default": true
}
```

#### 需要新增的字段
```json
{
    "pricing": {
        "input_price": 2.50,    // $/1M tokens
        "output_price": 10.00   // $/1M tokens
    },
    "status": "enabled",        // enabled/disabled
    "last_updated": "2024-01-15T10:30:00Z"  // 可选
}
```

### 阶段二：前端重构开发

#### 2.1 整体架构设计

```
新模型选择器组件结构：
├── 搜索栏 (ModelSearchBar)
├── 视图切换 (ViewToggle) 
├── 分组标签页 (CategoryTabs)
└── 模型网格 (ModelGrid)
    └── 模型卡片 (ModelCard)
```

#### 2.2 HTML结构设计

```html
<!-- 新模型选择器容器 -->
<div id="newModelSelector" class="new-model-selector">
    <!-- 顶部工具栏 -->
    <div class="model-selector-toolbar">
        <div class="model-search-container">
            <input type="text" id="modelSearchInput" placeholder="搜索模型...">
        </div>
        <div class="view-toggle">
            <button class="view-btn active" data-view="grid">网格</button>
            <button class="view-btn" data-view="list">列表</button>
        </div>
    </div>
    
    <!-- 分组标签页 -->
    <div class="category-tabs">
        <button class="category-tab active" data-category="all">全部</button>
        <!-- 动态生成分组标签 -->
    </div>
    
    <!-- 模型展示区域 -->
    <div class="models-container">
        <div class="models-grid" id="modelsGrid">
            <!-- 动态生成模型卡片 -->
        </div>
    </div>
</div>
```

#### 2.3 CSS样式设计

##### 主要CSS类
- `.new-model-selector` - 主容器
- `.model-selector-toolbar` - 顶部工具栏
- `.category-tabs` - 分组标签页
- `.models-grid` - 模型网格容器
- `.model-card` - 单个模型卡片
- `.model-card.selected` - 选中状态
- `.model-card.disabled` - 禁用状态

##### 响应式断点
- 大屏 (>1200px): 4列网格
- 中屏 (768-1200px): 3列网格
- 小屏 (<768px): 2列网格

#### 2.4 JavaScript功能模块

##### 核心类：NewModelSelector
```javascript
class NewModelSelector {
    constructor() {
        this.models = [];
        this.categories = [];
        this.currentCategory = 'all';
        this.searchTerm = '';
        this.selectedModel = null;
    }
    
    // 主要方法
    init()                    // 初始化
    loadModels()             // 加载模型数据
    renderCategories()       // 渲染分组标签
    renderModels()           // 渲染模型网格
    filterModels()           // 过滤模型
    selectModel(modelId)     // 选择模型
    updateUI()               // 更新界面
}
```

##### 主要功能方法
1. **数据加载**：`loadModels()` - 从API获取模型数据
2. **分组处理**：`categorizeModels()` - 按category分组
3. **搜索过滤**：`filterModels()` - 实时搜索过滤
4. **模型选择**：`selectModel()` - 处理模型切换
5. **UI更新**：`updateUI()` - 同步界面状态

#### 2.5 模型卡片设计

##### 卡片布局
```
┌─────────────────────────────┐
│ 🖼️ [头像]  模型名称  [状态]   │
│                             │
│ 简短描述文字...              │
│                             │
│ 💰 $2.5/$10 per 1M tokens   │
│                             │
│ [标签1] [标签2] [标签3]      │
└─────────────────────────────┘
```

##### 状态标识
- **可用** (enabled): 正常显示
- **禁用** (disabled): 灰化处理
- **默认** (is_default): 特殊标识
- **选中** (selected): 高亮边框

#### 2.6 交互设计

##### 用户操作流程
1. **打开选择器** → 显示当前分组的模型
2. **切换分组** → 过滤显示对应分组模型
3. **搜索模型** → 实时过滤匹配结果
4. **选择模型** → 更新当前模型，关闭选择器
5. **悬停卡片** → 显示详细信息tooltip

##### 键盘支持
- `Tab` / `Shift+Tab`: 导航
- `Enter` / `Space`: 选择
- `Esc`: 关闭选择器
- `Arrow Keys`: 网格导航

## 技术实现细节

### 3.1 与现有系统集成

#### 保持兼容性
- 不破坏现有的 `this.settings.model` 逻辑
- 保持 `selectModel()` 方法接口一致
- 维持设置面板的模型同步

#### 数据流
```
API数据 → NewModelSelector → 用户选择 → 更新settings → 同步旧选择器
```

### 3.2 性能优化

#### 渲染优化
- **虚拟滚动**：模型数量过多时启用
- **图片懒加载**：头像图片按需加载
- **搜索防抖**：避免频繁过滤操作

#### 内存管理
- 及时清理事件监听器
- 复用DOM元素
- 避免内存泄漏

### 3.3 错误处理

#### API错误
- 网络错误时显示重试按钮
- 数据格式错误时使用默认数据
- 加载失败时回退到旧选择器

#### 用户体验
- 加载状态指示器
- 空状态提示
- 错误信息友好展示

## 开发计划

### Phase 1: 基础框架 (1-2天)
- [ ] 创建新模型选择器HTML结构
- [ ] 实现基础CSS样式
- [ ] 创建NewModelSelector类框架
- [ ] 实现基本的模型加载和显示

### Phase 2: 核心功能 (2-3天)
- [ ] 实现分组功能
- [ ] 添加搜索过滤
- [ ] 完善模型卡片设计
- [ ] 实现模型选择逻辑

### Phase 3: 优化完善 (1-2天)
- [ ] 响应式布局优化
- [ ] 动画效果添加
- [ ] 性能优化
- [ ] 错误处理完善

### Phase 4: 集成测试 (1天)
- [ ] 与现有系统集成测试
- [ ] 兼容性测试
- [ ] 用户体验测试
- [ ] 清理旧代码

## 风险评估

### 技术风险
- **兼容性问题**：新旧选择器切换可能导致状态不同步
- **性能问题**：大量模型时可能影响渲染性能
- **API依赖**：后端API变更可能影响前端功能

### 解决方案
- 充分测试新旧系统的数据同步
- 实现虚拟滚动和懒加载优化
- 设计灵活的API适配层

## 测试计划

### 功能测试
- [ ] 模型加载和显示
- [ ] 分组切换功能
- [ ] 搜索过滤功能
- [ ] 模型选择和切换
- [ ] 响应式布局

### 兼容性测试
- [ ] 不同浏览器测试
- [ ] 不同屏幕尺寸测试
- [ ] 主题切换测试
- [ ] 与现有功能集成测试

### 性能测试
- [ ] 大量模型加载性能
- [ ] 搜索响应速度
- [ ] 内存使用情况
- [ ] 动画流畅度

---

*文档版本：v1.0*  
*创建时间：2024-01-15*  
*最后更新：2024-01-15*
