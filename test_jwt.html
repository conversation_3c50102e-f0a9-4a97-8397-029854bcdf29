<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Token 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 JWT Token 测试</h1>
        
        <div class="test-section info">
            <h3>当前Token状态</h3>
            <p>Token: <span id="tokenStatus">检查中...</span></p>
            <button onclick="checkToken()">检查Token</button>
            <button onclick="generateToken()">生成新Token</button>
        </div>
        
        <div class="test-section">
            <h3>云端组管理测试</h3>
            <button onclick="testCreateGroup()">创建测试组</button>
            <button onclick="testListGroups()">列出云端组</button>
            <div id="groupResult"></div>
        </div>
        
        <div class="test-section">
            <h3>云端对话测试</h3>
            <button onclick="testSaveConversation()">保存测试对话</button>
            <button onclick="testListConversations()">列出云端对话</button>
            <button onclick="testBatchOperations()">测试批量操作</button>
            <div id="conversationResult"></div>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div id="testLog"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let testToken = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="test-section ${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        async function checkToken() {
            // 尝试从localStorage获取token
            const storedToken = localStorage.getItem('auth_token');
            if (storedToken) {
                testToken = storedToken;
                document.getElementById('tokenStatus').textContent = `已找到: ${testToken.substring(0, 20)}...`;
                log('从localStorage获取到token', 'success');
            } else {
                document.getElementById('tokenStatus').textContent = '未找到token';
                log('localStorage中未找到token', 'error');
            }
        }
        
        async function generateToken() {
            try {
                const response = await fetch('/auth/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: 'test_user',
                        username: 'test'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`生成token失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                testToken = result.access_token;
                localStorage.setItem('auth_token', testToken);
                
                document.getElementById('tokenStatus').textContent = `新生成: ${testToken.substring(0, 20)}...`;
                log('成功生成新token', 'success');
                
            } catch (error) {
                log(`生成token失败: ${error.message}`, 'error');
            }
        }
        
        async function testCreateGroup() {
            if (!testToken) {
                log('请先获取或生成token', 'error');
                return;
            }
            
            try {
                const response = await fetch('/cloud/groups', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${testToken}`
                    },
                    body: JSON.stringify({
                        group_name: '测试组_' + Date.now()
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`创建组失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                document.getElementById('groupResult').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                log('成功创建云端组', 'success');
                
            } catch (error) {
                log(`创建组失败: ${error.message}`, 'error');
            }
        }
        
        async function testListGroups() {
            if (!testToken) {
                log('请先获取或生成token', 'error');
                return;
            }
            
            try {
                const response = await fetch('/cloud/groups', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${testToken}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`获取组列表失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                document.getElementById('groupResult').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                log(`获取到 ${result.groups?.length || 0} 个云端组`, 'success');
                
            } catch (error) {
                log(`获取组列表失败: ${error.message}`, 'error');
            }
        }
        
        async function testSaveConversation() {
            if (!testToken) {
                log('请先获取或生成token', 'error');
                return;
            }
            
            try {
                const testConv = {
                    id: 'test_' + Date.now(),
                    title: '测试对话',
                    messages: [
                        { role: 'user', content: '你好' },
                        { role: 'assistant', content: '你好！有什么可以帮助你的吗？' }
                    ],
                    timestamp: Date.now(),
                    is_favorite: false
                };
                
                const response = await fetch('/cloud/conversations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${testToken}`
                    },
                    body: JSON.stringify(testConv)
                });
                
                if (!response.ok) {
                    throw new Error(`保存对话失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                document.getElementById('conversationResult').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                log('成功保存测试对话', 'success');
                
            } catch (error) {
                log(`保存对话失败: ${error.message}`, 'error');
            }
        }
        
        async function testListConversations() {
            if (!testToken) {
                log('请先获取或生成token', 'error');
                return;
            }
            
            try {
                const response = await fetch('/cloud/conversations', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${testToken}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`获取对话列表失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                document.getElementById('conversationResult').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                log(`获取到 ${result.conversations?.length || 0} 个云端对话`, 'success');
                
            } catch (error) {
                log(`获取对话列表失败: ${error.message}`, 'error');
            }
        }
        
        async function testBatchOperations() {
            if (!testToken) {
                log('请先获取或生成token', 'error');
                return;
            }
            
            try {
                const testConvs = [
                    {
                        id: 'batch_test_1',
                        title: '批量测试对话1',
                        messages: [{ role: 'user', content: '测试1' }],
                        timestamp: Date.now()
                    },
                    {
                        id: 'batch_test_2',
                        title: '批量测试对话2',
                        messages: [{ role: 'user', content: '测试2' }],
                        timestamp: Date.now()
                    }
                ];
                
                const response = await fetch('/cloud/conversations/batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${testToken}`
                    },
                    body: JSON.stringify({
                        conversations: testConvs,
                        check_duplicate: true
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`批量操作失败: ${response.statusText}`);
                }
                
                const result = await response.json();
                document.getElementById('conversationResult').innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
                log('批量操作测试完成', 'success');
                
            } catch (error) {
                log(`批量操作失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查token
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
