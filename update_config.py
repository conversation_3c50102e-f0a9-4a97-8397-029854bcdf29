#!/usr/bin/env python3
"""
批量更新config.json中的角色配置，为所有角色添加allow_custom_system_prompt字段
"""

import json
import shutil
from datetime import datetime

def update_config():
    """更新配置文件"""
    config_path = "config.json"
    backup_path = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        # 备份原文件
        shutil.copy2(config_path, backup_path)
        print(f"✅ 已创建备份文件: {backup_path}")
        
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新角色配置
        if 'character_roles' in config:
            updated_count = 0
            for char_id, char_data in config['character_roles'].items():
                if 'allow_custom_system_prompt' not in char_data:
                    # 为特定角色启用自定义提示词功能
                    if char_id in ['astra', 'astra-slow']:
                        char_data['allow_custom_system_prompt'] = True
                        print(f"✅ 为角色 {char_id} 启用了自定义系统提示词功能")
                    else:
                        char_data['allow_custom_system_prompt'] = False
                        print(f"➕ 为角色 {char_id} 添加了自定义系统提示词字段（默认关闭）")
                    updated_count += 1
            
            print(f"\n📊 总计更新了 {updated_count} 个角色配置")
        
        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置文件更新完成: {config_path}")
        
    except Exception as e:
        print(f"❌ 更新配置文件时出错: {e}")
        # 如果出错，尝试恢复备份
        try:
            shutil.copy2(backup_path, config_path)
            print(f"🔄 已从备份恢复配置文件")
        except:
            pass

if __name__ == "__main__":
    update_config()
