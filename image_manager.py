import os
import time
import secrets
import mimetypes
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
from fastapi import HTTPEx<PERSON>, UploadFile
from config import get_timestamp
from utils import image_to_base64
from stats import update_image_stats, save_stats

def ensure_upload_directory(config: Dict[str, Any]) -> Path:
    """确保上传目录存在"""
    upload_path = Path(config.get('image_upload', {}).get('local_storage_path', 'uploads'))
    upload_path.mkdir(exist_ok=True)
    return upload_path

def cleanup_old_images(config: Dict[str, Any]):
    """清理旧图片文件"""
    try:
        image_config = config.get('image_upload', {})
        if not image_config.get('auto_cleanup', True):
            return
        
        upload_dir = ensure_upload_directory(config)
        cleanup_days = image_config.get('cleanup_days', 7)
        max_storage_size = image_config.get('max_storage_size', 500 * 1024 * 1024)
        max_files = image_config.get('max_files', 1000)
        
        now = datetime.now()
        cutoff_time = now - timedelta(days=cleanup_days)
        
        # 获取所有图片文件信息
        image_files = []
        total_size = 0
        
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                mime_type, _ = mimetypes.guess_type(str(file_path))
                if mime_type and mime_type.startswith('image/'):
                    stat = file_path.stat()
                    file_info = {
                        'path': file_path,
                        'size': stat.st_size,
                        'created_at': datetime.fromtimestamp(stat.st_ctime),
                        'modified_at': datetime.fromtimestamp(stat.st_mtime)
                    }
                    image_files.append(file_info)
                    total_size += stat.st_size
        
        # 按修改时间排序（最旧的在前）
        image_files.sort(key=lambda x: x['modified_at'])
        
        deleted_count = 0
        freed_size = 0
        
        # 1. 删除超过保留期的文件
        for file_info in image_files[:]:
            if file_info['modified_at'] < cutoff_time:
                try:
                    file_info['path'].unlink()
                    deleted_count += 1
                    freed_size += file_info['size']
                    total_size -= file_info['size']
                    image_files.remove(file_info)
                except Exception as e:
                    print(f"[{get_timestamp()}] 删除图片失败 {file_info['path'].name}: {e}")
        
        # 2. 如果总大小仍超过限制，只删除部分最旧的文件（保守策略）
        # 只有当总大小严重超出限制时才进行清理（超出20%）
        if total_size > max_storage_size * 1.2 and image_files:
            print(f"[{get_timestamp()}] ⚠️ 存储空间超出限制20%，开始保守清理...")
            target_size = max_storage_size * 0.8  # 清理到80%以下
            
            # 限制一次性删除的文件数量，避免过度清理
            max_delete_count = min(len(image_files) // 2, 50)  # 最多删除一半或50个文件
            delete_count = 0
            
            while total_size > target_size and image_files and delete_count < max_delete_count:
                oldest_file = image_files.pop(0)
                try:
                    oldest_file['path'].unlink()
                    deleted_count += 1
                    delete_count += 1
                    freed_size += oldest_file['size']
                    total_size -= oldest_file['size']
                except Exception as e:
                    print(f"[{get_timestamp()}] 删除图片失败 {oldest_file['path'].name}: {e}")
        
        # 3. 如果文件数量仍超过限制，只删除部分最旧的文件
        if len(image_files) > max_files:
            print(f"[{get_timestamp()}] ⚠️ 文件数量超出限制，开始数量清理...")
            target_files = int(max_files * 0.8)  # 清理到80%以下
            
            while len(image_files) > target_files:
                oldest_file = image_files.pop(0)
                try:
                    oldest_file['path'].unlink()
                    deleted_count += 1
                    freed_size += oldest_file['size']
                except Exception as e:
                    print(f"[{get_timestamp()}] 删除图片失败 {oldest_file['path'].name}: {e}")
        
        if deleted_count > 0:
            print(f"[{get_timestamp()}] 图片清理完成: 删除 {deleted_count} 个文件，释放 {freed_size / (1024*1024):.2f} MB 空间")
            # 更新统计信息
            update_image_stats(config)
            save_stats()
        
    except Exception as e:
        print(f"[{get_timestamp()}] 图片清理失败: {e}")

def generate_image_id(original_filename: Optional[str]) -> str:
    """生成简洁的图片ID"""
    # 提取文件扩展名
    file_extension = os.path.splitext(original_filename)[1] if original_filename else '.jpg'
    
    # 生成基于时间戳的简短ID
    timestamp = int(time.time() * 1000)
    random_suffix = secrets.token_hex(4)  # 8位随机后缀
    
    # 格式：img_时间戳_随机后缀.扩展名
    image_id = f"img_{timestamp}_{random_suffix}{file_extension}"
    
    return image_id

async def upload_image(file: UploadFile, config: Dict[str, Any]) -> Dict[str, Any]:
    """上传图片文件"""
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只允许上传图片文件")
        
        # 验证文件大小
        image_config = config.get('image_upload', {})
        max_size = image_config.get('max_file_size', 10 * 1024 * 1024)
        
        # 读取文件内容并检查大小
        content = await file.read()
        if len(content) > max_size:
            raise HTTPException(status_code=400, detail=f"文件大小超过限制({max_size // (1024*1024)}MB)")
        
        # 生成简洁的图片ID
        image_id = generate_image_id(file.filename)
        
        # 确保上传目录存在
        ensure_upload_directory(config)
        
        # 智能存储管理：只在必要时清理
        upload_dir = ensure_upload_directory(config)
        try:
            current_size = sum(f.stat().st_size for f in upload_dir.iterdir() if f.is_file())
            max_storage = image_config.get('max_storage_size', 2 * 1024 * 1024 * 1024)
            
            # 只有当存储使用率超过95%时才进行清理
            if current_size + len(content) > max_storage * 0.95:
                print(f"[{get_timestamp()}] ⚠️ 存储空间紧张，执行清理...")
                cleanup_old_images(config)
        except Exception as e:
            print(f"存储检查失败，继续上传: {e}")
        
        # 保存到本地（作为云端存储的缓存）
        local_file_path = Path(image_config['local_storage_path']) / image_id
        
        with open(local_file_path, 'wb') as f:
            f.write(content)
        
        print(f"[{get_timestamp()}] 📁 图片已保存: {image_id} ({len(content) // 1024}KB)")
        
        # 更新图片统计
        update_image_stats(config)
        save_stats()
        
        # 返回图片引用信息（不返回base64）
        return {
            "success": True,
            "image_url": f"image_ref:{image_id}",  # 返回图片引用而不是base64
            "local_path": image_id  # 图片ID
        }
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"[{get_timestamp()}] 图片上传处理异常: {e}")
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")

def get_image_list(page: int = 1, limit: int = 20, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """获取图片列表"""
    try:
        upload_dir = ensure_upload_directory(config)
        
        # 获取所有图片文件
        image_files = []
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                stat = file_path.stat()
                image_files.append({
                    "filename": file_path.name,
                    "size": stat.st_size,
                    "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
        
        # 按创建时间倒序排列
        image_files.sort(key=lambda x: x['created_at'], reverse=True)
        
        # 分页
        total = len(image_files)
        start = (page - 1) * limit
        end = start + limit
        paginated_files = image_files[start:end]
        
        return {
            "images": paginated_files,
            "total": total,
            "page": page,
            "limit": limit,
            "total_pages": (total + limit - 1) // limit
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")

def get_image_file(filename: str, format: str = "file", config: Dict[str, Any] = None) -> Dict[str, Any]:
    """获取图片文件"""
    try:
        upload_dir = ensure_upload_directory(config)
        file_path = upload_dir / filename
        
        if not file_path.exists() or not file_path.is_file():
            raise HTTPException(status_code=404, detail=f"图片文件不存在: {filename}")
        
        # 检查文件类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="不是有效的图片文件")
        
        if format == "base64":
            # 返回纯base64格式（供API使用）
            pure_base64 = image_to_base64(str(file_path), mime_type, data_url=False)
            if pure_base64:
                # 构造完整的数据URL供前端使用
                data_url = f"data:{mime_type};base64,{pure_base64}"
                return {
                    "success": True,
                    "image_id": filename,
                    "filename": filename,  # 兼容旧字段名
                    "mime_type": mime_type,
                    "base64_data": data_url,  # 完整的数据URL
                    "pure_base64": pure_base64,  # 纯base64数据（供API使用）
                    "data_length": len(data_url)
                }
            else:
                raise HTTPException(status_code=500, detail="转换为base64失败")
        elif format == "preview":
            # 返回预览信息
            file_size = file_path.stat().st_size
            return {
                "success": True,
                "image_id": filename,
                "mime_type": mime_type,
                "size": file_size,
                "size_kb": file_size // 1024,
                "preview_url": f"/client/images/{filename}"
            }
        else:
            # 返回文件路径
            return {
                "file_path": str(file_path),
                "mime_type": mime_type
            }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片失败: {str(e)}")

def delete_image(filename: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """删除图片文件"""
    try:
        upload_dir = ensure_upload_directory(config)
        file_path = upload_dir / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="图片文件不存在")
        
        file_path.unlink()
        
        # 更新图片统计
        update_image_stats(config)
        save_stats()
        
        return {"success": True, "message": f"图片 {filename} 已删除"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除图片失败: {str(e)}")

def batch_delete_images(filenames: List[str], config: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量删除图片文件"""
    try:
        upload_dir = ensure_upload_directory(config)
        results = []
        
        for filename in filenames:
            file_path = upload_dir / filename
            
            if not file_path.exists():
                results.append({
                    "filename": filename,
                    "success": False,
                    "error": "文件不存在"
                })
                continue
            
            try:
                file_path.unlink()
                results.append({
                    "filename": filename,
                    "success": True,
                    "message": "删除成功"
                })
            except Exception as e:
                results.append({
                    "filename": filename,
                    "success": False,
                    "error": f"删除失败: {str(e)}"
                })
        
        success_count = sum(1 for r in results if r["success"])
        
        # 如果有删除成功的，更新图片统计
        if success_count > 0:
            update_image_stats(config)
            save_stats()
        
        return {
            "success": True,
            "total": len(filenames),
            "success_count": success_count,
            "failed_count": len(filenames) - success_count,
            "results": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

def manual_cleanup(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """手动清理图片"""
    try:
        from stats import get_stats
        
        old_stats = get_stats().get('image_stats', {})
        old_count = old_stats.get('total_files', 0)
        old_size = old_stats.get('total_size', 0)
        
        cleanup_old_images(config)
        
        new_stats = get_stats().get('image_stats', {})
        new_count = new_stats.get('total_files', 0)
        new_size = new_stats.get('total_size', 0)
        
        deleted_count = max(0, old_count - new_count)
        freed_size = max(0, old_size - new_size)
        
        return {
            "success": True,
            "message": "图片清理完成",
            "deleted_count": deleted_count,
            "freed_size_mb": round(freed_size / (1024 * 1024), 2),
            "remaining_count": new_count,
            "remaining_size_mb": round(new_size / (1024 * 1024), 2)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}") 