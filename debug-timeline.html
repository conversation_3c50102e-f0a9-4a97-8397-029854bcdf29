<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-timeline {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            z-index: 9999;
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid #667eea;
            border-radius: 4px;
            padding: 10px;
            width: 40px;
            height: 300px;
        }
        .test-line {
            position: absolute;
            left: 50%;
            top: 20px;
            bottom: 20px;
            width: 2px;
            background: linear-gradient(to bottom, 
                rgba(102, 126, 234, 0.3) 0%,
                rgba(102, 126, 234, 0.8) 50%,
                rgba(102, 126, 234, 0.3) 100%);
            transform: translateX(-50%);
        }
        .test-dot {
            position: absolute;
            left: 50%;
            width: 8px;
            height: 8px;
            background: #667eea;
            border: 2px solid white;
            border-radius: 50%;
            transform: translateX(-50%);
            cursor: pointer;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h2>时间线调试工具</h2>
        <p>这个页面用于测试时间线的显示和定位</p>
        
        <button onclick="showTestTimeline()">显示测试时间线</button>
        <button onclick="hideTestTimeline()">隐藏测试时间线</button>
        <button onclick="checkMainPage()">检查主页面元素</button>
        <button onclick="clearLog()">清空日志</button>
        
        <h3>调试日志:</h3>
        <div class="log" id="debugLog"></div>
    </div>

    <!-- 测试时间线 -->
    <div class="test-timeline" id="testTimeline" style="display: none;">
        <div class="test-line"></div>
        <div class="test-dot" style="top: 30px;"></div>
        <div class="test-dot" style="top: 60px;"></div>
        <div class="test-dot" style="top: 90px;"></div>
        <div class="test-dot" style="top: 120px;"></div>
        <div class="test-dot" style="top: 150px;"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function showTestTimeline() {
            const timeline = document.getElementById('testTimeline');
            timeline.style.display = 'block';
            log('测试时间线已显示');
        }

        function hideTestTimeline() {
            const timeline = document.getElementById('testTimeline');
            timeline.style.display = 'none';
            log('测试时间线已隐藏');
        }

        function checkMainPage() {
            // 尝试在新窗口中检查主页面
            const mainWindow = window.open('chat.html', '_blank');
            
            setTimeout(() => {
                try {
                    const simpleTimeline = mainWindow.document.getElementById('simpleTimeline');
                    const timelineToggle = mainWindow.document.getElementById('timelineToggle');
                    const timelineDots = mainWindow.document.getElementById('timelineDots');
                    
                    log(`主页面检查结果:`);
                    log(`- simpleTimeline元素: ${simpleTimeline ? '存在' : '不存在'}`);
                    log(`- timelineToggle元素: ${timelineToggle ? '存在' : '不存在'}`);
                    log(`- timelineDots元素: ${timelineDots ? '存在' : '不存在'}`);
                    
                    if (simpleTimeline) {
                        log(`- 时间线显示状态: ${simpleTimeline.style.display}`);
                        log(`- 时间线位置: ${getComputedStyle(simpleTimeline).position}`);
                        log(`- 时间线z-index: ${getComputedStyle(simpleTimeline).zIndex}`);
                    }
                    
                    if (timelineToggle) {
                        log(`- 开关状态: ${timelineToggle.checked ? '开启' : '关闭'}`);
                    }
                } catch (e) {
                    log(`检查主页面时出错: ${e.message}`);
                }
            }, 1000);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        // 页面加载时的初始检查
        window.onload = function() {
            log('调试页面已加载');
            log('测试时间线元素检查:');
            
            const testTimeline = document.getElementById('testTimeline');
            log(`- 测试时间线元素: ${testTimeline ? '存在' : '不存在'}`);
            
            if (testTimeline) {
                const style = getComputedStyle(testTimeline);
                log(`- 位置: ${style.position}`);
                log(`- z-index: ${style.zIndex}`);
                log(`- 右边距: ${style.right}`);
                log(`- 顶部位置: ${style.top}`);
            }
        };
    </script>
</body>
</html>
