#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenAI /v1/responses API 交互式文档演示程序
完整的 API 参数展示与功能测试工具

功能特点:
- 支持所有 API 参数的详细配置
- 多种输入类型支持 (文本、图像、文件)
- 各种工具集成 (网络搜索、文件搜索、函数调用、计算机使用)
- 流式和非流式响应
- 推理模型支持
- 自定义 Base URL 和 API Key
- 实时响应展示
- 详细的参数说明和示例
"""

import requests
import json
import time
import base64
import os
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import argparse
import sys


class OpenAIResponsesDemo:
    """OpenAI Responses API 交互式演示类"""
    
    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):
        """初始化演示程序
        
        Args:
            base_url: API 基础URL
            api_key: API 密钥
        """
        self.base_url = base_url or "https://api.killerbest.com"
        self.api_key = api_key or "sk-46BpPGmCYirz7sJ5uuMLizcDWA3E1xcSKGQhnz8F3APW05MB"
        self.endpoint = f"{self.base_url.rstrip('/')}/v1/responses"
        self.session = requests.Session()
        # 是否在解析打印前展示原始JSON响应（调试用）
        self.show_raw_response = False

        if self.api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            })
    
    def print_header(self, title: str):
        """打印标题"""
        print("=" * 80)
        print(f" {title} ".center(80, "="))
        print("=" * 80)
    
    def print_section(self, title: str):
        """打印小节标题"""
        print(f"\n{'=' * 40}")
        print(f" {title} ")
        print("=" * 40)
    
    def get_user_input(self, prompt: str, default: Any = None, input_type: type = str):
        """获取用户输入"""
        if default:
            prompt = f"{prompt} (默认: {default}): "
        else:
            prompt = f"{prompt}: "
        
        user_input = input(prompt).strip()
        if not user_input and default is not None:
            return default
        
        if input_type == bool:
            return user_input.lower() in ['true', 't', 'yes', 'y', '1']
        elif input_type == int:
            try:
                return int(user_input) if user_input else None
            except ValueError:
                print("无效的整数输入，使用默认值")
                return default
        elif input_type == float:
            try:
                return float(user_input) if user_input else None
            except ValueError:
                print("无效的浮点数输入，使用默认值")
                return default
        
        return user_input if user_input else None
    
    def setup_credentials(self):
        """设置API凭证"""
        self.print_section("API 配置设置")
        
        # 设置 Base URL
        new_base_url = self.get_user_input(
            "请输入 API Base URL", 
            self.base_url
        )
        if new_base_url:
            self.base_url = new_base_url
            self.endpoint = f"{self.base_url.rstrip('/')}/v1/responses"
        
        # 设置 API Key
        new_api_key = self.get_user_input(
            "请输入 API Key", 
            "***" if self.api_key else None
        )
        if new_api_key and new_api_key != "***":
            self.api_key = new_api_key
        
        # 更新请求头
        if self.api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            })

        # 是否打印原始JSON响应（调试）
        raw_choice = self.get_user_input("是否在解析打印前输出原始JSON响应? (y/n)", "n")
        if isinstance(raw_choice, str):
            self.show_raw_response = raw_choice.lower() == 'y'
        
        print(f"\n当前配置:")
        print(f"Base URL: {self.base_url}")
        print(f"API Key: {'已设置' if self.api_key else '未设置'}")
        print(f"原始JSON调试输出: {'开启' if self.show_raw_response else '关闭'}")
    
    def build_input_content(self) -> Union[str, List[Dict]]:
        """构建输入内容"""
        self.print_section("输入内容配置")
        
        print("支持的输入类型:")
        print("1. 文本输入")
        print("2. 消息数组 (支持文本、图像、文件)")
        
        choice = self.get_user_input("选择输入类型 (1-2)", "1", int)
        
        if choice == 1:
            # 简单文本输入
            text = self.get_user_input("请输入文本内容", "你好，请介绍一下自己")
            return str(text) if text is not None else "你好，请介绍一下自己"
        
        elif choice == 2:
            # 构建消息数组
            messages = []
            
            while True:
                print(f"\n当前已添加 {len(messages)} 条消息")
                print("消息类型:")
                print("1. 文本消息")
                print("2. 图像消息")
                print("3. 文件消息")
                print("4. 完成输入")
                
                msg_type = self.get_user_input("选择消息类型 (1-4)", "1", int)
                
                if msg_type == 4:
                    break
                
                # 获取角色
                role = self.get_user_input("消息角色 (user/assistant/system/developer)", "user")
                
                if msg_type == 1:
                    # 文本消息
                    text = self.get_user_input("请输入文本内容")
                    if text:
                        messages.append({
                            "role": role,
                            "content": [{"type": "input_text", "text": text}]
                        })
                
                elif msg_type == 2:
                    # 图像消息
                    print("图像输入方式:")
                    print("1. 图像URL")
                    print("2. 文件ID")
                    
                    img_method = self.get_user_input("选择方式 (1-2)", "1", int)
                    detail = self.get_user_input("图像详细程度 (high/low/auto)", "auto")
                    
                    content = [{"type": "input_text", "text": self.get_user_input("图像描述文本", "")}]
                    
                    if img_method == 1:
                        image_url = self.get_user_input("请输入图像URL")
                        if image_url:
                            content.append({
                                "type": "input_image",
                                "image_url": image_url,
                                "detail": detail
                            })
                    else:
                        file_id = self.get_user_input("请输入文件ID")
                        if file_id:
                            content.append({
                                "type": "input_image",
                                "file_id": file_id,
                                "detail": detail
                            })
                    
                    messages.append({"role": role, "content": content})
                
                elif msg_type == 3:
                    # 文件消息
                    print("文件输入方式:")
                    print("1. 文件ID")
                    print("2. 文件数据")
                    
                    file_method = self.get_user_input("选择方式 (1-2)", "1", int)
                    
                    if file_method == 1:
                        file_id = self.get_user_input("请输入文件ID")
                        if file_id:
                            messages.append({
                                "role": role,
                                "content": [{
                                    "type": "input_file",
                                    "file_id": file_id
                                }]
                            })
                    else:
                        filename = self.get_user_input("文件名")
                        file_data = self.get_user_input("文件内容(base64)")
                        if filename and file_data:
                            messages.append({
                                "role": role,
                                "content": [{
                                    "type": "input_file",
                                    "filename": filename,
                                    "file_data": file_data
                                }]
                            })
            
            return messages if messages else "你好，请介绍一下自己"
        
        return "你好，请介绍一下自己"
    
    def build_tools(self) -> List[Dict]:
        """构建工具配置"""
        self.print_section("工具配置")
        
        tools: List[Dict[str, Any]] = []
        
        print("可用工具类型:")
        print("1. 文件搜索 (file_search)")
        print("2. 网络搜索 (web_search_preview)")
        print("3. 函数调用 (function)")
        print("4. 计算机使用 (computer_use_preview)")
        print("5. 不使用工具")
        
        while True:
            choice = self.get_user_input("选择工具类型 (1-5,回车结束)", "5", int)
            
            if choice == 5 or choice is None:
                break
            
            elif choice == 1:
                # 文件搜索工具
                vector_store_ids_raw = str(self.get_user_input("向量存储ID列表(逗号分隔)", "vs_1234567890"))
                vector_store_ids = vector_store_ids_raw.split(',')
                max_results = self.get_user_input("最大结果数 (1-50)", "20", int)
                
                tool: Dict[str, Any] = {
                    "type": "file_search",
                    "vector_store_ids": [id.strip() for id in vector_store_ids],
                    "max_num_results": max_results
                }
                
                # 可选的排名选项
                if self.get_user_input("是否配置排名选项? (y/n)", "n") == 'y':
                    ranker = self.get_user_input("排名器类型", "auto")
                    score_threshold = self.get_user_input("分数阈值 (0-1)", "0.0", float)
                    tool["ranking_options"] = {
                        "ranker": ranker,
                        "score_threshold": score_threshold
                    }
                
                tools.append(tool)
            
            elif choice == 2:
                # 网络搜索工具
                tool: Dict[str, Any] = {"type": "web_search_preview"}
                
                # 可选配置
                context_size = self.get_user_input("搜索上下文大小 (low/medium/high)", "medium")
                if context_size:
                    tool["search_context_size"] = str(context_size)
                
                # 用户位置
                if self.get_user_input("是否设置用户位置? (y/n)", "n") == 'y':
                    city = self.get_user_input("城市")
                    country = self.get_user_input("国家代码 (如 US)")
                    region = self.get_user_input("地区")
                    timezone = self.get_user_input("时区")
                    
                    location = {"type": "approximate"}
                    if city: location["city"] = str(city)
                    if country: location["country"] = str(country)
                    if region: location["region"] = str(region)
                    if timezone: location["timezone"] = str(timezone)
                    
                    tool["user_location"] = location
                
                # 域名限制
                domains = self.get_user_input("限制域名列表(逗号分隔，可选)")
                if domains:
                    tool["domains"] = [d.strip() for d in str(domains).split(',')]
                
                tools.append(tool)
            
            elif choice == 3:
                # 函数调用工具
                name = self.get_user_input("函数名称", "get_weather")
                description = self.get_user_input("函数描述", "获取天气信息")
                
                # 简化的参数定义
                print("请定义函数参数 (JSON Schema格式):")
                print("示例: {\"type\":\"object\",\"properties\":{\"location\":{\"type\":\"string\"}}}")
                
                parameters_str = self.get_user_input("参数定义", 
                    '{"type":"object","properties":{"location":{"type":"string","description":"位置"},"unit":{"type":"string","enum":["celsius","fahrenheit"]}},"required":["location"]}')
                
                try:
                    parameters = json.loads(str(parameters_str))
                except json.JSONDecodeError:
                    print("参数格式错误，使用默认参数")
                    parameters = {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "查询参数"}
                        },
                        "required": ["query"]
                    }
                
                strict = self.get_user_input("是否启用严格模式? (y/n)", "y", bool)
                
                tools.append({
                    "type": "function",
                    "name": name,
                    "description": description,
                    "parameters": parameters,
                    "strict": strict
                })
            
            elif choice == 4:
                # 计算机使用工具
                width = self.get_user_input("显示器宽度", "1024", int)
                height = self.get_user_input("显示器高度", "768", int)
                environment = self.get_user_input("环境类型", "ubuntu")
                
                tools.append({
                    "type": "computer_use_preview",
                    "display_width": width,
                    "display_height": height,
                    "environment": environment
                })
        
        return tools
    
    def build_text_format(self) -> Dict:
        """构建文本格式配置"""
        self.print_section("文本格式配置")
        
        print("支持的格式类型:")
        print("1. 普通文本 (text)")
        print("2. JSON对象 (json_object)")
        print("3. JSON Schema (json_schema)")
        
        choice = self.get_user_input("选择格式类型 (1-3)", "1", int)
        
        if choice == 1:
            return {"format": {"type": "text"}}
        
        elif choice == 2:
            return {"format": {"type": "json_object"}}
        
        elif choice == 3:
            name = self.get_user_input("Schema名称", "response_format")
            description = self.get_user_input("Schema描述", "响应格式")
            
            print("请定义JSON Schema:")
            print("示例: {\"type\":\"object\",\"properties\":{\"answer\":{\"type\":\"string\"}}}")
            
            schema_str = self.get_user_input("Schema定义",
                '{"type":"object","properties":{"answer":{"type":"string"},"confidence":{"type":"number"}},"required":["answer"]}')
            
            try:
                schema = json.loads(str(schema_str))
            except json.JSONDecodeError:
                print("Schema格式错误，使用默认Schema")
                schema = {
                    "type": "object",
                    "properties": {
                        "response": {"type": "string"}
                    },
                    "required": ["response"]
                }
            
            strict = self.get_user_input("是否启用严格模式? (y/n)", "false", bool)
            
            return {
                "format": {
                    "type": "json_schema",
                    "name": name,
                    "description": description,
                    "schema": schema,
                    "strict": strict
                }
            }
        
        return {"format": {"type": "text"}}
    
    def build_reasoning(self) -> Optional[Dict]:
        """构建推理配置 (仅适用于o系列模型)"""
        self.print_section("推理配置 (O系列模型)")
        
        if self.get_user_input("是否配置推理参数? (y/n)", "n") != 'y':
            return None
        
        reasoning = {}
        
        effort = self.get_user_input("推理努力程度 (low/medium/high)", "medium")
        if effort:
            reasoning["effort"] = effort
        
        summary = self.get_user_input("推理摘要类型 (auto/concise/detailed)")
        if summary:
            reasoning["summary"] = summary
        
        return reasoning if reasoning else None
    
    def build_full_request(self) -> Dict:
        """构建完整的请求参数"""
        self.print_header("构建完整API请求")
        
        # 基础参数
        request_data = {}
        
        # 必需参数
        model = self.get_user_input("模型名称", "gpt-4.1-mini")
        request_data["model"] = model
        
        # 输入内容
        request_data["input"] = self.build_input_content()
        
        # 可选参数
        instructions = self.get_user_input("系统指令")
        if instructions:
            request_data["instructions"] = instructions
        
        max_tokens = self.get_user_input("最大输出token数", None, int)
        if max_tokens:
            request_data["max_output_tokens"] = max_tokens
        
        temperature = self.get_user_input("采样温度 (0-2)", "1.0", float)
        if temperature is not None:
            request_data["temperature"] = temperature
        
        top_p = self.get_user_input("Top-p采样 (0-1)", "1.0", float)
        if top_p is not None:
            request_data["top_p"] = top_p
        
        # 工具配置
        tools = self.build_tools()
        if tools:
            request_data["tools"] = tools
            
            # 工具选择策略
            print("工具选择策略:")
            print("1. auto - 自动选择")
            print("2. none - 不使用工具")
            print("3. required - 必须使用工具")
            print("4. 指定特定工具")
            
            tool_choice_type = self.get_user_input("选择策略 (1-4)", "1", int)
            
            if tool_choice_type == 1:
                request_data["tool_choice"] = "auto"
            elif tool_choice_type == 2:
                request_data["tool_choice"] = "none"
            elif tool_choice_type == 3:
                request_data["tool_choice"] = "required"
            elif tool_choice_type == 4:
                tool_name = self.get_user_input("工具名称")
                tool_type = self.get_user_input("工具类型")
                if tool_name and tool_type:
                    request_data["tool_choice"] = {
                        "type": tool_type,
                        "name": tool_name
                    }
        
        # 文本格式
        text_format = self.build_text_format()
        if text_format:
            request_data["text"] = text_format
        
        # 推理配置
        reasoning = self.build_reasoning()
        if reasoning:
            request_data["reasoning"] = reasoning
        
        # 其他可选参数
        self.print_section("其他可选参数")
        
        stream = self.get_user_input("是否使用流式响应? (y/n)", "n", bool)
        request_data["stream"] = stream
        
        store = self.get_user_input("是否存储响应? (y/n)", "y", bool)
        request_data["store"] = store
        
        parallel_tools = self.get_user_input("是否启用并行工具调用? (y/n)", "y", bool)
        request_data["parallel_tool_calls"] = parallel_tools
        
        previous_response_id = self.get_user_input("前一个响应ID(多轮对话)")
        if previous_response_id:
            request_data["previous_response_id"] = previous_response_id
        
        user_id = self.get_user_input("用户ID")
        if user_id:
            request_data["user"] = user_id
        
        # 截断策略
        truncation = self.get_user_input("截断策略 (auto/disabled)", "disabled")
        request_data["truncation"] = truncation
        
        # 服务层级
        service_tier = self.get_user_input("服务层级 (auto/default/flex)", "auto")
        request_data["service_tier"] = service_tier
        
        # 包含选项
        include_options = self.get_user_input("包含选项 (逗号分隔，可选)")
        if include_options:
            request_data["include"] = [opt.strip() for opt in str(include_options).split(',')]
        
        # 元数据
        if self.get_user_input("是否添加元数据? (y/n)", "n") == 'y':
            metadata = {}
            while True:
                key = self.get_user_input("元数据键 (回车结束)")
                if not key:
                    break
                value = self.get_user_input(f"元数据值 ({key})")
                if value:
                    metadata[key] = value
            if metadata:
                request_data["metadata"] = metadata
        
        return request_data
    
    def send_request(self, request_data: Dict, show_request: bool = True) -> Optional[Dict]:
        """发送API请求"""
        if show_request:
            self.print_section("发送API请求")
            print("请求URL:", self.endpoint)
            print("请求数据:")
            print(json.dumps(request_data, indent=2, ensure_ascii=False))
        
        if not self.api_key:
            print("错误: 未设置API Key")
            return None
        
        try:
            if request_data.get("stream", False):
                # 流式响应
                response = self.session.post(
                    self.endpoint,
                    json=request_data,
                    stream=True,
                    headers={"Accept": "text/event-stream"}
                )
                response.raise_for_status()
                
                print("\n流式响应:")
                print("-" * 40)
                
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data = line[6:]  # 移除 'data: ' 前缀
                            if data.strip() == '[DONE]':
                                break
                            try:
                                event_data = json.loads(data)
                                print(f"事件: {event_data.get('type', 'unknown')}")
                                if 'delta' in event_data:
                                    print(f"内容: {event_data['delta']}", end='', flush=True)
                                elif 'response' in event_data:
                                    print(f"响应状态: {event_data['response'].get('status', 'unknown')}")
                            except json.JSONDecodeError:
                                print(f"原始数据: {data}")
                
                print("\n流式响应结束")
                return {"stream": True, "status": "completed"}
            
            else:
                # 普通响应
                response = self.session.post(self.endpoint, json=request_data)
                response.raise_for_status()
                return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    print("错误详情:", json.dumps(error_detail, indent=2, ensure_ascii=False))
                except:
                    print("错误响应:", e.response.text)
            return None
        except Exception as e:
            print(f"未知错误: {e}")
            return None
    
    def display_response(self, response: Dict):
        """显示响应结果"""
        if response.get("stream"):
            return  # 流式响应已经在send_request中显示
        
        # 先显示原始JSON响应（调试）
        self.print_section("原始JSON响应 (调试)")
        try:
            print(json.dumps(response, indent=2, ensure_ascii=False))
        except Exception:
            print(str(response))

        self.print_section("API响应结果")
        
        print(f"响应ID: {response.get('id', 'N/A')}")
        print(f"状态: {response.get('status', 'N/A')}")
        print(f"模型: {response.get('model', 'N/A')}")
        print(f"创建时间: {datetime.fromtimestamp(response.get('created_at', 0)).strftime('%Y-%m-%d %H:%M:%S') if response.get('created_at') else 'N/A'}")
        
        # 显示使用统计
        if 'usage' in response:
            usage = response['usage']
            print(f"\nToken使用统计:")
            print(f"  输入: {usage.get('input_tokens', 0)}")
            print(f"  输出: {usage.get('output_tokens', 0)}")
            print(f"  总计: {usage.get('total_tokens', 0)}")
            
            if 'output_tokens_details' in usage:
                details = usage['output_tokens_details']
                if details.get('reasoning_tokens', 0) > 0:
                    print(f"  推理: {details['reasoning_tokens']}")
        
        # 显示输出内容
        if 'output' in response:
            print(f"\n输出内容:")
            for i, output_item in enumerate(response['output']):
                print(f"\n输出项 {i+1}:")
                print(f"  类型: {output_item.get('type', 'unknown')}")
                
                if output_item.get('type') == 'message':
                    print(f"  角色: {output_item.get('role', 'unknown')}")
                    if 'content' in output_item:
                        for j, content in enumerate(output_item['content']):
                            print(f"  内容 {j+1}: {content.get('type', 'unknown')}")
                            if content.get('type') == 'output_text':
                                print(f"  文本: {content.get('text', '')}")
                                
                                # 显示注释
                                if content.get('annotations'):
                                    print(f"  注释:")
                                    for annotation in content['annotations']:
                                        if annotation.get('type') == 'file_citation':
                                            print(f"    文件引用: {annotation.get('filename', 'N/A')}")
                                        elif annotation.get('type') == 'url_citation':
                                            print(f"    URL引用: {annotation.get('title', 'N/A')} - {annotation.get('url', 'N/A')}")

                elif output_item.get('type') == 'reasoning':
                    # 对 reasoning 类型进行更友好的说明
                    summary = output_item.get('summary')
                    if summary is not None:
                        print("  摘要:")
                        try:
                            print("    " + (summary if isinstance(summary, str) else json.dumps(summary, ensure_ascii=False)))
                        except Exception:
                            print("    (无法展示的摘要内容)")
                    # 这些模型不会返回完整思考过程
                    if any(k in output_item for k in ['encrypted_content', 'redacted', 'hidden']):
                        print("  说明: 模型的思考过程不会以明文返回（可能被加密或省略）。详见上方原始JSON。")
                    else:
                        print("  说明: 此输出项为推理占位信息；大多数推理模型不会返回完整‘思考内容’。如需调试请查看上方原始JSON。")
                
                elif output_item.get('type') == 'function_call':
                    print(f"  函数名: {output_item.get('name', 'unknown')}")
                    print(f"  参数: {output_item.get('arguments', 'N/A')}")
                    print(f"  调用ID: {output_item.get('call_id', 'N/A')}")
                
                elif output_item.get('type') in ['file_search_call', 'web_search_call']:
                    print(f"  调用ID: {output_item.get('id', 'N/A')}")
                    print(f"  状态: {output_item.get('status', 'N/A')}")
                    if 'queries' in output_item:
                        print(f"  查询: {output_item['queries']}")
        
        # 显示推理信息
        if 'reasoning' in response:
            reasoning = response['reasoning']
            print(f"\n推理信息:")
            print(f"  努力程度: {reasoning.get('effort', 'N/A')}")
            if reasoning.get('summary'):
                print(f"  摘要: {reasoning['summary']}")
            # 友好提示
            if any(k in reasoning for k in ['encrypted_content', 'redacted', 'hidden']):
                print("  说明: 详细思考过程未公开返回（被加密/省略）。")
        
        # 显示错误信息
        if response.get('error'):
            print(f"\n错误信息:")
            print(json.dumps(response['error'], indent=2, ensure_ascii=False))
    
    def run_preset_examples(self):
        """运行预设示例"""
        self.print_header("预设示例演示")
        
        examples = {
            "1": {
                "name": "基础文本响应",
                "data": {
                    "model": "gpt-4.1-mini",
                    "input": "讲一个三句话的关于独角兽的睡前故事。"
                }
            },
            "2": {
                "name": "网络搜索工具",
                "data": {
                    "model": "gpt-4.1-mini",
                    "tools": [{"type": "web_search_preview"}],
                    "input": "今天有什么积极正面的新闻?"
                }
            },
            "3": {
                "name": "函数调用示例",
                "data": {
                    "model": "gpt-4.1-mini",
                    "input": "波士顿今天的天气如何？",
                    "tools": [{
                        "type": "function",
                        "name": "get_current_weather",
                        "description": "获取指定位置的当前天气",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {
                                    "type": "string",
                                    "description": "城市和州，例如 San Francisco, CA"
                                },
                                "unit": {
                                    "type": "string",
                                    "enum": ["celsius", "fahrenheit"]
                                }
                            },
                            "required": ["location", "unit"]
                        }
                    }],
                    "tool_choice": "auto"
                }
            },
            "4": {
                "name": "推理能力示例 (O系列模型)",
                "data": {
                    "model": "ali-gpt-5-mini",
                    "input": "把大象装进冰箱需要几步?",
                    "reasoning": {
                        "effort": "low",
                        "summary": "auto"
                    }
                }
            },
            "5": {
                "name": "流式响应示例",
                "data": {
                    "model": "gpt-4.1-mini",
                    "instructions": "你是一个有帮助的助手。",
                    "input": "你好！",
                    "stream": True
                }
            },
            "6": {
                "name": "JSON Schema格式化输出",
                "data": {
                    "model": "gpt-4.1-mini",
                    "input": "分析以下文本的情感并给出置信度：'今天天气真好，心情很不错！'",
                    "text": {
                        "format": {
                            "type": "json_schema",
                            "name": "sentiment_analysis",
                            "description": "情感分析结果",
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "sentiment": {
                                        "type": "string",
                                        "enum": ["positive", "negative", "neutral"]
                                    },
                                    "confidence": {
                                        "type": "number",
                                        "minimum": 0,
                                        "maximum": 1
                                    },
                                    "explanation": {
                                        "type": "string"
                                    }
                                },
                                "required": ["sentiment", "confidence", "explanation"]
                            },
                            "strict": True
                        }
                    }
                }
            },
            "7": {
                "name": "文件搜索工具",
                "data": {
                    "model": "gpt-4.1-mini",
                    "tools": [{
                        "type": "file_search",
                        "vector_store_ids": ["vs_1234567890"],
                        "max_num_results": 20
                    }],
                    "input": "古代棕龙有哪些特性和属性?"
                }
            },
            "8": {
                "name": "图像分析示例",
                "data": {
                    "model": "gpt-4.1-mini",
                    "input": [{
                        "role": "user",
                        "content": [
                            {"type": "input_text", "text": "描述这张图片中的内容"},
                            {
                                "type": "input_image",
                                "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                                "detail": "high"
                            }
                        ]
                    }]
                }
            }
        }
        
        while True:
            print("\n可用的预设示例:")
            for key, example in examples.items():
                print(f"{key}. {example['name']}")
            print("0. 返回主菜单")
            
            choice = self.get_user_input("选择示例 (0-8)", "0")
            
            if choice == "0":
                break
            
            if choice in examples:
                example = examples[choice]
                print(f"\n正在运行示例: {example['name']}")
                print("=" * 50)
                
                response = self.send_request(example['data'])
                if response:
                    self.display_response(response)
                
                input("\n按回车键继续...")
            else:
                print("无效选择，请重试")
    
    def interactive_mode(self):
        """交互式模式"""
        self.print_header("交互式API测试")
        
        while True:
            print("\n交互式测试选项:")
            print("1. 快速测试 (基础文本)")
            print("2. 自定义完整请求")
            print("3. 返回主菜单")
            
            choice = self.get_user_input("选择选项 (1-3)", "1", int)
            
            if choice == 1:
                # 快速测试
                model = self.get_user_input("模型名称", "gpt-4.1-mini")
                text = self.get_user_input("输入文本", "你好，请介绍一下自己")
                stream = self.get_user_input("是否流式响应? (y/n)", "n", bool)
                
                request_data = {
                    "model": model,
                    "input": text,
                    "stream": stream
                }
                
                response = self.send_request(request_data)
                if response:
                    self.display_response(response)
            
            elif choice == 2:
                # 自定义完整请求
                request_data = self.build_full_request()
                response = self.send_request(request_data)
                if response:
                    self.display_response(response)
            
            elif choice == 3:
                break
            
            input("\n按回车键继续...")
    
    def show_documentation(self):
        """显示API文档说明"""
        self.print_header("API 文档说明")
        
        sections = {
            "1": "基础概念",
            "2": "请求参数详解",
            "3": "输入类型说明",
            "4": "工具类型详解",
            "5": "响应格式说明",
            "6": "错误处理",
            "7": "最佳实践"
        }
        
        while True:
            print("\n文档章节:")
            for key, title in sections.items():
                print(f"{key}. {title}")
            print("0. 返回主菜单")
            
            choice = self.get_user_input("选择章节 (0-7)", "0")
            
            if choice == "0":
                break
            elif choice == "1":
                self.show_basic_concepts()
            elif choice == "2":
                self.show_parameters_guide()
            elif choice == "3":
                self.show_input_types()
            elif choice == "4":
                self.show_tools_guide()
            elif choice == "5":
                self.show_response_format()
            elif choice == "6":
                self.show_error_handling()
            elif choice == "7":
                self.show_best_practices()
            
            input("\n按回车键继续...")
    
    def show_basic_concepts(self):
        """显示基础概念"""
        self.print_section("基础概念")
        
        concepts = """
OpenAI Responses API 基础概念:

1. 响应对象 (Response Object)
   - 代表模型的一次完整响应
   - 包含输入、输出、使用统计等信息
   - 支持多轮对话状态管理

2. 输入类型
   - 文本输入: 简单字符串
   - 消息数组: 支持多种内容类型的结构化输入
   - 支持角色: user, assistant, system, developer

3. 输出类型
   - 消息输出: 模型生成的文本响应
   - 工具调用: 函数调用、文件搜索等
   - 推理内容: O系列模型的思考过程

4. 工具系统
   - 内置工具: 文件搜索、网络搜索、计算机使用
   - 自定义函数: 用户定义的函数调用
   - 并行调用: 同时调用多个工具

5. 流式响应
   - 实时获取生成内容
   - 服务器发送事件 (SSE) 格式
   - 支持增量更新

6. 推理能力 (O系列模型)
   - 显式思考过程
   - 可配置推理努力程度
   - 推理摘要生成
        """
        print(concepts)
    
    def show_parameters_guide(self):
        """显示参数详解"""
        self.print_section("请求参数详解")
        
        guide = """
核心参数:

必需参数:
- model: 模型名称 (如 gpt-4.1-mini, o1-mini)
- input: 输入内容 (字符串或消息数组)

文本生成控制:
- temperature: 采样温度 (0-2), 控制随机性
- top_p: 核采样阈值 (0-1), 与temperature二选一
- max_output_tokens: 最大输出token数

格式控制:
- text.format: 输出格式 (text/json_object/json_schema)
- stream: 是否流式响应 (true/false)

工具相关:
- tools: 工具列表 (数组)
- tool_choice: 工具选择策略 (auto/none/required/指定工具)
- parallel_tool_calls: 是否并行调用工具

对话管理:
- instructions: 系统指令
- previous_response_id: 前一个响应ID (多轮对话)

推理配置 (O系列):
- reasoning.effort: 推理努力程度 (low/medium/high)
- reasoning.summary: 推理摘要类型

其他选项:
- store: 是否存储响应
- user: 用户标识符
- metadata: 元数据键值对
- truncation: 截断策略
- service_tier: 服务层级
- include: 包含附加数据
        """
        print(guide)
    
    def show_input_types(self):
        """显示输入类型说明"""
        self.print_section("输入类型说明")
        
        types_info = """
1. 简单文本输入:
   input: "你好，请介绍一下自己"

2. 消息数组输入:
   input: [
     {
       "role": "user",
       "content": [
         {"type": "input_text", "text": "分析这张图片"},
         {"type": "input_image", "image_url": "https://..."}
       ]
     }
   ]

3. 内容类型:
   
   文本内容:
   {"type": "input_text", "text": "文本内容"}
   
   图像内容:
   {
     "type": "input_image",
     "image_url": "图像URL或base64",
     "detail": "high/low/auto"
   }
   或
   {
     "type": "input_image", 
     "file_id": "文件ID",
     "detail": "high/low/auto"
   }
   
   文件内容:
   {
     "type": "input_file",
     "file_id": "文件ID"
   }
   或
   {
     "type": "input_file",
     "filename": "文件名",
     "file_data": "base64编码内容"
   }

4. 消息角色:
   - user: 用户消息
   - assistant: 助手消息 (用于多轮对话)
   - system: 系统消息
   - developer: 开发者消息
        """
        print(types_info)
    
    def show_tools_guide(self):
        """显示工具详解"""
        self.print_section("工具类型详解")
        
        tools_info = """
1. 文件搜索工具 (file_search):
   {
     "type": "file_search",
     "vector_store_ids": ["vs_xxx"],
     "max_num_results": 20,
     "ranking_options": {
       "ranker": "auto",
       "score_threshold": 0.0
     },
     "filters": {
       "type": "and",
       "filters": [...]
     }
   }

2. 网络搜索工具 (web_search_preview):
   {
     "type": "web_search_preview",
     "search_context_size": "medium",
     "user_location": {
       "type": "approximate",
       "city": "San Francisco",
       "country": "US"
     },
     "domains": ["example.com"]
   }

3. 函数调用工具 (function):
   {
     "type": "function",
     "name": "get_weather",
     "description": "获取天气信息",
     "parameters": {
       "type": "object",
       "properties": {
         "location": {"type": "string"}
       },
       "required": ["location"]
     },
     "strict": true
   }

4. 计算机使用工具 (computer_use_preview):
   {
     "type": "computer_use_preview",
     "display_width": 1024,
     "display_height": 768,
     "environment": "ubuntu"
   }

工具选择策略:
- "auto": 模型自动决定是否使用工具
- "none": 不使用任何工具
- "required": 必须使用至少一个工具
- {"type": "function", "name": "函数名"}: 强制使用指定工具
        """
        print(tools_info)
    
    def show_response_format(self):
        """显示响应格式说明"""
        self.print_section("响应格式说明")
        
        format_info = """
响应对象结构:
{
  "id": "resp_xxx",              // 响应唯一ID
  "object": "response",          // 对象类型
  "created_at": 1234567890,      // 创建时间戳
  "status": "completed",         // 状态
  "model": "gpt-4.1-mini",             // 使用的模型
  "output": [...],              // 输出内容数组
  "usage": {...},               // Token使用统计
  "error": null,                // 错误信息
  // ... 其他字段
}

输出类型:

1. 消息输出:
   {
     "type": "message",
     "role": "assistant",
     "content": [
       {
         "type": "output_text",
         "text": "响应文本",
         "annotations": [...]
       }
     ]
   }

2. 工具调用输出:
   {
     "type": "function_call",
     "name": "函数名",
     "arguments": "JSON参数",
     "call_id": "调用ID"
   }

3. 推理输出 (O系列):
   {
     "type": "reasoning",
     "summary": [...],
     "encrypted_content": "加密内容"
   }

注释类型:
- file_citation: 文件引用
- url_citation: URL引用  
- file_path: 文件路径

使用统计:
{
  "input_tokens": 100,
  "output_tokens": 50,
  "total_tokens": 150,
  "output_tokens_details": {
    "reasoning_tokens": 20
  }
}
        """
        print(format_info)
    
    def show_error_handling(self):
        """显示错误处理"""
        self.print_section("错误处理")
        
        error_info = """
常见错误类型:

1. 认证错误 (401):
   - API密钥无效或过期
   - 权限不足

2. 请求错误 (400):
   - 参数格式错误
   - 必需参数缺失
   - 参数值超出范围

3. 上下文超限 (400):
   - 输入内容超过模型上下文窗口
   - 建议使用truncation参数

4. 资源不存在 (404):
   - 文件ID不存在
   - 向量存储ID无效

5. 速率限制 (429):
   - 请求频率过高
   - 建议添加重试机制

6. 服务器错误 (5xx):
   - 服务暂时不可用
   - 建议使用指数退避重试

错误响应格式:
{
  "error": {
    "type": "invalid_request_error",
    "code": "parameter_invalid",
    "message": "详细错误信息",
    "param": "错误参数名"
  }
}

推荐的错误处理策略:
1. 验证输入参数
2. 实现重试机制
3. 记录错误日志
4. 提供用户友好的错误信息
5. 监控API使用情况
        """
        print(error_info)
    
    def show_best_practices(self):
        """显示最佳实践"""
        self.print_section("最佳实践")
        
        practices = """
API使用最佳实践:

1. 参数优化:
   - 合理设置temperature和top_p
   - 根据需要设置max_output_tokens
   - 使用适当的model选择

2. 输入设计:
   - 清晰明确的prompt
   - 合理的instructions设置
   - 适当的输入格式

3. 工具使用:
   - 只添加必要的工具
   - 正确配置工具参数
   - 合理设置tool_choice

4. 性能优化:
   - 使用适当的service_tier
   - 合理设置truncation策略
   - 考虑使用并行工具调用

5. 错误处理:
   - 实现重试机制
   - 处理各种错误类型
   - 监控API使用情况

6. 成本控制:
   - 监控token使用
   - 设置合理的max_output_tokens
   - 选择适合的模型

7. 安全考虑:
   - 保护API密钥
   - 验证用户输入
   - 设置适当的user标识

8. 多轮对话:
   - 正确使用previous_response_id
   - 管理对话状态
   - 合理设置store参数

9. 结构化输出:
   - 使用JSON Schema确保格式
   - 启用strict模式
   - 验证输出结构

10. 调试建议:
    - 使用include参数获取详细信息
    - 查看推理过程 (O系列模型)
    - 分析token使用情况
        """
        print(practices)
    
    def main_menu(self):
        """主菜单"""
        while True:
            self.print_header("OpenAI /v1/responses API 交互式演示程序")
            
            print("当前配置:")
            print(f"  Base URL: {self.base_url}")
            print(f"  API Key: {'已设置' if self.api_key else '未设置'}")
            
            print("\n功能菜单:")
            print("1. 配置API设置")
            print("2. 预设示例演示")
            print("3. 交互式API测试")
            print("4. API文档说明")
            print("5. 退出程序")
            
            choice = self.get_user_input("请选择功能 (1-5)", "1", int)
            
            if choice == 1:
                self.setup_credentials()
            elif choice == 2:
                self.run_preset_examples()
            elif choice == 3:
                self.interactive_mode()
            elif choice == 4:
                self.show_documentation()
            elif choice == 5:
                print("感谢使用！")
                break
            else:
                print("无效选择，请重试")


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="OpenAI /v1/responses API 交互式演示程序")
    parser.add_argument("--base-url", help="API基础URL")
    parser.add_argument("--api-key", help="API密钥")
    parser.add_argument("--example", help="直接运行指定示例 (1-8)")
    parser.add_argument("--show-raw", action="store_true", help="在解析前打印原始JSON响应")
    
    args = parser.parse_args()
    
    # 创建演示程序实例
    demo = OpenAIResponsesDemo(
        base_url=args.base_url,
        api_key=args.api_key
    )
    # 从命令行开关设置原始JSON打印
    if getattr(args, 'show_raw', False):
        demo.show_raw_response = True
    
    if args.example:
        # 直接运行指定示例
        demo.setup_credentials()
        demo.run_preset_examples()
    else:
        # 运行交互式菜单
        demo.main_menu()


if __name__ == "__main__":
    main()