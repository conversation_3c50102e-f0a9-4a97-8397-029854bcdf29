import secrets
from datetime import datetime, timedelta
from typing import Dict, Optional
from fastapi import HTTP<PERSON>xception, Depends, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from config import get_timestamp

try:
    import jwt
except ImportError:
    raise ImportError("缺少JWT依赖包，请运行: pip install PyJWT")

security = HTTPBearer(auto_error=False)

# 会话管理
active_sessions = {}  # {token_id: {"created_at": datetime, "last_used": datetime, "ip": str}}
failed_attempts = {}  # {ip: {"count": int, "locked_until": datetime}}

def generate_tokens(user_id: str, ip_address: str, config: Dict) -> dict:
    """生成访问令牌和刷新令牌"""
    jwt_config = config.get('jwt_config', {})
    
    # 安全检查：确保 secret_key 存在且为字符串
    secret_key = jwt_config.get('secret_key')
    if not secret_key or not isinstance(secret_key, str):
        raise HTTPException(
            status_code=500, 
            detail="JWT配置错误：secret_key缺失或无效，请检查config.json中的jwt_config.secret_key配置"
        )
    
    session_id = secrets.token_urlsafe(16)
    now = datetime.utcnow()
    
    # 根据用户类型设置不同的过期时间
    if user_id == 'client':
        # 客户端令牌过期时间更长 - 3天
        expire_hours = jwt_config.get('client_token_expire_hours', 72)
        access_expire_time = now + timedelta(hours=expire_hours)
        expires_in = expire_hours * 3600
    else:
        # 管理员令牌
        expire_minutes = jwt_config.get('access_token_expire_minutes', 120)
        access_expire_time = now + timedelta(minutes=expire_minutes)
        expires_in = expire_minutes * 60
    
    access_payload = {
        "sub": user_id,
        "session_id": session_id,
        "iat": now,
        "exp": access_expire_time,
        "type": "access",
        "ip": ip_address
    }
    
    refresh_payload = {
        "sub": user_id,
        "session_id": session_id,
        "iat": now,
        "exp": now + timedelta(days=jwt_config.get('refresh_token_expire_days', 7)),
        "type": "refresh",
        "ip": ip_address
    }
    
    algorithm = jwt_config.get('algorithm', 'HS256')
    access_token = jwt.encode(access_payload, secret_key, algorithm)
    refresh_token = jwt.encode(refresh_payload, secret_key, algorithm)
    
    active_sessions[session_id] = {
        "created_at": now,
        "last_used": now,
        "ip": ip_address,
        "user_id": user_id
    }
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": expires_in
    }

def verify_token(token: str, ip_address: str, config: Dict) -> dict:
    """验证令牌"""
    try:
        jwt_config = config.get('jwt_config', {})
        
        # 安全检查：确保 secret_key 存在且为字符串
        secret_key = jwt_config.get('secret_key')
        if not secret_key or not isinstance(secret_key, str):
            raise HTTPException(
                status_code=500, 
                detail="JWT配置错误：secret_key缺失或无效，请检查config.json中的jwt_config.secret_key配置"
            )
        
        # 解码令牌
        algorithm = jwt_config.get('algorithm', 'HS256')
        payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        
        # 检查令牌类型
        if payload.get('type') != 'access':
            raise HTTPException(status_code=401, detail="无效的令牌类型")
        
        # 检查会话是否存在
        session_id = payload.get('session_id')
        if session_id and session_id in active_sessions:
            # 更新最后使用时间
            active_sessions[session_id]['last_used'] = datetime.utcnow()
        
        return payload
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="令牌已过期")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="无效的令牌")

def check_login_attempts(ip_address: str, config: Dict) -> bool:
    """检查登录尝试次数"""
    security_config = config.get('security_config', {})
    
    if ip_address in failed_attempts:
        attempt_info = failed_attempts[ip_address]
        
        # 检查是否还在锁定期
        if 'locked_until' in attempt_info and datetime.utcnow() < attempt_info['locked_until']:
            return False
        
        # 检查尝试次数
        if attempt_info.get('count', 0) >= security_config.get('max_login_attempts', 7):
            # 锁定账户
            failed_attempts[ip_address]['locked_until'] = datetime.utcnow() + timedelta(
                minutes=security_config.get('lockout_duration_minutes', 15)
            )
            return False
    
    return True

def record_failed_attempt(ip_address: str):
    """记录失败的登录尝试"""
    if ip_address not in failed_attempts:
        failed_attempts[ip_address] = {"count": 0}
    
    failed_attempts[ip_address]['count'] += 1

def clear_failed_attempts(ip_address: str):
    """清除失败的登录尝试记录"""
    if ip_address in failed_attempts:
        del failed_attempts[ip_address]

def validate_password_strength(password: str, config: Dict) -> bool:
    """验证密码强度"""
    security_config = config.get('security_config', {})
    
    if not security_config.get('require_strong_password', True):
        return len(password) >= security_config.get('password_min_length', 12)
    
    # 强密码要求：至少12位，包含大小写字母、数字、特殊字符
    if len(password) < security_config.get('password_min_length', 12):
        return False
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    return has_upper and has_lower and has_digit and has_special

def create_auth_dependency(config_getter):
    """创建认证依赖的工厂函数"""
    
    def verify_api_key(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)):
        """验证API密钥"""
        config = config_getter()
        api_config = config.get('api_config', {})
        
        # 如果不要求认证，直接通过
        if not api_config.get('require_auth', True):
            return True
        
        if not credentials:
            raise HTTPException(
                status_code=401, 
                detail="需要API密钥认证，请在请求头中添加 Authorization: Bearer your-api-key"
            )
        
        expected_key = api_config.get('api_key', '')
        if credentials.credentials != expected_key:
            raise HTTPException(
                status_code=401, 
                detail="无效的API密钥"
            )
        
        return True

    def verify_client_token(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)):
        """验证客户端令牌"""
        config = config_getter()
        
        if not credentials:
            raise HTTPException(
                status_code=401, 
                detail="需要客户端认证，请先登录",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # 获取客户端IP
        ip_address = request.client.host if request.client else "unknown"
        
        # 验证客户端令牌
        try:
            payload = verify_token(credentials.credentials, ip_address, config)
            
            # 检查是否是客户端令牌
            if payload.get('sub') != 'client':
                raise HTTPException(
                    status_code=401, 
                    detail="无效的客户端令牌",
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            return payload
            
        except HTTPException:
            raise
        except Exception as e:
            print(f"[{get_timestamp()}] 客户端令牌验证异常: {e}")
            raise HTTPException(
                status_code=401, 
                detail="令牌验证失败",
                headers={"WWW-Authenticate": "Bearer"}
            )

    def verify_admin_token(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)):
        """验证管理员令牌"""
        config = config_getter()
        
        if not credentials:
            raise HTTPException(status_code=401, detail="需要认证")
        
        # 获取客户端IP
        ip_address = request.client.host if request.client else "unknown"
        
        # 验证令牌
        payload = verify_token(credentials.credentials, ip_address, config)
        
        return payload
    
    return verify_api_key, verify_client_token, verify_admin_token 