import json
import os
from datetime import datetime
from pathlib import Path
import mimetypes
from config import get_timestamp

STATS_FILE = "stats.json"

# 全局统计数据
stats = {
    "total_requests": 0,
    "successful_requests": 0,
    "failed_requests": 0,
    "review_blocked": 0,
    "start_time": datetime.now().isoformat()
}

def update_image_stats(config: dict):
    """更新图片统计信息"""
    try:
        upload_dir = Path(config.get('image_upload', {}).get('local_storage_path', 'uploads'))
        if not upload_dir.exists():
            stats['image_stats'] = {
                "total_files": 0,
                "total_size": 0,
                "total_size_mb": 0,
                "file_types": {}
            }
            return
        
        total_files = 0
        total_size = 0
        file_types = {}
        
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                # 检查是否是图片文件
                mime_type, _ = mimetypes.guess_type(str(file_path))
                if mime_type and mime_type.startswith('image/'):
                    total_files += 1
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    
                    # 统计文件类型
                    file_ext = file_path.suffix.lower()
                    if file_ext in file_types:
                        file_types[file_ext] += 1
                    else:
                        file_types[file_ext] = 1
        
        stats['image_stats'] = {
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "file_types": file_types
        }
        
    except Exception as e:
        print(f"更新图片统计失败: {e}")
        stats['image_stats'] = {
            "total_files": 0,
            "total_size": 0,
            "total_size_mb": 0,
            "file_types": {}
        }

def load_stats():
    """加载统计数据"""
    global stats
    if os.path.exists(STATS_FILE):
        try:
            with open(STATS_FILE, 'r', encoding='utf-8') as f:
                loaded_stats = json.load(f)
                stats.update(loaded_stats)
        except Exception as e:
            print(f"[{get_timestamp()}] 加载统计文件失败: {e}")

def save_stats():
    """保存统计数据"""
    try:
        with open(STATS_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"[{get_timestamp()}] 保存统计文件失败: {e}")

def increment_total_requests():
    """增加总请求数"""
    global stats
    stats['total_requests'] += 1

def increment_successful_requests():
    """增加成功请求数"""
    global stats
    stats['successful_requests'] += 1

def increment_failed_requests():
    """增加失败请求数"""
    global stats
    stats['failed_requests'] += 1

def increment_review_blocked():
    """增加审查拦截数"""
    global stats
    stats['review_blocked'] += 1

def reset_stats():
    """重置统计数据"""
    global stats
    stats = {
        "total_requests": 0,
        "successful_requests": 0,
        "failed_requests": 0,
        "review_blocked": 0,
        "start_time": datetime.now().isoformat()
    }

def get_stats() -> dict:
    """获取统计数据"""
    return stats.copy() 