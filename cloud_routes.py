"""
云端存储API路由
提供对话云端存储的所有接口，支持云端组分类存储和JWT验证
"""

from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.security import HTTPAuthorizationCredentials
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
import json
from datetime import datetime
from cloud_storage import cloud_storage
from auth import create_auth_dependency
from config import load_config

# 创建认证依赖
config = load_config()
_, verify_client_token, _ = create_auth_dependency(lambda: config)

# 创建路由器
cloud_router = APIRouter(prefix="/cloud", tags=["cloud_storage"])

# 请求模型
class ConversationData(BaseModel):
    title: str
    messages: List[Dict[str, Any]]
    created_at: Optional[str] = None
    is_favorite: Optional[bool] = False
    summary: Optional[str] = None  # 修复：改为字符串类型
    cached_stats: Optional[Dict[str, Any]] = None
    group_id: Optional[str] = None

class CloudGroupCreate(BaseModel):
    group_name: str
    user_id: Optional[str] = "default"

class CloudGroupImport(BaseModel):
    group_id: str

class BatchConversationData(BaseModel):
    conversations: List[ConversationData]
    group_id: Optional[str] = None
    check_duplicate: Optional[bool] = True

class BatchDeleteRequest(BaseModel):
    conversation_ids: List[str]
    group_id: Optional[str] = None

class ConversationUpdate(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None
    messages: Optional[List[Dict[str, Any]]] = None
    timestamp: Optional[int] = None
    created_at: Optional[str] = None
    is_favorite: Optional[bool] = None
    summary: Optional[str] = None  # 修复：改为字符串类型，与ConversationData一致
    cached_stats: Optional[Dict[str, Any]] = None
    group_id: Optional[str] = None

class CleanupRequest(BaseModel):
    days_to_keep: int = 30

# 响应模型
class ConversationResponse(BaseModel):
    cloud_id: str
    success: bool
    message: str
    is_duplicate: Optional[bool] = False
    group_id: Optional[str] = None

class CloudGroupResponse(BaseModel):
    group_id: str
    group_name: str
    user_id: str
    created_at: str
    conversation_count: int
    total_size: int

class BatchOperationResponse(BaseModel):
    total: int
    success_count: int
    error_count: int
    details: List[Dict[str, Any]]

class ConversationListResponse(BaseModel):
    conversations: List[Dict[str, Any]]
    total_count: int

class StorageStatsResponse(BaseModel):
    stats: Dict[str, Any]

# ========== 云端组管理API ==========
@cloud_router.post("/groups", response_model=CloudGroupResponse)
async def create_cloud_group(group_data: CloudGroupCreate, user_payload: dict = Depends(verify_client_token)):
    """
    创建新的云端储存组
    """
    try:
        user_id = user_payload.get('user_id', 'default')
        group_metadata = cloud_storage.create_cloud_group(group_data.group_name, user_id)

        return CloudGroupResponse(
            group_id=group_metadata['group_id'],
            group_name=group_metadata['group_name'],
            user_id=group_metadata['user_id'],
            created_at=group_metadata['created_at'],
            conversation_count=group_metadata['conversation_count'],
            total_size=group_metadata['total_size']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

class GroupIdsRequest(BaseModel):
    group_ids: List[str]

@cloud_router.post("/groups/list")
async def get_cloud_groups_by_ids(request: GroupIdsRequest, user_payload: dict = Depends(verify_client_token)):
    """
    根据提供的group_id列表获取分组信息（高效安全的访问控制）
    """
    try:
        # 🚀 使用group_id作为访问权限 - 只返回请求的分组信息
        groups = []
        for group_id in request.group_ids:
            # 验证分组ID格式
            if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
                continue  # 跳过无效的分组ID

            group_info = cloud_storage.get_group_info_by_id(group_id)
            if group_info:
                groups.append(group_info)

        return {"groups": groups}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.post("/groups/import", response_model=CloudGroupResponse)
async def import_cloud_group(import_data: CloudGroupImport, user_payload: dict = Depends(verify_client_token)):
    """
    通过分组ID导入云端储存组
    """
    try:
        user_id = user_payload.get('user_id', 'default')
        group_metadata = cloud_storage.import_cloud_group(import_data.group_id, user_id)

        return CloudGroupResponse(
            group_id=group_metadata['group_id'],
            group_name=group_metadata['group_name'],
            user_id=group_metadata['user_id'],
            created_at=group_metadata['created_at'],
            conversation_count=group_metadata['conversation_count'],
            total_size=group_metadata['total_size']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.post("/groups/cleanup-legacy")
async def cleanup_legacy_data(user_payload: dict = Depends(verify_client_token)):
    """
    清理旧的日期分组数据
    """
    try:
        result = cloud_storage.cleanup_legacy_date_folders()
        return {
            "success": True,
            "message": "旧数据清理完成",
            "stats": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ========== 批量操作API ==========
@cloud_router.post("/conversations/batch", response_model=BatchOperationResponse)
async def batch_save_conversations(batch_data: BatchConversationData, user_payload: dict = Depends(verify_client_token)):
    """
    批量保存对话到云端
    """
    try:
        conversations_data = [conv.model_dump() for conv in batch_data.conversations]
        result = cloud_storage.batch_save_conversations(
            conversations_data,
            batch_data.group_id,
            batch_data.check_duplicate
        )

        return BatchOperationResponse(
            total=result['total'],
            success_count=result['saved'],
            error_count=result['errors'] + result['duplicates'],
            details=result['details']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.delete("/conversations/batch", response_model=BatchOperationResponse)
async def batch_delete_conversations(delete_data: BatchDeleteRequest, user_payload: dict = Depends(verify_client_token)):
    """
    批量删除对话
    """
    try:
        result = cloud_storage.batch_delete_conversations(
            delete_data.conversation_ids,
            delete_data.group_id
        )

        return BatchOperationResponse(
            total=result['total'],
            success_count=result['deleted'],
            error_count=result['errors'],
            details=result['details']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ========== 单个对话管理API ==========
@cloud_router.post("/conversations", response_model=ConversationResponse)
async def save_conversation(conversation: ConversationData, user_payload: dict = Depends(verify_client_token)):
    """
    保存对话到云端（完全重构版本）
    """
    try:
        raw_data = conversation.model_dump()

        # 验证基本的对话ID
        conversation_id = raw_data.get('id', '')
        if not conversation_id or conversation_id in ['undefined', 'null', '']:
            # 如果没有有效ID，生成一个新的
            import uuid
            conversation_id = str(uuid.uuid4())
            raw_data['id'] = conversation_id

        # 获取组ID
        group_id = raw_data.get('group_id')

        # 保存对话（支持去重检测和组存储）
        result = cloud_storage.save_conversation(raw_data, conversation_id, group_id, check_duplicate=True)

        return ConversationResponse(
            cloud_id=result.get('conversation_id', conversation_id),
            success=True,
            message=result.get('message', '对话保存成功'),
            is_duplicate=result.get('is_duplicate', False),
            group_id=result.get('group_id')
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"保存对话异常: {type(e).__name__}: {str(e)}")  # 添加详细日志
        raise HTTPException(status_code=500, detail=f"保存对话失败: {str(e)}")

@cloud_router.put("/conversations/{conversation_id}", response_model=ConversationResponse)
async def update_conversation(conversation_id: str, conversation: ConversationUpdate,
                            group_id: Optional[str] = None, target_date: Optional[str] = None,
                            user_payload: dict = Depends(verify_client_token)):
    """
    更新云端对话（完全重构版本）- 🚨 强制group_id验证
    """
    try:
        # 验证对话ID
        if not conversation_id or conversation_id in ['undefined', 'null', '']:
            raise HTTPException(status_code=400, detail="无效的对话ID")

        # 🚨 安全检查：必须指定分组ID才能更新对话
        if not group_id:
            raise HTTPException(status_code=400, detail="必须指定分组ID才能更新对话")

        # 验证分组ID格式（基本安全检查）
        if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
            raise HTTPException(status_code=400, detail="无效的分组ID格式")
            
        # 安全地处理输入数据，过滤掉None值和无效数据
        raw_data = conversation.dict()
        safe_update_data = {}
        
        # 只处理有效的字段
        for key, value in raw_data.items():
            if value is not None:
                if key == 'title' and isinstance(value, str):
                    safe_update_data[key] = str(value).strip()[:200]
                elif key == 'messages' and isinstance(value, list):
                    safe_update_data[key] = value
                elif key in ['id', 'is_favorite', 'timestamp', 'created_at', 'summary', 'cached_stats', 'group_id']:
                    safe_update_data[key] = value
        
        # 确保至少有基本字段
        if 'title' not in safe_update_data:
            safe_update_data['title'] = '未命名对话'
        
        success = cloud_storage.update_conversation(conversation_id, safe_update_data, group_id, target_date)
        
        return ConversationResponse(
            cloud_id=conversation_id,
            success=success,
            message="对话更新成功",
            group_id=group_id
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"更新对话异常: {type(e).__name__}: {str(e)}")  # 添加详细日志
        raise HTTPException(status_code=500, detail=f"更新对话失败: {str(e)}")

@cloud_router.get("/conversations/{conversation_id}")
async def load_conversation(conversation_id: str, group_id: Optional[str] = None,
                          target_date: Optional[str] = None, user_payload: dict = Depends(verify_client_token)):
    """
    从云端加载对话（支持组存储）- 🚨 强制group_id验证
    """
    try:
        # 验证对话ID
        if not conversation_id or conversation_id in ['undefined', 'null', '']:
            raise HTTPException(status_code=400, detail="无效的对话ID")

        # 🚨 安全检查：必须指定分组ID才能访问对话
        if not group_id:
            raise HTTPException(status_code=400, detail="必须指定分组ID才能访问对话")

        # 验证分组ID格式（基本安全检查）
        if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
            raise HTTPException(status_code=400, detail="无效的分组ID格式")

        conversation_data = cloud_storage.load_conversation(conversation_id, group_id, target_date)
        return conversation_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.delete("/conversations/{conversation_id}", response_model=ConversationResponse)
async def delete_conversation(conversation_id: str, group_id: Optional[str] = None,
                            target_date: Optional[str] = None, user_payload: dict = Depends(verify_client_token)):
    """
    删除云端对话（支持组存储）- 🚨 强制group_id验证
    """
    try:
        # 验证对话ID
        if not conversation_id or conversation_id in ['undefined', 'null', '']:
            raise HTTPException(status_code=400, detail="无效的对话ID")

        # 🚨 安全检查：必须指定分组ID才能删除对话
        if not group_id:
            raise HTTPException(status_code=400, detail="必须指定分组ID才能删除对话")

        # 验证分组ID格式（基本安全检查）
        if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
            raise HTTPException(status_code=400, detail="无效的分组ID格式")

        success = cloud_storage.delete_conversation(conversation_id, group_id, target_date)

        return ConversationResponse(
            cloud_id=conversation_id,
            success=success,
            message="对话删除成功"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.get("/conversations", response_model=ConversationListResponse)
async def list_conversations(group_id: Optional[str] = None, target_date: Optional[str] = None,
                           limit: int = 100, user_payload: dict = Depends(verify_client_token)):
    """
    列出云端对话（必须指定分组ID，确保数据安全）
    """
    try:
        # 🚨 安全检查：必须指定分组ID才能访问云端对话
        if not group_id:
            raise HTTPException(status_code=400, detail="必须指定分组ID才能访问云端对话")

        # 验证分组ID格式（基本安全检查）
        if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
            raise HTTPException(status_code=400, detail="无效的分组ID格式")

        conversations = cloud_storage.list_conversations(group_id, target_date, limit)

        return ConversationListResponse(
            conversations=conversations,
            total_count=len(conversations)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.get("/stats", response_model=StorageStatsResponse)
async def get_storage_stats(group_id: Optional[str] = None, user_payload: dict = Depends(verify_client_token)):
    """
    获取云端存储统计信息（必须指定分组ID）
    """
    try:
        # 🚨 安全检查：必须指定分组ID才能获取统计信息
        if not group_id:
            raise HTTPException(status_code=400, detail="必须指定分组ID才能获取统计信息")

        # 验证分组ID格式
        if len(group_id) < 10 or not group_id.replace('_', '').replace('-', '').isalnum():
            raise HTTPException(status_code=400, detail="无效的分组ID格式")

        stats = cloud_storage.get_storage_stats(group_id)
        return StorageStatsResponse(stats=stats)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.post("/cleanup")
async def cleanup_old_conversations(cleanup_request: CleanupRequest, user_payload: dict = Depends(verify_client_token)):
    """
    清理旧对话
    """
    try:
        result = cloud_storage.cleanup_old_conversations(cleanup_request.days_to_keep)
        return {
            "success": True,
            "message": f"清理完成，删除了 {result['deleted_count']} 个对话，释放了 {result['freed_mb']} MB 空间",
            "deleted_count": result['deleted_count'],
            "freed_mb": result['freed_mb']
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.post("/conversations/{conversation_id}/sync")
async def sync_conversation_after_update(conversation_id: str, conversation: ConversationData):
    """
    对话更新后同步到云端（用户每轮对话后调用）
    """
    try:
        conversation_data = conversation.dict()
        success = cloud_storage.update_conversation(conversation_id, conversation_data)
        
        return {
            "cloud_id": conversation_id,
            "success": success,
            "message": "对话同步成功",
            "synced_at": conversation_data.get('updated_at')
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.get("/conversations/{conversation_id}/summary")
async def get_conversation_summary(conversation_id: str):
    """
    获取对话摘要信息（不加载完整内容）
    """
    try:
        # 加载完整对话数据
        conversation_data = cloud_storage.load_conversation(conversation_id)
        
        # 返回摘要信息
        summary = {
            'cloud_id': conversation_data.get('cloud_id'),
            'title': conversation_data.get('title', '未命名对话'),
            'created_at': conversation_data.get('created_at'),
            'updated_at': conversation_data.get('updated_at'),
            'message_count': len(conversation_data.get('messages', [])),
            'is_favorite': conversation_data.get('is_favorite', False),
            'cached_stats': conversation_data.get('cached_stats', {})
        }
        
        return summary
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.post("/conversations/batch")
async def batch_save_conversations(conversations: List[ConversationData]):
    """
    批量保存对话到云端
    """
    try:
        results = []
        success_count = 0
        
        for conversation in conversations:
            try:
                conversation_data = conversation.dict()
                cloud_id = cloud_storage.save_conversation(conversation_data)
                results.append({
                    "cloud_id": cloud_id,
                    "success": True,
                    "message": "保存成功"
                })
                success_count += 1
            except Exception as e:
                results.append({
                    "cloud_id": None,
                    "success": False,
                    "message": str(e)
                })
        
        return {
            "results": results,
            "success_count": success_count,
            "total_count": len(conversations),
            "message": f"批量保存完成，成功 {success_count}/{len(conversations)} 个对话"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cloud_router.delete("/conversations/batch")
async def batch_delete_conversations(conversation_ids: List[str]):
    """
    批量删除云端对话
    """
    try:
        results = []
        success_count = 0
        
        for conversation_id in conversation_ids:
            try:
                success = cloud_storage.delete_conversation(conversation_id)
                results.append({
                    "cloud_id": conversation_id,
                    "success": success,
                    "message": "删除成功"
                })
                success_count += 1
            except Exception as e:
                results.append({
                    "cloud_id": conversation_id,
                    "success": False,
                    "message": str(e)
                })
        
        return {
            "results": results,
            "success_count": success_count,
            "total_count": len(conversation_ids),
            "message": f"批量删除完成，成功 {success_count}/{len(conversation_ids)} 个对话"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
