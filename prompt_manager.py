import json
import uuid
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config_getter):
        self.config_getter = config_getter
    
    def get_all_prompts(self) -> Dict[str, Any]:
        """获取所有提示词"""
        config = self.config_getter()
        return config.get('system_prompts', {})
    
    def get_prompt(self, prompt_id: str) -> Optional[Dict[str, Any]]:
        """获取指定提示词"""
        prompts = self.get_all_prompts()
        return prompts.get(prompt_id)
    
    def create_prompt(self, prompt_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新提示词"""
        from config import save_config
        
        config = self.config_getter()
        
        # 生成唯一ID
        prompt_id = prompt_data.get('id') or f"prompt_{uuid.uuid4().hex[:8]}"
        
        # 检查ID是否已存在
        if 'system_prompts' not in config:
            config['system_prompts'] = {}
        
        if prompt_id in config['system_prompts']:
            raise ValueError(f"提示词ID '{prompt_id}' 已存在")
        
        # 构建完整的提示词对象
        now = datetime.now(timezone.utc).isoformat()
        prompt = {
            'id': prompt_id,
            'name': prompt_data['name'],
            'description': prompt_data.get('description', ''),
            'content': prompt_data['content'],
            'category': prompt_data.get('category', '自定义'),
            'tags': prompt_data.get('tags', []),
            'created_at': now,
            'updated_at': now,
            'version': prompt_data.get('version', '1.0.0'),
            'author': prompt_data.get('author', '用户'),
            'is_system': prompt_data.get('is_system', False)
        }
        
        # 保存到配置
        config['system_prompts'][prompt_id] = prompt
        save_config(config)
        
        # 更新系统提示词tokens缓存
        try:
            from utils import update_all_prompt_tokens_cache
            update_all_prompt_tokens_cache(config)
            logger.info(f"系统提示词tokens缓存已更新（触发：创建提示词 {prompt_id}）")
        except Exception as e:
            logger.warning(f"更新系统提示词tokens缓存失败: {e}")
        
        logger.info(f"创建提示词: {prompt_id} - {prompt['name']}")
        return prompt
    
    def update_prompt(self, prompt_id: str, prompt_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新提示词"""
        from config import save_config
        
        config = self.config_getter()
        
        if 'system_prompts' not in config or prompt_id not in config['system_prompts']:
            raise ValueError(f"提示词 '{prompt_id}' 不存在")
        
        current_prompt = config['system_prompts'][prompt_id]
        
        # 检查是否为系统提示词
        if current_prompt.get('is_system', False):
            raise ValueError("不能修改系统提示词")
        
        # 更新字段
        updatable_fields = ['name', 'description', 'content', 'category', 'tags', 'version', 'author']
        for field in updatable_fields:
            if field in prompt_data:
                current_prompt[field] = prompt_data[field]
        
        current_prompt['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        save_config(config)
        
        # 更新系统提示词tokens缓存
        try:
            from utils import update_all_prompt_tokens_cache
            update_all_prompt_tokens_cache(config)
            logger.info(f"系统提示词tokens缓存已更新（触发：更新提示词 {prompt_id}）")
        except Exception as e:
            logger.warning(f"更新系统提示词tokens缓存失败: {e}")
        
        logger.info(f"更新提示词: {prompt_id} - {current_prompt['name']}")
        return current_prompt
    
    def delete_prompt(self, prompt_id: str) -> bool:
        """删除提示词"""
        from config import save_config
        
        config = self.config_getter()
        
        if 'system_prompts' not in config or prompt_id not in config['system_prompts']:
            raise ValueError(f"提示词 '{prompt_id}' 不存在")
        
        prompt = config['system_prompts'][prompt_id]
        
        # 检查是否为系统提示词
        if prompt.get('is_system', False):
            raise ValueError("不能删除系统提示词")
        
        # 检查是否被角色使用
        character_roles = config.get('character_roles', {})
        using_roles = []
        for role_id, role in character_roles.items():
            if role.get('system_prompt_id') == prompt_id:
                using_roles.append(role['name'])
        
        if using_roles:
            raise ValueError(f"提示词正在被以下角色使用，无法删除: {', '.join(using_roles)}")
        
        # 删除提示词
        del config['system_prompts'][prompt_id]
        save_config(config)
        
        # 更新系统提示词tokens缓存
        try:
            from utils import update_all_prompt_tokens_cache
            update_all_prompt_tokens_cache(config)
            logger.info(f"系统提示词tokens缓存已更新（触发：删除提示词 {prompt_id}）")
        except Exception as e:
            logger.warning(f"更新系统提示词tokens缓存失败: {e}")
        
        logger.info(f"删除提示词: {prompt_id} - {prompt['name']}")
        return True
    
    def copy_prompt(self, prompt_id: str, new_name: Optional[str] = None) -> Dict[str, Any]:
        """复制提示词"""
        source_prompt = self.get_prompt(prompt_id)
        if not source_prompt:
            raise ValueError(f"提示词 '{prompt_id}' 不存在")
        
        # 创建副本
        copy_data = source_prompt.copy()
        copy_data['id'] = f"prompt_{uuid.uuid4().hex[:8]}"
        copy_data['name'] = new_name or f"{source_prompt['name']} - 副本"
        copy_data['is_system'] = False  # 副本不是系统提示词
        copy_data['author'] = '用户'
        
        return self.create_prompt(copy_data)
    
    def export_prompt(self, prompt_id: str) -> Dict[str, Any]:
        """导出提示词"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            raise ValueError(f"提示词 '{prompt_id}' 不存在")
        
        # 创建导出格式
        export_data = {
            'format_version': '1.0',
            'export_time': datetime.now(timezone.utc).isoformat(),
            'prompt': prompt
        }
        
        return export_data
    
    def export_all_prompts(self) -> Dict[str, Any]:
        """导出所有提示词"""
        prompts = self.get_all_prompts()
        
        export_data = {
            'format_version': '1.0',
            'export_time': datetime.now(timezone.utc).isoformat(),
            'prompts': prompts
        }
        
        return export_data
    
    def import_prompt(self, import_data: Dict[str, Any], overwrite: bool = False) -> Dict[str, Any]:
        """导入提示词"""
        from config import save_config
        
        results = {
            'total': 0,
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }
        
        config = self.config_getter()
        if 'system_prompts' not in config:
            config['system_prompts'] = {}
        
        prompts_to_import = []
        
        # 处理不同的导入格式
        if 'prompt' in import_data:
            # 单个提示词格式
            prompts_to_import = [import_data['prompt']]
        elif 'prompts' in import_data:
            # 批量提示词格式
            if isinstance(import_data['prompts'], dict):
                prompts_to_import = list(import_data['prompts'].values())
            elif isinstance(import_data['prompts'], list):
                prompts_to_import = import_data['prompts']
            else:
                results['errors'].append("❌ 提示词数据格式不正确")
                return results
        elif isinstance(import_data, list):
            # 直接是提示词数组
            prompts_to_import = import_data
        elif isinstance(import_data, dict) and import_data.get('name'):
            # 单个提示词对象
            prompts_to_import = [import_data]
        else:
            results['errors'].append("❌ 不支持的文件格式。支持的格式：\n1. 提示词导出文件\n2. 提示词数组\n3. 单个提示词对象")
            return results
        
        results['total'] = len(prompts_to_import)
        
        for prompt_data in prompts_to_import:
            try:
                # 检查必需字段
                if not prompt_data.get('name'):
                    results['errors'].append(f"❌ 提示词缺少名称字段: {prompt_data}")
                    results['skipped'] += 1
                    continue
                
                # 确定提示词ID
                prompt_id = prompt_data.get('id')
                if not prompt_id:
                    prompt_id = f"prompt_{uuid.uuid4().hex[:8]}"
                    prompt_data['id'] = prompt_id
                
                # 检查是否已存在
                existing_prompt = config['system_prompts'].get(prompt_id)
                
                if existing_prompt and not overwrite:
                    # 生成新ID避免冲突
                    new_id = f"prompt_{uuid.uuid4().hex[:8]}"
                    prompt_data['id'] = new_id
                    prompt_data['name'] = f"{prompt_data['name']} - 导入"
                    results['errors'].append(f"⚠️ 提示词ID冲突，已重命名: {prompt_data['name']} (新ID: {new_id})")
                    prompt_id = new_id
                
                # 设置默认值
                now = datetime.now(timezone.utc).isoformat()
                
                prompt = {
                    'id': prompt_id,
                    'name': prompt_data.get('name', ''),
                    'content': prompt_data.get('content', ''),
                    'description': prompt_data.get('description', ''),
                    'category': prompt_data.get('category', '导入'),
                    'tags': prompt_data.get('tags', []),
                    'is_system': prompt_data.get('is_system', False),
                    'updated_at': now,
                    'author': prompt_data.get('author', '导入用户')
                }
                
                # 保留创建时间（如果存在）
                if existing_prompt:
                    prompt['created_at'] = existing_prompt.get('created_at', now)
                    results['updated'] += 1
                    results['errors'].append(f"✅ 更新提示词: {prompt['name']}")
                else:
                    prompt['created_at'] = prompt_data.get('created_at', now)
                    results['created'] += 1
                    results['errors'].append(f"🆕 创建提示词: {prompt['name']}")
                
                # 保存到配置
                config['system_prompts'][prompt_id] = prompt
                
            except Exception as e:
                error_msg = f"❌ 导入提示词失败: {prompt_data.get('name', '未知提示词')} - {str(e)}"
                results['errors'].append(error_msg)
                results['skipped'] += 1
                logger.error(f"提示词导入错误: {error_msg}", exc_info=True)
        
        # 保存配置
        try:
            save_config(config)
            logger.info(f"提示词导入完成: 创建 {results['created']} 个, 更新 {results['updated']} 个, 跳过 {results['skipped']} 个")
        except Exception as e:
            results['errors'].append(f"❌ 保存配置失败: {str(e)}")
            logger.error(f"保存提示词配置失败: {e}", exc_info=True)
        
        return results
    
    def search_prompts(self, keyword: str = '', category: str = '', tags: List[str] = None) -> List[Dict[str, Any]]:
        """搜索提示词"""
        prompts = self.get_all_prompts()
        results = []
        
        for prompt_id, prompt in prompts.items():
            # 关键词搜索
            if keyword:
                if (keyword.lower() not in prompt['name'].lower() and 
                    keyword.lower() not in prompt.get('description', '').lower()):
                    continue
            
            # 分类筛选
            if category and prompt.get('category', '') != category:
                continue
            
            # 标签筛选
            if tags:
                prompt_tags = prompt.get('tags', [])
                if not any(tag in prompt_tags for tag in tags):
                    continue
            
            results.append(prompt)
        
        # 按更新时间倒序排列
        results.sort(key=lambda x: x.get('updated_at', ''), reverse=True)
        return results


class CharacterManager:
    """角色管理器"""
    
    def __init__(self, config_getter):
        self.config_getter = config_getter
    
    def get_all_characters(self) -> Dict[str, Any]:
        """获取所有角色"""
        try:
            config = self.config_getter()
            # 减少日志输出，只在发生错误时记录
            character_roles = config.get('character_roles', {})
            return character_roles
        except Exception as e:
            logger.error(f"获取所有角色失败: {e}", exc_info=True)
            raise
    
    def get_character(self, character_id: str) -> Optional[Dict[str, Any]]:
        """获取指定角色（支持通过id或model_id查找）"""
        characters = self.get_all_characters()
        
        # 首先尝试通过内部ID查找
        if character_id in characters:
            return characters[character_id]
        
        # 然后尝试通过model_id查找
        for char in characters.values():
            if char.get('model_id') == character_id:
                return char
        
        return None
    
    def create_character(self, character_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新角色"""
        from config import save_config
        
        config = self.config_getter()
        
        # 生成唯一ID
        character_id = character_data.get('id') or f"char_{uuid.uuid4().hex[:8]}"
        
        # 检查ID是否已存在
        if 'character_roles' not in config:
            config['character_roles'] = {}
        
        if character_id in config['character_roles']:
            raise ValueError(f"角色ID '{character_id}' 已存在")
        
        # 验证必需字段
        required_fields = ['name', 'display_name', 'model_id', 'backend_model', 'system_prompt_id']
        for field in required_fields:
            if field not in character_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 注意：model_id 是用户自定义的角色标识符，不需要在 model_config 中存在
        
        # 验证提示词是否存在（允许空值）
        system_prompts = config.get('system_prompts', {})
        system_prompt_id = character_data['system_prompt_id']
        if system_prompt_id and system_prompt_id not in system_prompts:
            raise ValueError(f"提示词 '{system_prompt_id}' 不存在")
        
        # 构建完整的角色对象
        now = datetime.now(timezone.utc).isoformat()
        character = {
            'id': character_id,
            'name': character_data['name'],
            'display_name': character_data['display_name'],
            'description': character_data.get('description', ''),
            'avatar_url': character_data.get('avatar_url', ''),
            'model_id': character_data['model_id'],
            'backend_model': character_data['backend_model'],
            'system_prompt_id': character_data['system_prompt_id'],
            'category': character_data.get('category', '自定义'),
            'tags': character_data.get('tags', []),
            'is_active': character_data.get('is_active', True),
            'is_default': character_data.get('is_default', False),
            'allow_custom_system_prompt': character_data.get('allow_custom_system_prompt', False),  # 新增：用户自定义系统提示词开关
            'created_at': now,
            'updated_at': now,
            'author': character_data.get('author', '用户')
        }
        
        # 添加计费配置
        if 'pricing' in character_data:
            # 验证提供的计费配置
            validation_result = self.validate_character_pricing(character_data['pricing'])
            if not validation_result["valid"]:
                raise ValueError(f"计费配置验证失败: {validation_result['message']}")
            character['pricing'] = character_data['pricing']
        else:
            # 使用默认计费配置模板
            character['pricing'] = self.get_character_pricing_template()
        
        # 如果设置为默认角色，取消其他角色的默认状态
        if character.get('is_default', False):
            for existing_id, existing_char in config['character_roles'].items():
                existing_char['is_default'] = False
        
        # 保存到配置
        config['character_roles'][character_id] = character
        save_config(config)
        
        logger.info(f"创建角色: {character_id} - {character['name']}")
        return character
    
    def update_character(self, character_id: str, character_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新角色"""
        from config import save_config
        
        config = self.config_getter()
        
        if 'character_roles' not in config or character_id not in config['character_roles']:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        current_character = config['character_roles'][character_id]
        
        # 更新字段
        updatable_fields = [
            'name', 'display_name', 'description', 'avatar_url', 
            'model_id', 'backend_model', 'system_prompt_id', 
            'category', 'tags', 'is_active', 'is_default', 'pricing'
        ]
        
        for field in updatable_fields:
            if field in character_data:
                # 验证特殊字段
                if field == 'system_prompt_id':
                    system_prompts = config.get('system_prompts', {})
                    # 允许空的 system_prompt_id
                    if character_data[field] and character_data[field] not in system_prompts:
                        raise ValueError(f"提示词 '{character_data[field]}' 不存在")
                elif field == 'pricing':
                    # 验证计费配置
                    validation_result = self.validate_character_pricing(character_data[field])
                    if not validation_result["valid"]:
                        raise ValueError(f"计费配置验证失败: {validation_result['message']}")
                
                current_character[field] = character_data[field]
        
        # 如果设置为默认角色，取消其他角色的默认状态
        if character_data.get('is_default', False):
            for existing_id, existing_char in config['character_roles'].items():
                if existing_id != character_id:
                    existing_char['is_default'] = False
        
        current_character['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        save_config(config)
        
        logger.info(f"更新角色: {character_id} - {current_character['name']}")
        return current_character
    
    def delete_character(self, character_id: str) -> bool:
        """删除角色"""
        from config import save_config
        
        config = self.config_getter()
        
        if 'character_roles' not in config or character_id not in config['character_roles']:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        character = config['character_roles'][character_id]
        
        # 检查是否为默认角色
        if character.get('is_default', False):
            raise ValueError("不能删除默认角色，请先设置其他角色为默认")
        
        # 删除角色
        del config['character_roles'][character_id]
        save_config(config)
        
        logger.info(f"删除角色: {character_id} - {character['name']}")
        return True
    
    def copy_character(self, character_id: str, new_name: Optional[str] = None) -> Dict[str, Any]:
        """复制角色"""
        source_character = self.get_character(character_id)
        if not source_character:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        # 创建副本
        copy_data = source_character.copy()
        copy_data['id'] = f"char_{uuid.uuid4().hex[:8]}"
        copy_data['name'] = new_name or f"{source_character['name']} - 副本"
        copy_data['display_name'] = copy_data['name']
        copy_data['is_default'] = False  # 副本不是默认角色
        copy_data['author'] = '用户'
        
        return self.create_character(copy_data)
    
    def get_active_characters(self) -> List[Dict[str, Any]]:
        """获取活跃角色列表"""
        try:
            characters = self.get_all_characters()
            
            active_chars = []
            
            for char_id, char in characters.items():
                if char.get('is_active', True):
                    active_chars.append(char)
            
            # 按默认角色、创建时间排序
            active_chars.sort(key=lambda x: (not x.get('is_default', False), x.get('created_at', '')))
            return active_chars
        except Exception as e:
            logger.error(f"获取活跃角色失败: {e}", exc_info=True)
            raise
    
    def get_default_character(self) -> Optional[Dict[str, Any]]:
        """获取默认角色"""
        characters = self.get_all_characters()
        
        for char in characters.values():
            if char.get('is_default', False) and char.get('is_active', True):
                return char
        
        return None
    
    def search_characters(self, keyword: str = '', category: str = '', tags: List[str] = None) -> List[Dict[str, Any]]:
        """搜索角色"""
        characters = self.get_all_characters()
        results = []
        
        for char_id, char in characters.items():
            # 关键词搜索
            if keyword:
                if (keyword.lower() not in char['name'].lower() and 
                    keyword.lower() not in char.get('description', '').lower()):
                    continue
            
            # 分类筛选
            if category and char.get('category', '') != category:
                continue
            
            # 标签筛选
            if tags:
                char_tags = char.get('tags', [])
                if not any(tag in char_tags for tag in tags):
                    continue
            
            results.append(char)
        
        # 按默认角色、更新时间排序
        results.sort(key=lambda x: (not x.get('is_default', False), x.get('updated_at', '')), reverse=True)
        return results
    
    def get_character_pricing_template(self) -> dict:
        """获取角色计费配置模板"""
        config = self.config_getter()
        return config.get('character_pricing_template', {
            "enable_independent_pricing": True,
            "pricing_mode": "simple",
            "simple_pricing": {
                "input_price_1m": 1.0,
                "output_price_1m": 5.0
            },
            "tiered_pricing": {
                "input_price_1m": 1.0,
                "input_price_1m_high": 2.0,
                "output_price_1m": 5.0,
                "output_price_1m_high": 8.0,
                "threshold_tokens": 100000
            },
            "staged_pricing": {
                "enable_staged": False,
                "stages": [
                    {
                        "stage_name": "初期阶段",
                        "max_messages": 50,
                        "input_price_1m": 0.5,
                        "output_price_1m": 2.5
                    },
                    {
                        "stage_name": "深度交流",
                        "max_messages": -1,
                        "input_price_1m": 1.5,
                        "output_price_1m": 7.5
                    }
                ]
            }
        })
    
    def validate_character_pricing(self, pricing_config: dict) -> dict:
        """验证角色计费配置"""
        try:
            # 检查必要字段
            if not isinstance(pricing_config, dict):
                raise ValueError("计费配置必须是字典格式")
            
            pricing_mode = pricing_config.get('pricing_mode', 'simple')
            if pricing_mode not in ['simple', 'tiered', 'staged']:
                raise ValueError("计费模式必须是 simple、tiered 或 staged")
            
            # 验证简单计费
            if 'simple_pricing' in pricing_config:
                simple = pricing_config['simple_pricing']
                if not isinstance(simple, dict):
                    raise ValueError("简单计费配置格式错误")
                
                if 'input_price_1m' not in simple or 'output_price_1m' not in simple:
                    raise ValueError("简单计费必须包含 input_price_1m 和 output_price_1m")
                
                if not isinstance(simple['input_price_1m'], (int, float)) or simple['input_price_1m'] < 0:
                    raise ValueError("输入价格必须是非负数")
                
                if not isinstance(simple['output_price_1m'], (int, float)) or simple['output_price_1m'] < 0:
                    raise ValueError("输出价格必须是非负数")
            
            # 验证阶梯计费
            if 'tiered_pricing' in pricing_config:
                tiered = pricing_config['tiered_pricing']
                if not isinstance(tiered, dict):
                    raise ValueError("阶梯计费配置格式错误")
                
                required_fields = ['input_price_1m', 'output_price_1m']
                for field in required_fields:
                    if field not in tiered:
                        raise ValueError(f"阶梯计费必须包含 {field}")
                    if not isinstance(tiered[field], (int, float)) or tiered[field] < 0:
                        raise ValueError(f"{field} 必须是非负数")
                
                # 验证高价阶梯
                if 'input_price_1m_high' in tiered:
                    if not isinstance(tiered['input_price_1m_high'], (int, float)) or tiered['input_price_1m_high'] < 0:
                        raise ValueError("高价输入价格必须是非负数")
                
                if 'output_price_1m_high' in tiered:
                    if not isinstance(tiered['output_price_1m_high'], (int, float)) or tiered['output_price_1m_high'] < 0:
                        raise ValueError("高价输出价格必须是非负数")
                
                if 'threshold_tokens' in tiered:
                    if not isinstance(tiered['threshold_tokens'], int) or tiered['threshold_tokens'] <= 0:
                        raise ValueError("阈值tokens必须是正整数")
            
            # 验证阶段计费
            if 'staged_pricing' in pricing_config:
                staged = pricing_config['staged_pricing']
                if not isinstance(staged, dict):
                    raise ValueError("阶段计费配置格式错误")
                
                if staged.get('enable_staged', False):
                    stages = staged.get('stages', [])
                    if not isinstance(stages, list) or len(stages) == 0:
                        raise ValueError("启用阶段计费时必须提供阶段配置")
                    
                    for i, stage in enumerate(stages):
                        if not isinstance(stage, dict):
                            raise ValueError(f"第 {i+1} 个阶段配置格式错误")
                        
                        required_stage_fields = ['stage_name', 'max_messages', 'input_price_1m', 'output_price_1m']
                        for field in required_stage_fields:
                            if field not in stage:
                                raise ValueError(f"第 {i+1} 个阶段必须包含 {field}")
                        
                        if not isinstance(stage['max_messages'], int):
                            raise ValueError(f"第 {i+1} 个阶段的 max_messages 必须是整数")
                        
                        if not isinstance(stage['input_price_1m'], (int, float)) or stage['input_price_1m'] < 0:
                            raise ValueError(f"第 {i+1} 个阶段的输入价格必须是非负数")
                        
                        if not isinstance(stage['output_price_1m'], (int, float)) or stage['output_price_1m'] < 0:
                            raise ValueError(f"第 {i+1} 个阶段的输出价格必须是非负数")
            
            return {"valid": True, "message": "计费配置验证通过"}
            
        except Exception as e:
            return {"valid": False, "message": str(e)}
    
    def update_character_pricing(self, character_id: str, pricing_config: dict) -> dict:
        """更新角色计费配置"""
        from config import save_config
        
        # 验证计费配置
        validation_result = self.validate_character_pricing(pricing_config)
        if not validation_result["valid"]:
            raise ValueError(validation_result["message"])
        
        config = self.config_getter()
        
        if 'character_roles' not in config or character_id not in config['character_roles']:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        # 更新角色的计费配置
        config['character_roles'][character_id]['pricing'] = pricing_config
        config['character_roles'][character_id]['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        save_config(config)
        
        logger.info(f"更新角色计费配置: {character_id}")
        return config['character_roles'][character_id]
    
    def get_character_pricing(self, character_id: str) -> dict:
        """获取角色计费配置"""
        character = self.get_character(character_id)
        if not character:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        pricing = character.get('pricing', {})
        if not pricing:
            # 如果角色没有计费配置，返回模板
            return self.get_character_pricing_template()
        
        return pricing
    
    def enable_character_independent_pricing(self, character_id: str, enable: bool = True) -> dict:
        """启用/禁用角色独立计费"""
        character = self.get_character(character_id)
        if not character:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        # 获取当前计费配置
        pricing = character.get('pricing', self.get_character_pricing_template())
        pricing['enable_independent_pricing'] = enable
        
        # 更新配置
        return self.update_character_pricing(character_id, pricing)
    
    def set_character_pricing_mode(self, character_id: str, mode: str) -> dict:
        """设置角色计费模式"""
        if mode not in ['simple', 'tiered', 'staged']:
            raise ValueError("计费模式必须是 simple、tiered 或 staged")
        
        character = self.get_character(character_id)
        if not character:
            raise ValueError(f"角色 '{character_id}' 不存在")
        
        # 获取当前计费配置
        pricing = character.get('pricing', self.get_character_pricing_template())
        pricing['pricing_mode'] = mode
        
        # 更新配置
        return self.update_character_pricing(character_id, pricing)
    
    def get_characters_pricing_summary(self) -> list:
        """获取所有角色的计费配置摘要"""
        characters = self.get_all_characters()
        summary = []
        
        for char_id, character in characters.items():
            pricing = character.get('pricing', {})
            
            if pricing.get('enable_independent_pricing', False):
                mode = pricing.get('pricing_mode', 'simple')
                
                if mode == 'simple':
                    simple_pricing = pricing.get('simple_pricing', {})
                    price_info = {
                        'input_price': simple_pricing.get('input_price_1m', 0),
                        'output_price': simple_pricing.get('output_price_1m', 0),
                        'description': '简单计费'
                    }
                elif mode == 'tiered':
                    tiered_pricing = pricing.get('tiered_pricing', {})
                    price_info = {
                        'input_price': tiered_pricing.get('input_price_1m', 0),
                        'output_price': tiered_pricing.get('output_price_1m', 0),
                        'description': f"阶梯计费 (阈值: {tiered_pricing.get('threshold_tokens', 0)})"
                    }
                elif mode == 'staged':
                    staged_pricing = pricing.get('staged_pricing', {})
                    stages = staged_pricing.get('stages', [])
                    stage_count = len(stages)
                    price_info = {
                        'input_price': stages[0].get('input_price_1m', 0) if stages else 0,
                        'output_price': stages[0].get('output_price_1m', 0) if stages else 0,
                        'description': f"阶段计费 ({stage_count}个阶段)"
                    }
                else:
                    price_info = {
                        'input_price': 0,
                        'output_price': 0,
                        'description': '未知模式'
                    }
            else:
                price_info = {
                    'input_price': 0,
                    'output_price': 0,
                    'description': '使用模型默认计费'
                }
            
            summary.append({
                'character_id': char_id,
                'character_name': character.get('name', ''),
                'display_name': character.get('display_name', ''),
                'independent_pricing': pricing.get('enable_independent_pricing', False),
                'pricing_mode': pricing.get('pricing_mode', 'simple'),
                'price_info': price_info
            })
        
        return summary
    
    def import_characters(self, characters_data: List[Dict[str, Any]], overwrite: bool = False) -> Dict[str, Any]:
        """批量导入角色"""
        from config import save_config
        
        results = {
            'total': len(characters_data),
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }
        
        try:
            config = self.config_getter()
            if 'character_roles' not in config:
                config['character_roles'] = {}
            
            # 获取现有的系统提示词列表
            system_prompts = config.get('system_prompts', {})
            available_prompts = list(system_prompts.keys())
            
            # 首先检查所有角色需要的系统提示词
            missing_prompts = set()
            for char_data in characters_data:
                system_prompt_id = char_data.get('system_prompt_id')
                if system_prompt_id and system_prompt_id not in system_prompts:
                    missing_prompts.add(system_prompt_id)
            
            # 如果有缺失的系统提示词，给出详细提醒
            if missing_prompts:
                results['errors'].append(f"⚠️ 检测到缺失的系统提示词: {', '.join(missing_prompts)}")
                results['errors'].append(f"💡 建议：请先在提示词管理页面导入或创建这些系统提示词，然后重新导入角色")
                results['errors'].append(f"📋 当前可用的系统提示词: {', '.join(available_prompts) if available_prompts else '无'}")
            
            for i, char_data in enumerate(characters_data):
                try:
                    # 检查必需字段
                    if not char_data or not isinstance(char_data, dict):
                        results['errors'].append(f"❌ 第{i+1}个角色数据格式错误: {char_data}")
                        results['skipped'] += 1
                        continue
                        
                    if not char_data.get('name'):
                        results['errors'].append(f"❌ 第{i+1}个角色缺少名称字段: {char_data}")
                        results['skipped'] += 1
                        continue
                    
                    if not char_data.get('display_name'):
                        char_data['display_name'] = char_data['name']
                        results['errors'].append(f"ℹ️ 角色 '{char_data['name']}' 缺少显示名称，已使用角色名称")
                    
                    # 确定角色ID
                    char_id = char_data.get('id')
                    if not char_id:
                        char_id = f"char_{uuid.uuid4().hex[:8]}"
                        char_data['id'] = char_id
                        results['errors'].append(f"ℹ️ 角色 '{char_data['name']}' 缺少ID，已生成新ID: {char_id}")
                    
                    # 检查是否已存在
                    existing_character = config['character_roles'].get(char_id)
                    
                    if existing_character and not overwrite:
                        results['errors'].append(f"⏭️ 跳过已存在的角色: {char_data['name']} (ID: {char_id})")
                        results['skipped'] += 1
                        continue
                    
                    # 处理系统提示词
                    system_prompt_id = char_data.get('system_prompt_id')
                    if system_prompt_id:
                        if system_prompt_id not in system_prompts:
                            # 尝试使用默认提示词
                            if 'general_assistant' in system_prompts:
                                char_data['system_prompt_id'] = 'general_assistant'
                                results['errors'].append(f"🔄 角色 '{char_data['name']}' 的系统提示词 '{system_prompt_id}' 不存在，已改用默认提示词 'general_assistant'")
                            else:
                                # 如果连默认提示词都没有，创建一个基本的
                                default_prompt_id = 'imported_default'
                                if default_prompt_id not in system_prompts:
                                    system_prompts[default_prompt_id] = {
                                        'id': default_prompt_id,
                                        'name': '导入默认提示词',
                                        'content': '你是一个有用的AI助手，请认真回答用户的问题。',
                                        'description': '为导入角色自动创建的默认系统提示词',
                                        'category': '系统',
                                        'tags': ['导入', '默认'],
                                        'is_system': True,
                                        'created_at': datetime.now(timezone.utc).isoformat(),
                                        'updated_at': datetime.now(timezone.utc).isoformat(),
                                        'author': '系统'
                                    }
                                    config['system_prompts'] = system_prompts
                                    results['errors'].append(f"📝 自动创建了默认系统提示词 '{default_prompt_id}'")
                                
                                char_data['system_prompt_id'] = default_prompt_id
                                results['errors'].append(f"🔄 角色 '{char_data['name']}' 的系统提示词 '{system_prompt_id}' 不存在，已改用自动创建的默认提示词")
                    else:
                        # 如果没有指定系统提示词，使用默认的
                        if 'general_assistant' in system_prompts:
                            char_data['system_prompt_id'] = 'general_assistant'
                        else:
                            char_data['system_prompt_id'] = 'imported_default'
                            results['errors'].append(f"⚠️ 角色 '{char_data['name']}' 未指定系统提示词，已设置为默认")
                    
                    # 设置默认值
                    now = datetime.now(timezone.utc).isoformat()
                    
                    character = {
                        'id': char_id,
                        'name': char_data.get('name', ''),
                        'display_name': char_data.get('display_name', ''),
                        'description': char_data.get('description', ''),
                        'avatar_url': char_data.get('avatar_url', ''),
                        'model_id': char_data.get('model_id', char_id),
                        'backend_model': char_data.get('backend_model', 'gpt-3.5-turbo'),
                        'system_prompt_id': char_data.get('system_prompt_id', 'general_assistant'),
                        'category': char_data.get('category', '导入'),
                        'tags': char_data.get('tags', []),
                        'is_active': char_data.get('is_active', True),
                        'is_default': False,  # 导入的角色不设为默认
                        'updated_at': now,
                        'author': char_data.get('author', '导入用户')
                    }
                    
                    # 保留创建时间（如果存在）
                    if existing_character:
                        character['created_at'] = existing_character.get('created_at', now)
                        results['updated'] += 1
                        results['errors'].append(f"✅ 更新角色: {character['name']}")
                    else:
                        character['created_at'] = char_data.get('created_at', now)
                        results['created'] += 1
                        results['errors'].append(f"🆕 创建角色: {character['name']}")
                    
                    # 处理计费配置
                    try:
                        if 'pricing' in char_data and char_data['pricing']:
                            validation_result = self.validate_character_pricing(char_data['pricing'])
                            if validation_result["valid"]:
                                character['pricing'] = char_data['pricing']
                            else:
                                character['pricing'] = self.get_character_pricing_template()
                                results['errors'].append(f"⚠️ 角色 '{character['name']}' 的计费配置无效，已使用默认配置: {validation_result.get('message', '')}")
                        else:
                            character['pricing'] = self.get_character_pricing_template()
                    except Exception as pricing_error:
                        character['pricing'] = self.get_character_pricing_template()
                        results['errors'].append(f"⚠️ 角色 '{character['name']}' 的计费配置处理失败，已使用默认配置: {str(pricing_error)}")
                    
                    # 保存到配置
                    config['character_roles'][char_id] = character
                    
                except Exception as char_error:
                    error_msg = f"❌ 处理角色失败: {char_data.get('name', f'第{i+1}个角色')} - {str(char_error)}"
                    results['errors'].append(error_msg)
                    results['skipped'] += 1
                    logger.error(f"角色导入错误: {error_msg}", exc_info=True)
            
            # 保存配置
            try:
                save_config(config)
                logger.info(f"角色导入完成: 创建 {results['created']} 个, 更新 {results['updated']} 个, 跳过 {results['skipped']} 个")
            except Exception as save_error:
                results['errors'].append(f"❌ 保存配置失败: {str(save_error)}")
                logger.error(f"保存角色配置失败: {save_error}", exc_info=True)
        
        except Exception as general_error:
            error_msg = f"❌ 导入过程中发生错误: {str(general_error)}"
            results['errors'].append(error_msg)
            logger.error(f"角色导入总体错误: {error_msg}", exc_info=True)
        
        return results 