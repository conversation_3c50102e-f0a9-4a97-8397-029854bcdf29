# Python 编译生成的文件
__pycache__/
*.pyc
*.pyo
*.pyd

# 虚拟环境目录（假设是 venv 或 env）
venv/
env/
ENV/
.venv/

# 编辑器配置文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 日志文件
*.log

# 系统生成文件
.DS_Store
Thumbs.db

# 配置缓存和临时文件
*.cache
*.tmp

# JSON 缓存文件（如 prompt_tokens_cache.json）
*.json

# 如果有打包生成的文件夹
dist/
build/
.eggs/
*.egg-info/

# 忽略 node_modules（如果你用前端包管理工具）
node_modules/

# 忽略静态资源中的某些生成文件（根据需要）
# 比如如果你知道有自动生成的文件夹，可以加
# static/generated/

# 忽略测试相关的临时文件
.coverage
.pytest_cache/
