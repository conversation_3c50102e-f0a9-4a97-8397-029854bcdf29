<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级 Markdown & LaTeX 编辑器</title>
    
    <!-- 核心样式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/lib/codemirror.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/theme/github-light.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.2.0/github-markdown.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/css/all.min.css"> 
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/css/all.min.css"> 


    <style>
        :root {
            --primary: #4361ee;
            --primary-light: #4895ef;
            --primary-dark: #3f37c9;
            --success: #4cc9f0;
            --danger: #f72585;
            --warning: #f8961e;
            --info: #56cfe1;
            --dark: #3a0ca3;
            --light: #f8f9fa;
            --bg-color: #ffffff;
            --text-color: #2d3748;
            --border-color: #e2e8f0;
            --highlight-color: rgba(67, 97, 238, 0.1);
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
            --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            --font-mono: "JetBrains Mono", "Fira Code", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* 暗黑模式 */
        [data-theme="dark"] {
            --primary: #4895ef;
            --primary-light: #4cc9f0;
            --primary-dark: #3f37c9;
            --bg-color: #1a202c;
            --text-color: #e2e8f0;
            --border-color: #2d3748;
            --highlight-color: rgba(72, 149, 239, 0.15);
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.25);
        }

        [data-theme="dark"]::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"]::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.15);
        }

        [data-theme="dark"]::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        /* 暗色模式代码和表格适配 - 增强对比度 */
        [data-theme="dark"] .markdown-body code {
            background-color: #2d3748;
            color: #f7fafc; /* 更亮的文本颜色 */
        }
        
        [data-theme="dark"] .markdown-body pre {
            background-color: #2d3748 !important;
            border: 1px solid #4a5568 !important;
        }
        
        [data-theme="dark"] .markdown-body pre code {
            color: #f7fafc; /* 更亮的文本颜色 */
        }
        
        [data-theme="dark"] .markdown-body table {
            border-collapse: collapse;
        }
        
        [data-theme="dark"] .markdown-body table tr {
            background-color: #2d3748;
            border-top: 1px solid #4a5568;
        }
        
        [data-theme="dark"] .markdown-body table tr:nth-child(2n) {
            background-color: #1a202c;
        }
        
        [data-theme="dark"] .markdown-body table th,
        [data-theme="dark"] .markdown-body table td {
            border: 1px solid #4a5568;
            padding: 6px 13px;
        }
        
        /* 暗色模式 KaTeX 适配 */
        [data-theme="dark"] .katex-display {
            background-color: transparent;
        }
        
        [data-theme="dark"] .katex {
            color: #f7fafc; /* 更亮的文本颜色 */
        }

        /* 暗色模式 CodeMirror 样式调整 */
        [data-theme="dark"] .CodeMirror {
            background-color: #1a202c !important;
            color: #f7fafc !important; 
        }

        [data-theme="dark"] .CodeMirror-gutters {
            background-color: #1a202c !important;
            border-right: 1px solid #2d3748 !important;
        }

        [data-theme="dark"] .CodeMirror-linenumber {
            color: #718096 !important;
        }
        
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: var(--font-sans);
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
            overflow: hidden;
        }

        /* 主容器 */
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        /* 重新设计的顶部导航 */
        .header {
            display: flex;
            flex-direction: row; /* 确保水平排列 */
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: var(--bg-color);
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            z-index: 10;
            height: 40px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }

        .header-center {
            flex: 2;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.75rem;
            flex: 1;
        }

        .app-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 标题打字动画效果 */
        .typing-title-container {
            width: 280px; /* 固定宽度，防止文本变化导致布局变化 */
            text-align: center;
            overflow: hidden;
        }

        .typing-title {
            position: relative;
            display: inline-block;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            transition: color 0.3s ease;
            min-height: 1.5em;
            line-height: 1.5;
            margin: 0;
        }

        .typing-title::after {
            content: '|';
            position: absolute;
            right: -4px;
            top: 0;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: -320px; /* 更宽的侧边栏 */
            height: 100%;
            width: 320px;
            background-color: var(--bg-color);
            border-right: 1px solid var(--border-color);
            z-index: 1000;
            transition: left 0.3s ease;
            box-shadow: var(--shadow-lg);
            overflow-y: auto;
        }

        .sidebar.open {
            left: 0;
        }

        .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--primary);
        }

        .sidebar-content {
            padding: 1rem;
        }

        .sidebar-section {
            margin-bottom: 1.5rem;
        }

        .sidebar-section h4 {
            font-size: 1rem;
            margin: 0 0 0.75rem 0;
            color: var(--text-color);
        }

        .sidebar-divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 1.5rem 0;
        }

        .btn-block {
            display: block;
            width: 100%;
            text-align: left;
            margin-bottom: 0.5rem;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .empty-favorites {
            padding: 0.5rem;
            color: #718096;
            font-style: italic;
            text-align: center;
        }

        /* 侧边栏按钮网格布局 */
        .sidebar-buttons-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            width: 100%;
        }

        .btn-grid-item {
            padding: 0.5rem;
            text-align: center;
            font-size: 0.8rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 主内容区域 */
        .main-container {
            display: flex;
            flex: 1;
            overflow: hidden;
            position: relative;
            height: 100%;
        }

        /* 编辑器和预览容器 */
        .editor-container, .preview-container {
            flex: 1 0 0;
            min-width: 0;
            height: 100%;
            position: relative;
            transition: flex 0.3s ease;
        }

        .editor-container.fullwidth, .preview-container.fullwidth {
            flex: 2 0 0;
        }

        .editor-container.hidden, .preview-container.hidden {
            flex: 0;
            overflow: hidden;
            width: 0;
            padding: 0;
        }

        /* CodeMirror 自定义样式 */
        .CodeMirror {
            height: calc(100% - 20px);
            font-family: var(--font-mono);
            font-size: 16px;
            line-height: 1.6;
            padding: 1rem;
            background-color: var(--bg-color) !important;
            color: var(--text-color) !important;
        }

        .CodeMirror-lines {
            padding: 1rem 0;
        }

        .CodeMirror-gutters {
            border-right: 1px solid var(--border-color) !important;
            background-color: var(--bg-color) !important;
        }

        .CodeMirror-linenumber {
            color: #a0aec0 !important;
        }

        /* 预览区样式 */
        .preview-container {
            padding: 0;
            background-color: var(--bg-color);
            border-left: 1px solid var(--border-color);
        }

        .markdown-preview {
            height: calc(100% - 50px);
            overflow-y: auto;
            padding: 2rem ;
            font-family: var(--font-sans);
        }

        /* 自动保存历史记录样式 */
        .autosave-history-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .autosave-history-modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .autosave-history-container {
            background-color: var(--bg-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 600px;
            max-width: 90%;
            max-height: 80vh;
            padding: 1.5rem;
            overflow-y: auto;
        }
        
        .autosave-history-title {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            color: var(--text-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .autosave-history-list {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .autosave-history-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: var(--radius-md);
            background-color: rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .autosave-history-item:hover {
            background-color: var(--highlight-color);
        }
        
        .autosave-history-date {
            font-weight: bold;
        }
        
        .autosave-history-preview {
            color: #718096;
            font-size: 0.85rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 300px;
            margin-top: 0.25rem;
        }
        
        .autosave-history-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        [data-theme="dark"] .autosave-history-item {
            background-color: rgba(255, 255, 255, 0.05);
        }


        /* GitHub markdown 样式覆盖 */
        .markdown-body {
            color: var(--text-color) !important;
            background-color: transparent !important;
            font-family: var(--font-sans);
            font-size: 16px;
            line-height: 1.6;
        }

        .markdown-body pre {
            position: relative;
            background-color: #f6f8fa !important;
            border-radius: var(--radius-md);
            margin: 1.5rem 0;
            padding: 1rem;
            overflow: auto;
        }

        [data-theme="dark"] .markdown-body pre {
            background-color: #2d3748 !important;
        }

        .markdown-body code {
            font-family: var(--font-mono);
            font-size: 14px;
            border-radius: 3px;
            padding: 0.2em 0.4em;
        }

        .markdown-body pre code {
            padding: 0;
            border-radius: 0;
            font-size: 14px;
            line-height: 1.6;
        }

        /* 视图下拉菜单样式 */
        .view-dropdown .btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .toolbar-group {
            display: flex;
            gap: 0.5rem;
        }

        .toolbar-divider {
            width: 1px;
            background-color: var(--border-color);
            margin: 0 0.5rem;
        }

        /* 按钮 - 统一样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            border: none;
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .btn:hover {
            background-color: var(--highlight-color);
        }

        .btn-icon {
            padding: 0.5rem;
            border-radius: var(--radius-md);
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-success {
            background-color: var(--success);
            color: white;
            border: 1px solid var(--success);
        }

        .btn-success:hover {
            opacity: 0.9;
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
            border: 1px solid var(--danger);
        }

        .btn-danger:hover {
            opacity: 0.9;
        }

        .btn-warning {
            background-color: var(--warning);
            color: white;
            border: 1px solid var(--warning);
        }

        .btn-warning:hover {
            opacity: 0.9;
        }

        .btn-info {
            background-color: var(--info);
            color: white;
            border: 1px solid var(--info);
        }

        .btn-info:hover {
            opacity: 0.9;
        }

        /* 修改彩色按钮悬停样式 */
        .btn-primary:hover,
        .btn-success:hover,
        .btn-danger:hover,
        .btn-warning:hover,
        .btn-info:hover {
            opacity: 1;
            color: white; /* 保持文字为白色 */
            background-color: rgba(0, 197, 118, 0.7); /* 添加灰色背景，确保与白色文字形成对比 */
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3), 0 0 20px currentColor; /* 光晕效果 */
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); /* 文字发光效果 */
            transform: translateY(-2px); /* 轻微上浮效果 */
            transition: all 0.3s ease;
        }

        /* 为每个按钮类型增加独特光效 */
        .btn-primary:hover { border-color: #83a4ff; }
        .btn-success:hover { border-color: #7df0ff; }
        .btn-danger:hover { border-color: #ff94c3; }
        .btn-warning:hover { border-color: #ffbd6f; }
        .btn-info:hover { border-color: #98ebf1; }

        /* 为按钮添加文字阴影，提高对比度 */
        .btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info {
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }

        .markdown-body pre:hover .code-copy-btn {
            opacity: 1;
        }

        /* 下拉菜单 */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 10rem;
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-md);
            z-index: 1000;
            margin-top: 0.25rem;
        }

        .dropdown-content.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background-color: var(--highlight-color);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 0.25rem 0;
        }

        /* 状态栏 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 1.5rem;
            background-color: var(--bg-color);
            border-top: 1px solid var(--border-color);
            font-size: 0.875rem;
            color: #718096;
            transition: var(--transition);
            height: 24px;
        }

        .status-metrics {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        /* 自动保存指示器 */
        .autosave-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--success);
            font-size: 0.75rem;
        }

        .autosave-indicator.saving {
            animation: pulse 1s ease;
        }

        /* 加载指示器 */
        .loading-indicator {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--bg-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            padding: 0.75rem 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            z-index: 1000;
            opacity: 0;
            transform: translateY(1rem);
            transition: opacity 0.3s ease, transform 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .loading-indicator.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid rgba(67, 97, 238, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-0.5rem); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 主题切换 */
        .theme-switch {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--radius-md);
            transition: var(--transition);
        }

        .theme-switch:hover {
            background-color: var(--highlight-color);
        }

        /* 文件拖放区域 */
        .drag-area {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .drag-area.active {
            visibility: visible;
            opacity: 1;
        }

        .drag-area-content {
            background-color: var(--bg-color);
            padding: 2rem;
            border-radius: var(--radius-lg);
            text-align: center;
            box-shadow: var(--shadow-lg);
        }


        /* 移动端响应式 */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.75rem;
                height: auto;
            }

            .header-left,
            .header-center,
            .header-right {
                width: 100%;
                justify-content: center;
            }
            
            .main-container {
                flex-direction: column;
                height: calc(100% - 130px);
            }
            
            .editor-container, .preview-container {
                height: 50%;
                border-left: none;
            }
            
            .editor-container {
                border-bottom: 1px solid var(--border-color);
            }
            
            .preview-container {
                border-top: 1px solid var(--border-color);
            }
            
            .markdown-preview {
                padding: 1rem;
            }
            
            .status-bar {
                padding: 0.5rem;
            }
            
            .status-metrics {
                gap: 0.75rem;
                font-size: 0.75rem;
            }
        }

        /* LaTeX 样式美化 - 透明背景 */
        .katex-display {
            margin: 1.5rem 0;
            overflow-x: auto;
            overflow-y: hidden;
            background-color: transparent !important;
            border-radius: var(--radius-md);
        }

        /* 脚注样式 */
        .footnotes {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .footnotes ol {
            padding-left: 1.5rem;
        }

        .footnotes li {
            font-size: 0.875rem;
            color: #718096;
        }

        .footnote-ref, .footnote-backref {
            color: var(--primary);
            text-decoration: none;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.75rem 1.25rem;
            border-radius: var(--radius-md);
            background-color: var(--success);
            color: white;
            box-shadow: var(--shadow-md);
            z-index: 2000;
            opacity: 0;
            transform: translateY(-1rem);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .notification.error {
            background-color: var(--danger);
        }

        .notification.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: var(--primary);
        }

        input:checked + .slider:before {
            transform: translateX(16px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* 侧边栏遮罩 */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* 媒体预览样式 */
        .markdown-body video,
        .markdown-body img {
            max-width: 100%;
            border-radius: var(--radius-md);
            margin: 1rem 0;
        }

        .markdown-body .svg-image {
            background-color: white;
            padding: 0.5rem;
        }

        [data-theme="dark"] .markdown-body .svg-image {
            background-color: #2d3748;
        }
        
        /* 收藏项目编辑对话框 */
        .favorite-edit-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .favorite-edit-modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .favorite-edit-container {
            background-color: var(--bg-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 400px;
            max-width: 90%;
            padding: 1.5rem;
        }
        
        .favorite-edit-title {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            color: var(--text-color);
        }
        
        .favorite-edit-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .form-group label {
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-group input {
            padding: 0.75rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }
        
        .favorite-edit-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            margin-top: 1.5rem;
        }

        /* 添加居中视图模式的样式 */
        .editor-container.centered, .preview-container.centered {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .editor-container.centered .CodeMirror {
            width: 70%;
            max-width: 900px;
            height: auto !important;
            min-height: calc(100vh - 150px);
            box-shadow: var(--shadow-md);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .preview-container.centered .markdown-preview {
            width: 70%;
            max-width: 900px;
            background-color: var(--bg-color);
            box-shadow: var(--shadow-md);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            padding: 2rem;
        }

        /* 代码复制按钮改进 - 固定位置 */
        .markdown-body pre {
            position: relative;
            overflow-x: auto !important; /* 强制水平滚动 */
            overflow-y: hidden; /* 避免垂直方向出现双重滚动条 */
            white-space: pre; /* 保持代码格式 */
            word-wrap: normal; /* 不允许单词换行 */
            padding: 1rem;
            background-color: #f6f8fa ;
            border-radius: var(--radius-md);
            margin: 1.5rem 0;
            max-width: 100% !important; /* 确保不超过容器宽度 */
        }

        .markdown-body pre code {
            display: block;
            overflow-x: visible; /* 让内容可以水平延伸 */
            padding: 0;
            background-color: transparent;
            white-space: pre;
            word-break: normal;
            font-family: var(--font-mono);
            font-size: 14px;
        }


        /* 代码和公式复制按钮样式 */
        .code-copy-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            opacity: 0;
            transition: all 0.2s ease;
            z-index: 10;
            color: var(--text-color);
            backdrop-filter: blur(4px);
        }

        .markdown-body pre:hover .code-copy-btn,
        .katex-display:hover .code-copy-btn {
            opacity: 1;
        }

        .code-copy-btn:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .code-copy-btn.copied {
            background-color: var(--success);
            color: white;
            opacity: 1;
        }

        /* 暗色模式下的复制按钮 */
        [data-theme="dark"] .code-copy-btn {
            background-color: rgba(30, 30, 30, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
        }

        [data-theme="dark"] .code-copy-btn:hover {
            background-color: rgba(40, 40, 40, 0.7);
        }

        [data-theme="dark"] .code-copy-btn.copied {
            background-color: var(--success);
            color: white;
        }

        /* 为公式块设置相对位置 */
        .katex-display {
            position: relative;
        }

        /* 确保代码块和公式块中的内容不会覆盖按钮 */
        .markdown-body pre,
        .katex-display {
            overflow: visible !important;
        }

        .markdown-body pre:hover .code-copy-btn {
            opacity: 0.9;
        }

        /* 自定义样式滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        /* 历史记录侧边栏部分样式 */
        .sidebar-history-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 0.5rem;
        }

        .sidebar-history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius-md);
            margin-bottom: 0.5rem;
            background-color: rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: var(--transition);
        }

        .sidebar-history-item:hover {
            background-color: var(--highlight-color);
        }

        .history-item-info {
            flex: 1;
            overflow: hidden;
        }

        .history-item-date {
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--primary);
        }

        .history-item-preview {
            font-size: 0.7rem;
            color: #718096;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 0.1rem;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .empty-history {
            padding: 0.5rem;
            color: #718096;
            font-style: italic;
            text-align: center;
            font-size: 0.85rem;
        }

        .setting-select {
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 0.85rem;
        }

        [data-theme="dark"] .sidebar-history-item {
            background-color: rgba(255, 255, 255, 0.05);
        }

        /* 设置输入框样式 */
        .setting-input-container {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .setting-input {
            width: 60px;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-size: 0.85rem;
            text-align: right;
        }

        .setting-unit {
            font-size: 0.85rem;
            color: var(--text-color);
            opacity: 0.8;
        }

        /* 收藏列表优化布局 */
        .favorite-item {
            display: flex;
            flex-direction: column;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius-md);
            margin-bottom: 0.5rem;
            background-color: rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: var(--transition);
        }

        .favorite-item:hover {
            background-color: var(--highlight-color);
        }

        .favorite-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .favorite-item-title {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 0.9rem;
            flex: 1;
        }

        .favorite-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .favorite-item-time {
            font-size: 0.75rem;
            color: #718096;
        }

        .favorite-item-actions {
            display: flex;
            gap: 0.25rem;
        }

        .favorite-action-btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
            border-radius: var(--radius-sm);
            background-color: transparent;
            border: none;
            color: var(--text-color);
            opacity: 0.6;
            transition: var(--transition);
        }

        /* 靠到最右边 */
        #favorites-sort-select {
            margin-left: 11vh;
        }

        .favorites-sort {
            margin-bottom: 1rem;
        }

        .favorite-action-btn:hover {
            opacity: 1;
            background-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] .favorite-item {
            background-color: rgba(255, 255, 255, 0.05);
        }

        [data-theme="dark"] .favorite-action-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* 快捷键帮助弹窗样式 */
        .shortcuts-help-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .shortcuts-help-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .shortcuts-help-container {
            background-color: var(--bg-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 650px;
            max-width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            padding: 1.5rem;
        }

        .shortcuts-help-title {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--text-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .shortcuts-help-content {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .shortcuts-section {
            flex: 1 0 45%;
            min-width: 250px;
        }

        .shortcuts-section h3 {
            font-size: 1rem;
            color: var(--primary);
            margin-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.25rem;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.35rem 0;
            font-size: 0.9rem;
        }

        .shortcut-keys {
            display: flex;
            gap: 0.25rem;
            align-items: center;
            min-width: 120px;
        }

        .shortcut-keys kbd {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 3px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            padding: 0.1rem 0.4rem;
            font-size: 0.8rem;
            font-family: var(--font-sans);
            color: var(--text-color);
        }

        .shortcut-desc {
            color: var(--text-color);
            opacity: 0.9;
        }

        /* 帮助按钮样式 */
        .help-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: var(--primary);
            color: white;
            font-weight: bold;
            margin-right: 0.5rem;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .help-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(67, 97, 238, 0.3);
        }

        /* 优化快捷键帮助对话框样式 */
        .shortcuts-help-container {
            background-color: var(--bg-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            width: 700px;
            max-width: 95%;
            max-height: 85vh;
            overflow-y: auto;
            padding: 1.5rem;
        }

        .shortcuts-help-content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .shortcuts-section {
            min-width: 280px;
        }

        /* 增加滚动条美化 */
        .shortcuts-help-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .shortcuts-help-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .shortcuts-help-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }

        [data-theme="dark"] .shortcuts-help-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }

        [data-theme="dark"] .shortcuts-help-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        /* 快捷键元素美化 */
        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.35rem 0;
            font-size: 0.9rem;
            border-bottom: 1px dotted var(--border-color);
        }

        .shortcut-item:last-child {
            border-bottom: none;
        }

        /* Mermaid 相关样式 */
        .mermaid-wrapper {
            position: relative;
            margin: 1.5rem 0;
            border-radius: var(--radius-md);
            overflow: visible;
        }

        .mermaid-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .mermaid-toggle-btn,
        .mermaid-actions .code-copy-btn {
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            opacity: 0.7;
            transition: all 0.2s ease;
            z-index: 10;
            color: var(--text-color);
            backdrop-filter: blur(4px);
            display: inline-flex;  /* 确保按钮内容正确显示 */
            align-items: center;   /* 垂直居中图标和文本 */
        }

        .mermaid-toggle-btn:hover,
        .mermaid-actions .code-copy-btn:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        .mermaid-code {
            background-color: #f6f8fa;
            border-radius: var(--radius-md);
            position: relative;
        }

        .mermaid-diagram {
            padding: 1rem;
            background-color: #f6f8fa;
            border-radius: var(--radius-md);
            overflow: auto;
            text-align: center;
        }

        [data-theme="dark"] .mermaid-code,
        [data-theme="dark"] .mermaid-diagram {
            background-color: #2d3748;
        }

        [data-theme="dark"] .mermaid-toggle-btn {
            background-color: rgba(30, 30, 30, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
        }

        [data-theme="dark"] .mermaid-toggle-btn:hover {
            background-color: rgba(40, 40, 40, 0.7);
        }

        .mermaid-wrapper:hover .mermaid-toggle-btn,
        .mermaid-wrapper:hover .code-copy-btn {
            opacity: 0.9;
        }

        /* 媒体拖放区域样式 */
        .media-drop-zone.drag-over {
            border: 2px dashed var(--primary);
            background-color: rgba(67, 97, 238, 0.05);
        }

        /* 预览区域内的媒体样式优化 */
        .markdown-body video {
            max-width: 100%;
            border-radius: var(--radius-md);
            margin: 1rem 0;
            background-color: #000;
        }

        .markdown-body img.svg-image {
            background-color: white;
            padding: 0.5rem;
        }

        [data-theme="dark"] .markdown-body img.svg-image {
            background-color: #2d3748;
        }

    </style>
</head>
<body>
    <!-- 加载指示器 -->
    <div class="loading-indicator" id="loading-indicator">
        <div class="loading-spinner"></div>
        <span>正在处理...</span>
    </div>
    
    <!-- 文件拖放区域 -->
    <div class="drag-area" id="drag-area">
        <div class="drag-area-content">
            <i class="fas fa-cloud-upload-alt fa-3x" style="margin-bottom: 1rem; color: var(--primary);"></i>
            <h3>拖放文件至此处</h3>
            <p>支持 .md 文件</p>
        </div>
    </div>
    
    <!-- 收藏编辑对话框 -->
    <div class="favorite-edit-modal" id="favorite-edit-modal">
        <div class="favorite-edit-container">
            <h3 class="favorite-edit-title">编辑收藏</h3>
            <div class="favorite-edit-form">
                <div class="form-group">
                    <label for="favorite-title">标题</label>
                    <input type="text" id="favorite-title" placeholder="输入收藏标题">
                </div>
                <div class="favorite-edit-actions">
                    <button class="btn" id="cancel-edit-btn">取消</button>
                    <button class="btn btn-primary" id="save-edit-btn">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>文档管理</h3>
                <button id="close-sidebar" class="btn btn-icon">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h4>文件操作</h4>
                    <div class="sidebar-buttons-grid">
                        <button class="btn btn-grid-item btn-primary" id="sidebar-save-btn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                        <button class="btn btn-grid-item btn-info" id="sidebar-load-btn">
                            <i class="fas fa-folder-open"></i> 加载
                        </button>
                        <button class="btn btn-grid-item btn-info" id="sidebar-import-btn">
                            <i class="fas fa-file-import"></i> 导入
                        </button>
                        <button class="btn btn-grid-item btn-success" id="sidebar-export-md-btn">
                            <i class="fas fa-file-alt"></i> 导出MD
                        </button>
                        <button class="btn btn-grid-item btn-success" id="sidebar-export-html-btn">
                            <i class="fas fa-file-code"></i> 导出HTML
                        </button>
                        <button class="btn btn-grid-item btn-success" id="sidebar-export-pdf-btn">
                            <i class="fas fa-file-pdf"></i> 导出PDF
                        </button>
                    </div>
                </div>
                <div class="sidebar-divider"></div>
                <div class="sidebar-section">
                    <h4>设置</h4>
                    <div class="setting-item">
                        <span>暗色模式</span>
                        <label class="switch">
                            <input type="checkbox" id="dark-mode-toggle">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <div class="setting-item">
                        <span>自动保存</span>
                        <label class="switch">
                            <input type="checkbox" id="auto-save-toggle">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <!-- 自动保存时间间隔设置 -->
                    <div class="setting-item auto-save-settings" id="auto-save-settings" style="display: none;">
                        <span>保存间隔 (秒)</span>
                        <div class="setting-input-container">
                            <input type="number" id="auto-save-interval" class="setting-input" min="5" max="3600" value="60">
                            <span class="setting-unit">秒</span>
                        </div>
                    </div>
                    <!-- 历史记录最大数量设置 -->
                    <div class="setting-item auto-save-settings" id="history-limit-settings" style="display: none;">
                        <span>最大历史数</span>
                        <div class="setting-input-container">
                            <input type="number" id="history-max-items" class="setting-input" min="5" max="500" value="30">
                            <span class="setting-unit">条</span>
                        </div>
                    </div>
                </div>
                <div class="sidebar-divider"></div>
                <div class="sidebar-section">
                    <div class="favorites-header">
                        <h4>收藏列表</h4>
                        <div class="favorites-sort">
                            <span>排序：</span>
                            <select id="favorites-sort-select" class="btn btn-sm">
                                <option value="newest">最新</option>
                                <option value="oldest">最旧</option>
                                <option value="alphabetical">字母顺序</option>
                            </select>
                        </div>
                    </div>
                    <div id="favorites-list" class="favorites-list">
                        <!-- 收藏项目将动态添加在这里 -->
                        <div class="empty-favorites">还没有收藏项目</div>
                    </div>
                </div>
                <div class="sidebar-divider"></div>
                <!-- 添加历史记录 -->
                <div class="sidebar-section">
                    <div class="history-header">
                        <h4>自动保存历史</h4>
                        <button class="btn btn-sm btn-icon" id="clear-history-btn" title="清空历史记录">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div id="sidebar-history-list" class="sidebar-history-list">
                        <!-- 历史记录将动态添加在这里 -->
                        <div class="empty-history">没有历史记录</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏遮罩 -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- 重新设计后的顶部导航 -->
        <header class="header">
            <div class="header-left">
                <button class="btn btn-icon" id="toggle-sidebar-btn" title="打开菜单">
                    <i class="fas fa-bars"></i>
                </button>
                
                <!-- 格式化工具栏 - 移到左侧 -->
                <div class="toolbar-group">
                    <button class="btn btn-icon" title="加粗" id="bold-btn">
                        <i class="fas fa-bold"></i>
                    </button>
                    <button class="btn btn-icon" title="斜体" id="italic-btn">
                        <i class="fas fa-italic"></i>
                    </button>
                    <button class="btn btn-icon" title="链接" id="link-btn">
                        <i class="fas fa-link"></i>
                    </button>
                    <button class="btn btn-icon" title="代码" id="code-btn">
                        <i class="fas fa-code"></i>
                    </button>
                    <button class="btn btn-icon" title="图片" id="image-btn">
                        <i class="fas fa-image"></i>
                    </button>
                    <button class="btn btn-icon" title="数学公式" id="math-btn">
                        <i class="fas fa-square-root-alt"></i>
                    </button>
                </div>
                
                <div class="toolbar-divider"></div>
                
                <!-- 列表和表格 -->
                <div class="toolbar-group">
                    <button class="btn btn-icon" title="表格" id="table-btn">
                        <i class="fas fa-table"></i>
                    </button>
                    <button class="btn btn-icon" title="无序列表" id="list-btn">
                        <i class="fas fa-list-ul"></i>
                    </button>
                    <button class="btn btn-icon" title="有序列表" id="ordered-list-btn">
                        <i class="fas fa-list-ol"></i>
                    </button>
                    <button class="btn btn-icon" title="任务列表" id="task-list-btn">
                        <i class="fas fa-tasks"></i>
                    </button>
                    <button class="btn btn-icon" title="引用" id="quote-btn">
                        <i class="fas fa-quote-right"></i>
                    </button>
                    <button class="btn btn-icon" title="水平线" id="hr-btn">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn btn-icon" title="脚注" id="footnote-btn">
                        <i class="fas fa-sticky-note"></i>
                    </button>
                    <button class="btn btn-icon" title="Mermaid 图表" id="mermaid-btn">
                        <i class="fas fa-project-diagram"></i>
                    </button>
                </div>
            </div>
            
            <div class="header-center">
                <!-- 标题放中间 - 固定宽度容器 -->
                <div class="typing-title-container">
                    <h1 class="typing-title" id="typing-title">超级 Markdown 编辑器</h1>
                </div>
            </div>
            
            <div class="header-right">
                <!-- 基本操作移到右侧 -->
                <div class="toolbar-group">
                    <div class="dropdown">
                        <button class="btn btn-primary" id="export-btn">
                            <i class="fas fa-file-export"></i> 导出
                        </button>
                        <div class="dropdown-content" id="export-dropdown">
                            <a class="dropdown-item" id="export-markdown">
                                <i class="fas fa-file-alt"></i> 导出 Markdown
                            </a>
                            <a class="dropdown-item" id="export-html">
                                <i class="fas fa-file-code"></i> 导出 HTML
                            </a>
                            <a class="dropdown-item" id="export-pdf">
                                <i class="fas fa-file-pdf"></i> 导出 PDF
                            </a>
                        </div>
                    </div>
                    
                    <button class="btn btn-success" id="save-btn">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    
                    <button class="btn btn-info" id="load-btn">
                        <i class="fas fa-folder-open"></i> 加载
                    </button>
                    
                    <button class="btn btn-info" id="import-btn">
                        <i class="fas fa-file-import"></i> 导入
                    </button>
                    
                    <button class="btn btn-warning" id="favorite-btn" title="添加到收藏">
                        <i class="fas fa-star"></i> 收藏
                    </button>
                </div>
                
                <div class="toolbar-divider"></div>
                
                <!-- 帮助按钮 -->
                <button class="btn btn-icon" id="shortcuts-help-btn" title="键盘快捷键 (F1)">
                    <i class="fas fa-question-circle"></i>
                </button>

                <!-- 单一视图切换按钮 -->
                <div class="dropdown view-dropdown">
                    <button class="btn" id="view-mode-btn">
                        <i class="fas fa-columns"></i> 视图
                    </button>
                    <div class="dropdown-content" id="view-dropdown">
                        <a class="dropdown-item" id="editor-only-btn">
                            <i class="fas fa-edit"></i> 仅编辑 (全宽)
                        </a>
                        <a class="dropdown-item" id="editor-centered-btn">
                            <i class="fas fa-align-center"></i> 仅编辑 (居中)
                        </a>
                        <a class="dropdown-item" id="split-view-btn">
                            <i class="fas fa-columns"></i> 分屏模式
                        </a>
                        <a class="dropdown-item" id="preview-only-btn">
                            <i class="fas fa-eye"></i> 仅预览 (全宽)
                        </a>
                        <a class="dropdown-item" id="preview-centered-btn">
                            <i class="fas fa-eye"></i> 仅预览 (居中)
                        </a>
                    </div>
                </div>
                <button class="btn btn-icon" title="切换主题" id="theme-switch">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>
        
        <!-- 主内容区域 -->
        <div class="main-container">
            <!-- 编辑器 -->
            <div class="editor-container" id="editor-container">
                <textarea id="editor"></textarea>
            </div>
            
            <!-- 预览 -->
            <div class="preview-container" id="preview-container">
                <div class="markdown-preview markdown-body" id="preview"></div>
            </div>
        </div>
        
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-metrics">
                <span id="word-count">0 字</span>
                <span id="character-count">0 字符</span>
                <!-- 自动保存指示器 -->
                <div class="autosave-indicator" id="autosave-indicator">
                    <i class="fas fa-save"></i>
                    <span id="autosave-status">自动保存：已关闭</span>
                </div>
            </div>
            <div>
                <span id="current-time"></span>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏文件输入 -->
    <input type="file" id="sidebar-file-input" accept=".md" style="display:none;">
    <!-- 工具栏文件输入 -->
    <input type="file" id="file-input-toolbar" accept=".md" style="display: none;">

    <!-- 自动保存历史对话框 -->
    <div class="autosave-history-modal" id="autosave-history-modal">
        <div class="autosave-history-container">
            <div class="autosave-history-title">
                <span>自动保存历史</span>
                <button class="btn btn-icon" id="close-history-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="autosave-history-list" id="autosave-history-list">
                <!-- 历史记录将动态添加在这里 -->
            </div>
        </div>
    </div>

    <div class="shortcuts-help-modal" id="shortcuts-help-modal">
        <div class="shortcuts-help-container">
            <div class="shortcuts-help-title">
                <span>键盘快捷键</span>
                <button class="btn btn-icon" id="close-shortcuts-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="shortcuts-help-content">
                <div class="shortcuts-section">
                    <h3>文件操作</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>S</kbd></span>
                        <span class="shortcut-desc">保存文档</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>O</kbd></span>
                        <span class="shortcut-desc">加载文档</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>S</kbd></span>
                        <span class="shortcut-desc">导出 Markdown</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>E</kbd></span>
                        <span class="shortcut-desc">导出 HTML</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>P</kbd></span>
                        <span class="shortcut-desc">导出 PDF</span>
                    </div>
                </div>
                
                <div class="shortcuts-section">
                    <h3>编辑格式</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>B</kbd></span>
                        <span class="shortcut-desc">粗体文本</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>I</kbd></span>
                        <span class="shortcut-desc">斜体文本</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>K</kbd></span>
                        <span class="shortcut-desc">插入链接</span>
                    </div>
                </div>
                
                <div class="shortcuts-section">
                    <h3>视图控制</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Alt</kbd> + <kbd>1</kbd></span>
                        <span class="shortcut-desc">仅编辑模式(全宽)</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Alt</kbd> + <kbd>2</kbd></span>
                        <span class="shortcut-desc">仅编辑模式(居中)</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Alt</kbd> + <kbd>3</kbd></span>
                        <span class="shortcut-desc">分屏模式</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Alt</kbd> + <kbd>4</kbd></span>
                        <span class="shortcut-desc">仅预览模式(全宽)</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Alt</kbd> + <kbd>5</kbd></span>
                        <span class="shortcut-desc">仅预览模式(居中)</span>
                    </div>
                </div>
                
                <div class="shortcuts-section">
                    <h3>插入内容</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>I</kbd></span>
                        <span class="shortcut-desc">插入图片</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>C</kbd></span>
                        <span class="shortcut-desc">插入代码块</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>M</kbd></span>
                        <span class="shortcut-desc">插入数学公式</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>T</kbd></span>
                        <span class="shortcut-desc">插入表格</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>L</kbd></span>
                        <span class="shortcut-desc">插入无序列表</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>O</kbd></span>
                        <span class="shortcut-desc">插入有序列表</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>K</kbd></span>
                        <span class="shortcut-desc">插入任务列表</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>Q</kbd></span>
                        <span class="shortcut-desc">插入引用</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>H</kbd></span>
                        <span class="shortcut-desc">插入水平线</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>F</kbd></span>
                        <span class="shortcut-desc">插入脚注</span>
                    </div>
                </div>
                
                <div class="shortcuts-section">
                    <h3>其他功能</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>F1</kbd> 或 <kbd>Ctrl</kbd> + <kbd>/</kbd></span>
                        <span class="shortcut-desc">显示此帮助</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>D</kbd></span>
                        <span class="shortcut-desc">切换暗色模式</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>F</kbd></span>
                        <span class="shortcut-desc">添加到收藏</span>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-keys"><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>O</kbd></span>
                        <span class="shortcut-desc">打开侧边栏</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/lib/codemirror.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.13/mode/markdown/markdown.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-clike.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-markup.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-css.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-java.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.8.0/dist/mermaid.min.js"></script>
    <script>
        // 全局变量
        let editor;
        let darkMode = false;
        let currentView = 'split'; // 'editor', 'preview', 或 'split'
        let renderTimeout;
        let favorites = []; // 收藏列表
        let autoSaveInterval; // 自动保存间隔
        let currentEditingFavorite = -1; // 当前正在编辑的收藏项目索引
        let lastAutoSaveTime = null; // 最后自动保存时间
        let autoSaveHistory = []; // 自动保存历史记录
        let MAX_HISTORY_ITEMS = 30; // 最大历史记录数量
        let AUTO_SAVE_INTERVAL_SECONDS = 60; // 自动保存间隔（秒）

        // 示例内容
        const exampleContent = `# Markdown & LaTeX 完整示例

## 1. Markdown 基础语法

### 1.1 文本格式化
**粗体文本** 和 *斜体文本* 以及 ***粗斜体文本***
~~删除线~~ 和 \`行内代码\`

### 1.2 列表
#### 无序列表
- 项目1
  - 子项目1.1
  - 子项目1.2
- 项目2

#### 有序列表
1. 第一步
2. 第二步
3. 第三步

### 1.3 引用
> 这是一段引用文本
>> 这是嵌套引用

### 1.4 表格
| 功能 | 语法 | 效果 |
|------|------|------|
| 粗体 | \`**文本**\` | **文本** |
| 斜体 | \`*文本*\` | *文本* |
| 删除线 | \`~~文本~~\` | ~~文本~~ |

### 1.5 代码块
\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
\`\`\`

\`\`\`javascript
// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
\`\`\`

## 2. LaTeX 数学公式

### 2.1 行内公式
爱因斯坦质能方程：$E = mc^2$
欧拉式：$e^{i\\pi} + 1 = 0$

### 2.2 行间公式
傅里叶变换：
$$
F(\\omega) = \\int_{-\\infty}^{\\infty} f(t)e^{-i\\omega t}dt
$$

### 2.3 矩阵
$$
\\begin{pmatrix}
a_{11} & a_{12} & a_{13} \\\\
a_{21} & a_{22} & a_{23} \\\\
a_{31} & a_{32} & a_{33}
\\end{pmatrix}
$$

### 2.4 多行公式
$$
\\begin{aligned}
\\nabla \\times \\vec{\\mathbf{B}} &= \\frac{1}{c}\\frac{\\partial\\vec{\\mathbf{E}}}{\\partial t} + \\frac{4\\pi}{c}\\vec{\\mathbf{j}} \\\\
\\nabla \\cdot \\vec{\\mathbf{E}} &= 4 \\pi \\rho \\\\
\\nabla \\times \\vec{\\mathbf{E}}&= -\\frac{1}{c}\\frac{\\partial\\vec{\\mathbf{B}}}{\\partial t} \\\\
\\nabla \\cdot \\vec{\\mathbf{B}} &= 0
\\end{aligned}
$$

### 2.5 化学方程式
$$
C_6H_{12}O_6 \\xrightarrow{\\text{发酵}} 2C_2H_5OH + 2CO_2
$$

## 3. 多媒体支持

### 3.1 图片插入
![示例图片](https://imgbed.killerbest.com/file/1738847659088_111.png)

### 3.2 SVG 图形
> **注意**：插入 SVG 代码时，必须确保所有代码在一行内，不要包含换行符或注释，否则无法被正确识别为 SVG 元素。

下面是一个简单的 SVG 圆形示例：

<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" stroke="green" stroke-width="4" fill="yellow" /></svg>

### 3.3 视频嵌入
支持嵌入 MP4、WebM 和 OGG 格式的视频：

<video controls src="https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.mp4" style="max-width: 100%;" title="示例视频"></video>

## 4. 高级功能

### 4.1 任务列表
- [x] 已完成任务
- [ ] 未完成任务
- [x] ~~已完成但已放弃的任务~~

### 4.2 脚注
这里一个脚注[^1]
另一个脚注示例[^2]

[^1]: 这是脚注的内容
[^2]: 这是第二个脚注的内容

### 4.3 链接
[访问GitHub](https://github.com)

### 4.4 HTML 支持
<details>
<summary>点击展开</summary>
这是展开的内容
</details>

<kbd>Ctrl</kbd> + <kbd>C</kbd> 复制

## 5. Mermaid 图表

### 5.1 流程图
\`\`\`mermaid
graph TD;
    A[开始] --> B{是否已登录?};
    B -->|是| C[显示用户面板];
    B -->|否| D[显示登录页面];
    C --> E[结束];
    D --> E[结束];
\`\`\`

### 5.2 序列图
\`\`\`mermaid
sequenceDiagram
    participant 用户
    participant 系统
    用户->>系统: 发起请求
    系统-->>用户: 响应请求
    Note right of 系统: 处理用户数据
    loop 每日检查
        用户->>系统: 发送心跳
        系统-->>用户: 确认在线
    end
\`\`\`

### 5.3 甘特图
\`\`\`mermaid
gantt
    title 项目时间线
    dateFormat  YYYY-MM-DD
    section 规划阶段
    需求分析      :done, des1, 2023-01-01, 2023-01-05
    系统设计      :active, des2, 2023-01-06, 2023-01-15
    section 开发阶段
    编码          :des3, after des2, 30d
    测试          :des4, after des3, 14d
    section 发布阶段
    部署上线      :des5, after des4, 7d
\`\`\`

### 5.4 类图
\`\`\`mermaid
classDiagram
    Person <|-- Student
    Person <|-- Teacher
    Person : +String name
    Person : +int age
    Person : +getName()
    Student : +String studentID
    Student : +getStudentID()
    Teacher : +String subject
    Teacher : +getSubject()
\`\`\`

### 5.5 思维导图
\`\`\`mermaid
mindmap
  root((思维导图))
    编程语言
      前端
        HTML
        CSS
        JavaScript
      后端
        Python
        Java
        Go
    数据库
      关系型
        MySQL
        PostgreSQL
      非关系型
        MongoDB
        Redis
    开发工具
      VS Code
      Git
      Docker
\`\`\`

## 6. 使用说明

- 图片可以通过复制后直接粘贴到编辑器中
- 视频和其他媒体文件可以拖放到预览区域
- 代码块支持语法高亮和一键复制功能
- 数学公式支持 LaTeX 语法
- Mermaid 图表支持切换代码和图表视图
`;

        // 动态标题内容
        const titles = [
            "Markdown 编辑器 ✨",
            "支持 LaTeX 渲染 📐",
            "代码高亮与复制 💡",
            "支持暗黑模式 🌙",
            "表格与任务列表 ✅",
            "支持全宽切换 🔄",
            "一键导出多种格式 📤",
            "实时字数统计 📊",
            "支持图片粘贴 📷",
            "收藏文档功能 ⭐"
        ];

        // 初始化应用程序
        document.addEventListener('DOMContentLoaded', function() {
            initTypewriter();
            initEditor();
            initMarked();
            initMermaid();
            setupEventListeners();
            initSidebar(); 
            updateWordCount();
            updateCurrentTime();
            initMediaPreview();
            initFavorites();
            initAutoSaveHistory();
            initShortcutsHelp(); // 添加初始化快捷键帮助
            setupGlobalKeyboardShortcuts(); // 添加全局快捷键
    
            // 检查暗黑模式偏好
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                toggleDarkMode();
            }
            
            // 设置初始视图
            setView('split');
        });

        // 初始化 Mermaid
        function initMermaid() {
            if (typeof mermaid !== 'undefined') {
                // 配置 Mermaid
                mermaid.initialize({
                    startOnLoad: true,
                    theme: darkMode ? 'dark' : 'default',
                    securityLevel: 'loose', // 放宽安全限制，允许点击交互
                    fontFamily: 'var(--font-sans)'
                });
                
                console.log('Mermaid 初始化完成');
            } else {
                console.warn('Mermaid 库未加载，图表功能可能不可用');
            }
        }

        // 检查库是否加载的函数
        function checkLibraryLoaded(library, libraryName) {
            if (!library) {
                console.warn(`${libraryName}库未加载，正在使用备用方案`);
                return false;
            }
            return true;
        }

        // 初始化打字机效果
        function initTypewriter() {
            const typingTitle = document.getElementById('typing-title');
            if (!typingTitle) {
                console.error('找不到标题元素');
                return;
            }
            
            class TypeWriter {
                constructor(element) {
                    this.element = element;
                    this.words = titles;
                    this.txt = '';
                    this.wordIndex = 0;
                    this.isDeleting = false;
                    this.typeSpeed = 100;
                    this.wait = Math.random() * 1500 + 500;
                }

                addSpecialEffect() {
                    const effects = [
                        () => this.element.style.animation = 'shake 0.5s ease',
                        () => this.element.style.animation = 'pulse 0.5s ease',
                        () => this.element.style.fontSize = '1.7em',
                        () => this.element.style.fontWeight = 'bold',
                        () => this.element.style.letterSpacing = '2px'
                    ];

                    // 随机选择一个特效
                    const effect = effects[Math.floor(Math.random() * effects.length)]();
                    
                    // 300ms后恢复正常
                    setTimeout(() => {
                        this.element.style.animation = '';
                        this.element.style.fontSize = '1.5em';
                        this.element.style.fontWeight = '600';
                        this.element.style.letterSpacing = 'normal';
                    }, 300);
                }

                tick() {
                    const currentWord = this.words[this.wordIndex];
                    const typeSpeed = this.isDeleting ? 30 : Math.random() * 100 + 100;

                    if (this.isDeleting) {
                        this.txt = currentWord.substring(0, this.txt.length - 1);
                    } else {
                        this.txt = currentWord.substring(0, this.txt.length + 1);
                        
                        // 随机特效
                        if (Math.random() < 0.1) {
                            // 打字错误效果
                            const wrongChar = String.fromCharCode(97 + Math.floor(Math.random() * 26));
                            this.element.innerHTML = this.txt + wrongChar;
                            this.addSpecialEffect();
                            setTimeout(() => {
                                this.element.innerHTML = this.txt;
                            }, 150);
                        }
                    }

                    this.element.innerHTML = this.txt;

                    let nextTick = typeSpeed;

                    if (!this.isDeleting && this.txt === currentWord) {
                        nextTick = this.wait;
                        this.isDeleting = true;
                    } else if (this.isDeleting && this.txt === '') {
                        this.isDeleting = false;
                        this.wordIndex = (this.wordIndex + 1) % this.words.length;
                        nextTick = 500;
                    }

                    setTimeout(() => this.tick(), nextTick);
                }

                start() {
                    this.tick();
                }
            }

            const typeWriter = new TypeWriter(typingTitle);
            typeWriter.start();
        }

        // 初始化 CodeMirror 编辑器
        function initEditor() {
            editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
                mode: 'markdown',
                lineNumbers: true,
                lineWrapping: true,
                theme: 'default',
                autofocus: true,
                tabSize: 4,
                indentUnit: 4,
                styleActiveLine: true,
                matchBrackets: true,
                extraKeys: {
                    "Ctrl-S": function(cm) {
                        saveToLocal();
                        return false; // 阻止默认行为
                    },
                    "Cmd-S": function(cm) {
                        saveToLocal();
                        return false;
                    },
                    "Ctrl-B": function(cm) {
                        insertMarkdown('**', '**');
                        return false;
                    },
                    "Cmd-B": function(cm) {
                        insertMarkdown('**', '**');
                        return false;
                    },
                    "Ctrl-I": function(cm) {
                        insertMarkdown('*', '*');
                        return false;
                    },
                    "Cmd-I": function(cm) {
                        insertMarkdown('*', '*');
                        return false;
                    },
                    "Ctrl-K": function(cm) {
                        insertLink();
                        return false;
                    },
                    "Cmd-K": function(cm) {
                        insertLink();
                        return false;
                    }
                }
            });
            
            // 设置初始内容
            editor.setValue(exampleContent);
            
            // 延迟触发初始渲染，确保库已加载
            setTimeout(() => {
                renderMarkdown();
            }, 500);
            
            // 添加变化事件监听器，实现实时预览
            editor.on('change', function() {
                showLoading();
                clearTimeout(renderTimeout);
                renderTimeout = setTimeout(() => {
                    renderMarkdown();
                    updateWordCount();
                    hideLoading();
                }, 300);
            });
        }

        // 添加全局键盘事件监听器来捕获其他快捷键
        function setupGlobalKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // 避免在输入框内触发不应该触发的快捷键
                const isInInput = e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA';
                
                if (isInInput && e.target !== editor.getInputField()) {
                    return;
                }
                
                // Ctrl+S 保存
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    saveToLocal();
                }
                
                // Ctrl+O 加载
                if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
                    e.preventDefault();
                    loadFromLocal();
                }
                
                // ===== 视图切换 =====
                // Alt+1: 仅编辑器 (全宽)
                if (e.altKey && e.key === '1') {
                    e.preventDefault();
                    setView('editor');
                }
                
                // Alt+2: 仅编辑器 (居中)
                if (e.altKey && e.key === '2') {
                    e.preventDefault();
                    setView('editor-centered');
                }
                
                // Alt+3: 分屏模式
                if (e.altKey && e.key === '3') {
                    e.preventDefault();
                    setView('split');
                }
                
                // Alt+4: 仅预览 (全宽)
                if (e.altKey && e.key === '4') {
                    e.preventDefault();
                    setView('preview');
                }
                
                // Alt+5: 仅预览 (居中)
                if (e.altKey && e.key === '5') {
                    e.preventDefault();
                    setView('preview-centered');
                }
                
                // F1 或 Ctrl+/ 打开帮助
                if (e.key === 'F1' || ((e.ctrlKey || e.metaKey) && e.key === '/')) {
                    e.preventDefault();
                    openShortcutsHelp();
                }
                
                // ===== 插入功能 =====
                // Ctrl+Alt+I 插入图片
                if (e.ctrlKey && e.altKey && e.key === 'i') {
                    e.preventDefault();
                    insertImage();
                }
                
                // Ctrl+Alt+C 插入代码块
                if (e.ctrlKey && e.altKey && e.key === 'c') {
                    e.preventDefault();
                    insertMarkdown('```\n', '\n```', 'code');
                }
                
                // Ctrl+Alt+M 插入数学公式
                if (e.ctrlKey && e.altKey && e.key === 'm') {
                    e.preventDefault();
                    insertMath();
                }
                
                // Ctrl+Alt+T 插入表格
                if (e.ctrlKey && e.altKey && e.key === 't') {
                    e.preventDefault();
                    insertTable();
                }
                
                // Ctrl+Alt+L 插入无序列表
                if (e.ctrlKey && e.altKey && e.key === 'l') {
                    e.preventDefault();
                    insertUnorderedList();
                }
                
                // Ctrl+Alt+O 插入有序列表
                if (e.ctrlKey && e.altKey && e.key === 'o') {
                    e.preventDefault();
                    insertOrderedList();
                }
                
                // Ctrl+Alt+K 插入任务列表
                if (e.ctrlKey && e.altKey && e.key === 'k') {
                    e.preventDefault();
                    insertTaskList();
                }
                
                // Ctrl+Alt+Q 插入引用
                if (e.ctrlKey && e.altKey && e.key === 'q') {
                    e.preventDefault();
                    insertQuote();
                }
                
                // Ctrl+Alt+H 插入水平线
                if (e.ctrlKey && e.altKey && e.key === 'h') {
                    e.preventDefault();
                    insertHorizontalRule();
                }
                
                // Ctrl+Alt+F 插入脚注
                if (e.ctrlKey && e.altKey && e.key === 'f') {
                    e.preventDefault();
                    insertFootnote();
                }
                
                // ===== 文件操作 =====
                // Ctrl+Alt+E 导出为HTML
                if (e.ctrlKey && e.altKey && e.key === 'e') {
                    e.preventDefault();
                    exportHtmlFile();
                }
                
                // Ctrl+Alt+P 导出为PDF
                if (e.ctrlKey && e.altKey && e.key === 'p') {
                    e.preventDefault();
                    exportPdfFile();
                }
                
                // Ctrl+Alt+S 导出为Markdown
                if (e.ctrlKey && e.altKey && e.key === 's') {
                    e.preventDefault();
                    exportMarkdownFile();
                }
                
                // ===== 其他操作 =====
                // Ctrl+Shift+D 切换暗色模式
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    toggleDarkMode();
                }
                
                // Ctrl+Shift+F 添加到收藏
                if (e.ctrlKey && e.shiftKey && e.key === 'F') {
                    e.preventDefault();
                    addToFavorites();
                }
                
                // Ctrl+Shift+O 打开侧边栏
                if (e.ctrlKey && e.shiftKey && e.key === 'O') {
                    e.preventDefault();
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebar-overlay');
                    if (sidebar && sidebarOverlay) {
                        sidebar.classList.add('open');
                        sidebarOverlay.classList.add('active');
                    }
                }
            });
        }

        // 初始化侧边栏
        function initSidebar() {
            const toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');
            const closeSidebarBtn = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const darkModeToggle = document.getElementById('dark-mode-toggle');
            const autoSaveToggle = document.getElementById('auto-save-toggle');
            const autoSaveIntervalSelect = document.getElementById('auto-save-interval');
            const historyMaxItemsSelect = document.getElementById('history-max-items');
            const favoritesSortSelect = document.getElementById('favorites-sort-select');
            const autoSaveSettings = document.getElementById('auto-save-settings');
            const historyLimitSettings = document.getElementById('history-limit-settings');
            const clearHistoryBtn = document.getElementById('clear-history-btn');
            
            if (!toggleSidebarBtn || !sidebar || !sidebarOverlay) {
                console.error('侧边栏必要元素缺失，无法初始化侧边栏');
                return;
            }
            
            // 打开侧边栏
            toggleSidebarBtn.addEventListener('click', function() {
                console.log('打开侧边栏');
                sidebar.classList.add('open');
                sidebarOverlay.classList.add('active');
            });
            
            // 关闭侧边栏
            function closeSidebar() {
                console.log('关闭侧边栏');
                sidebar.classList.remove('open');
                sidebarOverlay.classList.remove('active');
            }
            
            if (closeSidebarBtn) {
                closeSidebarBtn.addEventListener('click', closeSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }
            
            // 暗色模式开关
            if (darkModeToggle) {
                darkModeToggle.checked = darkMode;
                darkModeToggle.addEventListener('change', function() {
                    toggleDarkMode();
                });
            }
            
            // 自动保存开关
            if (autoSaveToggle) {
                // 从本地存储加载设置
                const autoSaveEnabled = localStorage.getItem('autoSaveEnabled') === 'true';
                autoSaveToggle.checked = autoSaveEnabled;
                
                // 加载保存间隔设置
                if (autoSaveIntervalSelect) {
                    const savedInterval = localStorage.getItem('autoSaveInterval');
                    if (savedInterval) {
                        AUTO_SAVE_INTERVAL_SECONDS = parseInt(savedInterval);
                        autoSaveIntervalSelect.value = savedInterval;
                    }
                    
                    // 使用 input 事件处理实时输入
                    autoSaveIntervalSelect.addEventListener('input', function() {
                        // 设置最小值
                        if (this.value < 5) this.value = 5;
                        // 设置最大值
                        if (this.value > 3600) this.value = 3600;
                        
                        AUTO_SAVE_INTERVAL_SECONDS = parseInt(this.value);
                        localStorage.setItem('autoSaveInterval', this.value);
                        
                        // 如果自动保存已启用，重新启动以应用新间隔
                        if (autoSaveToggle.checked) {
                            stopAutoSave();
                            startAutoSave();
                            showNotification(`自动保存间隔已更新: ${formatTimeInterval(AUTO_SAVE_INTERVAL_SECONDS)}`);
                        }
                    });
                }
                
                // 加载最大历史记录设置
                if (historyMaxItemsSelect) {
                    const savedMaxItems = localStorage.getItem('maxHistoryItems');
                    if (savedMaxItems) {
                        MAX_HISTORY_ITEMS = parseInt(savedMaxItems);
                        historyMaxItemsSelect.value = savedMaxItems;
                    }
                    
                    // 使用 input 事件处理实时输入
                    historyMaxItemsSelect.addEventListener('input', function() {
                        // 设置最小值
                        if (this.value < 5) this.value = 5;
                        // 设置最大值
                        if (this.value > 500) this.value = 500;
                        
                        MAX_HISTORY_ITEMS = parseInt(this.value);
                        localStorage.setItem('maxHistoryItems', this.value);
                        
                        // 裁剪当前历史记录以符合新的限制
                        if (autoSaveHistory.length > MAX_HISTORY_ITEMS) {
                            autoSaveHistory = autoSaveHistory.slice(-MAX_HISTORY_ITEMS);
                            localStorage.setItem('autoSaveHistory', JSON.stringify(autoSaveHistory));
                            // 更新侧边栏历史记录列表
                            updateSidebarHistoryList();
                            showNotification(`最大历史记录数量已更新: ${MAX_HISTORY_ITEMS}条`);
                        }
                    });
                }
                
                // 显示/隐藏相关设置
                if (autoSaveSettings && historyLimitSettings) {
                    autoSaveSettings.style.display = autoSaveEnabled ? 'flex' : 'none';
                    historyLimitSettings.style.display = autoSaveEnabled ? 'flex' : 'none';
                }
                
                // 设置自动保存
                if (autoSaveEnabled) {
                    startAutoSave();
                } else {
                    updateAutoSaveStatus(false);
                }
                
                autoSaveToggle.addEventListener('change', function() {
                    localStorage.setItem('autoSaveEnabled', this.checked);
                    
                    // 显示/隐藏相关设置
                    if (autoSaveSettings && historyLimitSettings) {
                        autoSaveSettings.style.display = this.checked ? 'flex' : 'none';
                        historyLimitSettings.style.display = this.checked ? 'flex' : 'none';
                    }
                    
                    if (this.checked) {
                        startAutoSave();
                        showNotification('已启用自动保存');
                    } else {
                        stopAutoSave();
                        showNotification('已禁用自动保存');
                    }
                });
            }

            // 清空历史记录按钮
            if (clearHistoryBtn) {
                clearHistoryBtn.addEventListener('click', clearAllHistory);
            }
            
            // 侧边栏按钮事件 - 添加空检查
            const sidebarSaveBtn = document.getElementById('sidebar-save-btn');
            const sidebarLoadBtn = document.getElementById('sidebar-load-btn');
            const sidebarImportBtn = document.getElementById('sidebar-import-btn');
            const sidebarFileInput = document.getElementById('sidebar-file-input');
            const sidebarExportMdBtn = document.getElementById('sidebar-export-md-btn');
            const sidebarExportHtmlBtn = document.getElementById('sidebar-export-html-btn');
            const sidebarExportPdfBtn = document.getElementById('sidebar-export-pdf-btn');
            
            if (sidebarSaveBtn) {
                sidebarSaveBtn.addEventListener('click', function() {
                    saveToLocal();
                    closeSidebar();
                });
            }
            
            if (sidebarLoadBtn) {
                sidebarLoadBtn.addEventListener('click', function() {
                    loadFromLocal();
                    closeSidebar();
                });
            }
            
            if (sidebarImportBtn && sidebarFileInput) {
                sidebarImportBtn.addEventListener('click', function() {
                    sidebarFileInput.click();
                    closeSidebar();
                });
            }
            
            if (sidebarExportMdBtn) {
                sidebarExportMdBtn.addEventListener('click', function() {
                    exportMarkdownFile();
                    closeSidebar();
                });
            }
            
            if (sidebarExportHtmlBtn) {
                sidebarExportHtmlBtn.addEventListener('click', function() {
                    exportHtmlFile();
                    closeSidebar();
                });
            }
            
            if (sidebarExportPdfBtn) {
                sidebarExportPdfBtn.addEventListener('click', function() {
                    exportPdfFile();
                    closeSidebar();
                });
            }
            
            // 收藏排序下拉框
            if (favoritesSortSelect) {
                favoritesSortSelect.addEventListener('change', function() {
                    sortFavorites(this.value);
                });
            }
            
            console.log('侧边栏初始化完成');
        }

        // 配置 marked 渲染器
        function initMarked() {
            if (typeof marked === 'undefined') {
                console.error('marked 库未加载');
                return;
            }
            
            // 设置自定义 marked 渲染器
            const renderer = new marked.Renderer();
            
            // 自定义代码渲染器，支持 mermaid 语法
            renderer.code = function(code, language, isEscaped) {
                // 处理 mermaid 代码
                if (language === 'mermaid') {
                    const mermaidId = 'mermaid-' + Math.random().toString(36).substring(2, 15);
                    
                    return `
                        <div class="mermaid-wrapper">
                            <div class="mermaid-actions">
                                <button class="mermaid-toggle-btn" onclick="toggleMermaidView('${mermaidId}')">
                                    <i class="fas fa-code"></i> 显示代码
                                </button>
                                <button class="code-copy-btn" onclick="copyToClipboard(this, '${escapeForJS(code)}')">
                                    复制代码
                                </button>
                            </div>
                            <div id="${mermaidId}-code" class="mermaid-code" style="display:none;">
                                <pre class="language-mermaid"><code>${escapeHTML(code)}</code></pre>
                            </div>
                            <div id="${mermaidId}-diagram" class="mermaid-diagram mermaid">
                                ${code}
                            </div>
                        </div>`;
                }
                
                // 处理其他代码类型，保留原始格式
                const classAttribute = language ? ` class="language-${language}"` : '';
                return `<pre><code${classAttribute}>${escapeHTML(code)}</code><button class="code-copy-btn" onclick="copyToClipboard(this, '${escapeForJS(code)}')">复制代码</button></pre>`;
            };
            
            // 自定义图片渲染
            renderer.image = function(href, title, text) {
                // 检查是否为视频链接
                if (href && (href.endsWith('.mp4') || href.endsWith('.webm') || href.endsWith('.ogg'))) {
                    return `<video controls src="${href}" alt="${text}" ${title ? `title="${title}"` : ''} style="max-width: 100%;"></video>`;
                }
                
                // 检查是否为 SVG 链接
                if (href && href.endsWith('.svg')) {
                    return `<img src="${href}" alt="${text}" ${title ? `title="${title}"` : ''} style="max-width: 100%;" class="svg-image">`;
                }
                
                // 普通图片
                return `<img src="${href}" alt="${text}" ${title ? `title="${title}"` : ''} style="max-width: 100%;">`;
            };
            
            // 配置 marked 选项
            marked.setOptions({
                renderer: renderer,
                highlight: function(code, lang) {
                    try {
                        // 处理代码高亮，但跳过 mermaid
                        if (lang === 'mermaid') return code;
                        
                        if (Prism && Prism.languages[lang]) {
                            return Prism.highlight(code, Prism.languages[lang], lang);
                        } else {
                            return code;
                        }
                    } catch (e) {
                        console.error("Prism 高亮错误:", e);
                        return code;
                    }
                },
                pedantic: false,
                gfm: true,
                breaks: true,
                sanitize: false,
                smartypants: false,    // 关闭智能标点转换
                xhtml: false,
                headerIds: false,      // 关闭标题 ID 自动生成
                mangle: false          // 关闭标题转换
            });
            
            // 辅助函数：转义 HTML
            function escapeHTML(html) {
                return html
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
            }
            
            // 辅助函数：为 JavaScript 字符串转义
            function escapeForJS(str) {
                return str
                    .replace(/\\/g, '\\\\')
                    .replace(/'/g, "\\'")
                    .replace(/"/g, '\\"')
                    .replace(/\n/g, '\\n')
                    .replace(/\r/g, '\\r');
            }
            // 将 toggleMermaidView 函数暴露到全局作用域
            window.toggleMermaidView = toggleMermaidView;
            window.copyToClipboard = copyToClipboard;
        }

        // 初始化上传的媒体预览支持
        function initMediaPreview() {
            // 处理图片上传和预览
            document.addEventListener('paste', function(e) {
                const items = (e.clipboardData || e.originalEvent.clipboardData).items;
                
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const blob = items[i].getAsFile();
                        handleImageFile(blob);
                        e.preventDefault();
                        break;
                    }
                }
            });

            // 监听文件拖放到预览区域
            const previewContainer = document.getElementById('preview-container');
            if (previewContainer) {
                previewContainer.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    previewContainer.classList.add('drag-over');
                });

                previewContainer.addEventListener('dragleave', function(e) {
                    previewContainer.classList.remove('drag-over');
                });

                previewContainer.addEventListener('drop', function(e) {
                    e.preventDefault();
                    previewContainer.classList.remove('drag-over');
                    
                    if (e.dataTransfer.files.length > 0) {
                        const file = e.dataTransfer.files[0];
                        if (file.type.startsWith('image/') || 
                            file.name.endsWith('.mp4') || 
                            file.name.endsWith('.webm') || 
                            file.name.endsWith('.ogg') ||
                            file.name.endsWith('.svg')) {
                            handleMediaFile(file);
                        }
                    }
                });
            }

            // 处理图片文件
            function handleImageFile(file) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const imageUrl = e.target.result;
                    const timestamp = new Date().getTime();
                    const imageMd = `![上传的图片_${timestamp}](${imageUrl})`;
                    
                    // 在光标位置插入图片
                    if (editor) {
                        const cursor = editor.getCursor();
                        editor.replaceRange(imageMd, cursor);
                        showNotification('图片已插入');
                    }
                };
                
                reader.readAsDataURL(file);
            }

            // 处理各种媒体文件
            function handleMediaFile(file) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const mediaUrl = e.target.result;
                    const timestamp = new Date().getTime();
                    let mediaMd = '';
                    
                    if (file.type.startsWith('image/')) {
                        mediaMd = `![上传的图片_${timestamp}](${mediaUrl})`;
                    } else if (file.name.endsWith('.mp4') || file.name.endsWith('.webm') || file.name.endsWith('.ogg')) {
                        mediaMd = `<video controls src="${mediaUrl}" style="max-width: 100%;" title="上传的视频_${timestamp}"></video>`;
                    } else if (file.name.endsWith('.svg')) {
                        mediaMd = `![上传的SVG_${timestamp}](${mediaUrl})`;
                    }
                    
                    // 在光标位置插入媒体
                    if (editor && mediaMd) {
                        const cursor = editor.getCursor();
                        editor.replaceRange(mediaMd, cursor);
                        showNotification(`${file.type.startsWith('image/') ? '图片' : '媒体'}已插入`);
                    }
                };
                
                reader.readAsDataURL(file);
            }

            // 给预览区域添加类，支持拖放样式
            document.getElementById('preview-container').classList.add('media-drop-zone');

            console.log('媒体预览支持初始化完成');
        }

        // 初始化收藏列表功能
        function initFavorites() {
            // 从本地存储加载收藏列表
            const savedFavorites = localStorage.getItem('favorites');
            if (savedFavorites) {
                favorites = JSON.parse(savedFavorites);
                updateFavoritesList();
            }

            // 添加收藏按钮点击事件
            const favoriteBtn = document.getElementById('favorite-btn');
            if (favoriteBtn) {
                favoriteBtn.addEventListener('click', addToFavorites);
            }
            
            // 收藏编辑对话框事件
            const cancelEditBtn = document.getElementById('cancel-edit-btn');
            const saveEditBtn = document.getElementById('save-edit-btn');
            
            if (cancelEditBtn) {
                cancelEditBtn.addEventListener('click', closeEditModal);
            }
            
            if (saveEditBtn) {
                saveEditBtn.addEventListener('click', saveFavoriteEdit);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            const viewModeBtn = document.getElementById('view-mode-btn');
            const viewDropdown = document.getElementById('view-dropdown');
            const exportBtn = document.getElementById('export-btn');
            const exportDropdown = document.getElementById('export-dropdown');
            const exportMarkdown = document.getElementById('export-markdown');
            const exportHtml = document.getElementById('export-html');
            const exportPdf = document.getElementById('export-pdf');
            const saveBtn = document.getElementById('save-btn');
            const loadBtn = document.getElementById('load-btn');
            const importBtn = document.getElementById('import-btn');
            const fileInputToolbar = document.getElementById('file-input-toolbar');
            const themeSwitch = document.getElementById('theme-switch');
            const dragArea = document.getElementById('drag-area');
            
            // 视图模式下拉菜单切换
            if (viewModeBtn) {
                viewModeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (viewDropdown) {
                        viewDropdown.classList.toggle('show');
                    }
                });
            }
            
            // 视图切换
            const editorOnlyBtn = document.getElementById('editor-only-btn');
            const editorCenteredBtn = document.getElementById('editor-centered-btn');
            const splitViewBtn = document.getElementById('split-view-btn');
            const previewOnlyBtn = document.getElementById('preview-only-btn');
            const previewCenteredBtn = document.getElementById('preview-centered-btn');
            
            if (editorOnlyBtn) {
                editorOnlyBtn.addEventListener('click', () => setView('editor'));
            }
            if (editorCenteredBtn) {
                editorCenteredBtn.addEventListener('click', () => setView('editor-centered'));
            }
            if (splitViewBtn) {
                splitViewBtn.addEventListener('click', () => setView('split'));
            }
            if (previewOnlyBtn) {
                previewOnlyBtn.addEventListener('click', () => setView('preview'));
            }
            if (previewCenteredBtn) {
                previewCenteredBtn.addEventListener('click', () => setView('preview-centered'));
            }

            // 导出下拉菜单切换
            if (exportBtn) {
                exportBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (exportDropdown) {
                        exportDropdown.classList.toggle('show');
                        if (viewDropdown) {
                            viewDropdown.classList.remove('show');
                        }
                    }
                });
            }
            
            // 点击外部关闭下拉菜单
            document.addEventListener('click', function() {
                if (exportDropdown) {
                    exportDropdown.classList.remove('show');
                }
                if (viewDropdown) {
                    viewDropdown.classList.remove('show');
                }
            });
            
            // 导出操作
            if (exportMarkdown) {
                exportMarkdown.addEventListener('click', exportMarkdownFile);
            }
            if (exportHtml) {
                exportHtml.addEventListener('click', exportHtmlFile);
            }
            if (exportPdf) {
                exportPdf.addEventListener('click', exportPdfFile);
            }
            
            // 本地存储操作
            if (saveBtn) {
                saveBtn.addEventListener('click', () => saveToLocal(false));
            }
            if (loadBtn) {
                loadBtn.addEventListener('click', loadFromLocal);
            }
            
            // 导入功能
            if (importBtn && fileInputToolbar) {
                importBtn.addEventListener('click', function() {
                    fileInputToolbar.click();
                });
            }
            
            if (fileInputToolbar) {
                fileInputToolbar.addEventListener('change', importMarkdownFile);
            }
            
            // 主题切换
            if (themeSwitch) {
                themeSwitch.addEventListener('click', toggleDarkMode);
            }
            
            if (editorOnlyBtn) {
                editorOnlyBtn.addEventListener('click', () => setView('editor'));
            }
            if (splitViewBtn) {
                splitViewBtn.addEventListener('click', () => setView('split'));
            }
            if (previewOnlyBtn) {
                previewOnlyBtn.addEventListener('click', () => setView('preview'));
            }
            
            // 工具栏操作
            const boldBtn = document.getElementById('bold-btn');
            const italicBtn = document.getElementById('italic-btn');
            const linkBtn = document.getElementById('link-btn');
            const codeBtn = document.getElementById('code-btn');
            const imageBtn = document.getElementById('image-btn');
            const mathBtn = document.getElementById('math-btn');
            const tableBtn = document.getElementById('table-btn');
            const listBtn = document.getElementById('list-btn');
            const orderedListBtn = document.getElementById('ordered-list-btn');
            const taskListBtn = document.getElementById('task-list-btn');
            const quoteBtn = document.getElementById('quote-btn');
            const hrBtn = document.getElementById('hr-btn');
            const footnoteBtn = document.getElementById('footnote-btn');
            const mermaidBtn = document.getElementById('mermaid-btn');
            
            if (boldBtn) boldBtn.addEventListener('click', () => insertMarkdown('**', '**'));
            if (italicBtn) italicBtn.addEventListener('click', () => insertMarkdown('*', '*'));
            if (linkBtn) linkBtn.addEventListener('click', insertLink);
            if (codeBtn) codeBtn.addEventListener('click', () => insertMarkdown('`', '`'));
            if (imageBtn) imageBtn.addEventListener('click', insertImage);
            if (mathBtn) mathBtn.addEventListener('click', insertMath);
            if (tableBtn) tableBtn.addEventListener('click', insertTable);
            if (listBtn) listBtn.addEventListener('click', insertUnorderedList);
            if (orderedListBtn) orderedListBtn.addEventListener('click', insertOrderedList);
            if (taskListBtn) taskListBtn.addEventListener('click', insertTaskList);
            if (quoteBtn) quoteBtn.addEventListener('click', insertQuote);
            if (hrBtn) hrBtn.addEventListener('click', insertHorizontalRule);
            if (footnoteBtn) footnoteBtn.addEventListener('click', insertFootnote);
            if (mermaidBtn) mermaidBtn.addEventListener('click', insertMermaidDiagram);
            
            // 文件拖放
            document.addEventListener('dragover', function(e) {
                e.preventDefault();
                if (dragArea) {
                    dragArea.classList.add('active');
                }
            });
            
            document.addEventListener('dragleave', function(e) {
                if (dragArea && !e.target.closest('#drag-area')) {
                    dragArea.classList.remove('active');
                }
            });
            
            document.addEventListener('drop', function(e) {
                e.preventDefault();
                if (dragArea) {
                    dragArea.classList.remove('active');
                }
                
                if (e.dataTransfer.files.length > 0) {
                    const file = e.dataTransfer.files[0];
                    if (file.name.endsWith('.md')) {
                        readAndLoadFile(file);
                    } else {
                        showNotification('请上传 Markdown (.md) 文件', true);
                    }
                }
            });
            
            // 支持粘贴图片
            if (editor) {
                editor.getWrapperElement().addEventListener('paste', handlePaste);
            }
            
            // 每分钟更新时间
            setInterval(updateCurrentTime, 60000);
        }

        // 渲染 Markdown 为 HTML 并替换元素
        function renderMarkdown() {
            const previewElement = document.getElementById('preview');
            if (!editor || !previewElement) {
                console.error('编辑器或预览元素不存在');
                return;
            }
            
            const content = editor.getValue();
            
            try {
                // 检查 katex 是否加载
                const katexLoaded = typeof katex !== 'undefined';
                
                if (!katexLoaded) {
                    console.warn('KaTeX 未加载，数学公式可能无法正确渲染');
                }
                
                // 首先将所有数学公式替换为唯一的占位符
                const mathExpressions = [];
                let processedContent = content
                    // 处理行间公式
                    .replace(/\\\[([\s\S]*?)\\\]|\$\$([\s\S]*?)\$\$/g, (match, formula1, formula2) => {
                        const formula = formula1 || formula2;
                        const placeholder = `%%MATH_BLOCK_${mathExpressions.length}%%`;
                        mathExpressions.push({
                            type: 'block',
                            formula: formula,
                            original: match // 保存原始文本
                        });
                        return placeholder;
                    })
                    // 处理行内公式
                    .replace(/\\\(([\s\S]*?)\\\)|\$([^\$\n]+?)\$/g, (match, formula1, formula2) => {
                        const formula = formula1 || formula2;
                        const placeholder = `%%MATH_INLINE_${mathExpressions.length}%%`;
                        mathExpressions.push({
                            type: 'inline',
                            formula: formula,
                            original: match // 保存原始文本
                        });
                        return placeholder;
                    });

                // 使用 marked 处理 Markdown
                let htmlContent = marked.parse(processedContent);

                // 还原数学公式
                mathExpressions.forEach((expr, index) => {
                    const placeholder = expr.type === 'block' 
                        ? `%%MATH_BLOCK_${index}%%` 
                        : `%%MATH_INLINE_${index}%%`;
                    try {
                        if (katexLoaded) {
                            const rendered = katex.renderToString(expr.formula.trim(), {
                                displayMode: expr.type === 'block',
                                throwOnError: false,
                                strict: false
                            });
                            
                            // 包装渲染后的公式，添加原始公式作为自定义属性
                            const wrappedElement = expr.type === 'block'
                                ? `<div class="katex-display" data-original-formula="${encodeURIComponent(expr.original)}">${rendered}</div>`
                                : `<span class="katex-inline" data-original-formula="${encodeURIComponent(expr.original)}">${rendered}</span>`;
                            
                            htmlContent = htmlContent.replace(placeholder, wrappedElement);
                        } else {
                            htmlContent = htmlContent.replace(
                                placeholder, 
                                `<span style="color:orange">数学公式: ${expr.formula}</span>`
                            );
                        }
                    } catch (err) {
                        console.error('渲染LaTeX错误:', err);
                        htmlContent = htmlContent.replace(
                            placeholder, 
                            `<span style="color:red">LaTeX错误: ${err.message}</span>`
                        );
                    }
                });

                // 更新预览
                previewElement.innerHTML = htmlContent;
                
                // 确保代码高亮
                try {
                    if (typeof Prism !== 'undefined') {
                        document.querySelectorAll('.markdown-body pre code').forEach(block => {
                            Prism.highlightElement(block);
                        });
                    }
                } catch (e) {
                    console.error('代码高亮错误:', e);
                }
                
                // 初始化复制按钮
                initCodeCopyButtons();
                
                // 渲染 Mermaid 图表
                if (typeof mermaid !== 'undefined') {
                    try {
                        mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                    } catch (e) {
                        console.warn('Mermaid 渲染错误:', e);
                    }
                }
            } catch (error) {
                console.error('Markdown 渲染错误:', error);
                previewElement.innerHTML = `<div style="color: red;">渲染错误: ${error.message}</div>`;
            }
        }


        // 更新收藏列表显示
        function updateFavoritesList() {
            const favoritesList = document.getElementById('favorites-list');
            if (!favoritesList) return;

            // 清空现有列表
            favoritesList.innerHTML = '';

            if (favorites.length === 0) {
                favoritesList.innerHTML = '<div class="empty-favorites">还没有收藏项目</div>';
                return;
            }

            // 添加收藏项目
            favorites.forEach((item, index) => {
                const favoriteItem = document.createElement('div');
                favoriteItem.className = 'favorite-item';
                
                // 创建头部（标题）
                const itemHeader = document.createElement('div');
                itemHeader.className = 'favorite-item-header';
                
                // 标题
                const itemTitle = document.createElement('div');
                itemTitle.className = 'favorite-item-title';
                itemTitle.textContent = item.title || `收藏项目 ${index + 1}`;
                
                // 将标题添加到头部
                itemHeader.appendChild(itemTitle);
                
                // 创建底部（时间和操作按钮）
                const itemFooter = document.createElement('div');
                itemFooter.className = 'favorite-item-footer';
                
                // 时间戳显示
                const itemTime = document.createElement('div');
                itemTime.className = 'favorite-item-time';
                let dateDisplay = '未知时间';
                
                if (item.date) {
                    try {
                        const date = new Date(item.date);
                        dateDisplay = date.toLocaleString('zh-CN', {
                            month: 'numeric',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    } catch (e) {
                        console.error('日期解析错误:', e);
                    }
                }
                
                itemTime.textContent = dateDisplay;
                
                // 操作按钮
                const itemActions = document.createElement('div');
                itemActions.className = 'favorite-item-actions';
                
                // 加载按钮
                const loadBtn = document.createElement('button');
                loadBtn.className = 'favorite-action-btn';
                loadBtn.innerHTML = '<i class="fas fa-file-import"></i>';
                loadBtn.title = '加载';
                loadBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    loadFavorite(index);
                });
                
                // 编辑按钮
                const editBtn = document.createElement('button');
                editBtn.className = 'favorite-action-btn';
                editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                editBtn.title = '编辑';
                editBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    openEditModal(index);
                });
                
                // 删除按钮
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'favorite-action-btn';
                deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteBtn.title = '删除';
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    deleteFavorite(index);
                });
                
                // 将按钮添加到操作容器
                itemActions.appendChild(loadBtn);
                itemActions.appendChild(editBtn);
                itemActions.appendChild(deleteBtn);
                
                // 将时间和操作按钮添加到底部
                itemFooter.appendChild(itemTime);
                itemFooter.appendChild(itemActions);
                
                // 将头部和底部添加到项目
                favoriteItem.appendChild(itemHeader);
                favoriteItem.appendChild(itemFooter);
                
                // 点击项目本身也可以加载
                favoriteItem.addEventListener('click', () => {
                    loadFavorite(index);
                });
                
                favoritesList.appendChild(favoriteItem);
            });
        }

        // 添加到收藏
        function addToFavorites() {
            if (!editor) return;
            
            const content = editor.getValue();
            if (!content.trim()) {
                showNotification('无法收藏空内容', true);
                return;
            }
            
            // 尝试从内容中提取标题
            let title = '未命名文档';
            const firstLine = content.split('\n')[0];
            if (firstLine && firstLine.startsWith('# ')) {
                title = firstLine.substring(2).trim();
            }
            
            // 创建收藏对象
            const favorite = {
                title: title,
                content: content,
                date: new Date().toISOString()
            };
            
            // 添加到收藏列表
            favorites.push(favorite);
            
            // 保存到本地存储
            localStorage.setItem('favorites', JSON.stringify(favorites));
            
            // 更新列表显示
            updateFavoritesList();
            
            showNotification(`已收藏: ${title}`);
        }

        // 加载收藏项目
        function loadFavorite(index) {
            if (!editor || index < 0 || index >= favorites.length) return;
            
            const favorite = favorites[index];
            editor.setValue(favorite.content);
            
            showNotification(`已加载: ${favorite.title}`);
        }

        // 删除收藏项目
        function deleteFavorite(index) {
            if (index < 0 || index >= favorites.length) return;
            
            const title = favorites[index].title;
            
            // 从数组中移除
            favorites.splice(index, 1);
            
            // 更新本地存储
            localStorage.setItem('favorites', JSON.stringify(favorites));
            
            // 更新列表显示
            updateFavoritesList();
            
            showNotification(`已删除: ${title}`);
        }

        // 打开编辑收藏对话框
        function openEditModal(index) {
            const favoriteEditModal = document.getElementById('favorite-edit-modal');
            const favoriteTitle = document.getElementById('favorite-title');
            
            if (index < 0 || index >= favorites.length || !favoriteEditModal || !favoriteTitle) return;
            
            currentEditingFavorite = index;
            favoriteTitle.value = favorites[index].title || '';
            
            favoriteEditModal.classList.add('active');
        }

        // 关闭编辑收藏对话框
        function closeEditModal() {
            const favoriteEditModal = document.getElementById('favorite-edit-modal');
            if (!favoriteEditModal) return;
            
            favoriteEditModal.classList.remove('active');
            currentEditingFavorite = -1;
        }

        // 保存收藏编辑
        function saveFavoriteEdit() {
            const favoriteTitle = document.getElementById('favorite-title');
            if (currentEditingFavorite < 0 || !favoriteTitle) return;
            
            // 更新标题
            favorites[currentEditingFavorite].title = favoriteTitle.value.trim() || '未命名文档';
            
            // 保存到本地存储
            localStorage.setItem('favorites', JSON.stringify(favorites));
            
            // 更新列表显示
            updateFavoritesList();
            
            // 关闭对话框
            closeEditModal();
            
            showNotification('收藏标题已更新');
        }

        // 排序收藏项目
        function sortFavorites(sortType) {
            if (!favorites.length) return;
            
            switch (sortType) {
                case 'newest':
                    favorites.sort((a, b) => new Date(b.date) - new Date(a.date));
                    break;
                case 'oldest':
                    favorites.sort((a, b) => new Date(a.date) - new Date(b.date));
                    break;
                case 'alphabetical':
                    favorites.sort((a, b) => (a.title || '').localeCompare(b.title || ''));
                    break;
            }
            
            // 保存排序后的收藏列表
            localStorage.setItem('favorites', JSON.stringify(favorites));
            
            // 更新显示
            updateFavoritesList();
        }

        // 处理粘贴事件
        function handlePaste(e) {
            const items = (e.clipboardData || e.originalEvent.clipboardData).items;
            
            for (let i = 0; i < items.length; i++) {
                if (items[i].type.indexOf('image') !== -1) {
                    // 阻止默认粘贴行为
                    e.preventDefault();
                    
                    // 获取粘贴的图片文件
                    const blob = items[i].getAsFile();
                    
                    // 创建一个 FileReader 用于读取图片内容
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        // 图片的 Base64 编码
                        const imageUrl = event.target.result;
                        
                        // 生成 Markdown 图片语法，并插入到编辑器中
                        const timestamp = new Date().getTime();
                        const imageMd = `![粘贴的图片_${timestamp}](${imageUrl})`;
                        
                        // 获取编辑器当前光标位置
                        const cursor = editor.getCursor();
                        
                        // 在光标位置插入图片
                        editor.replaceRange(imageMd, cursor);
                    };
                    
                    // 读取图片文件内容
                    reader.readAsDataURL(blob);
                    
                    // 只处理第一个图片，然后退出循环
                    break;
                }
            }
        }

        // 更新自动保存状态显示
        function updateAutoSaveStatus(isActive, timestamp = null) {
            const autosaveIndicator = document.getElementById('autosave-indicator');
            const autosaveStatus = document.getElementById('autosave-status');
            
            if (!autosaveIndicator || !autosaveStatus) return;
            
            if (isActive) {
                autosaveStatus.innerHTML = timestamp 
                    ? `上次保存: ${new Date(timestamp).toLocaleTimeString()}`
                    : '自动保存: 已启用';
                autosaveIndicator.style.color = 'var(--success)';
                autosaveIndicator.title = '点击查看自动保存历史';
                autosaveIndicator.style.cursor = 'pointer';
                
                // 添加短暂的动画效果
                autosaveIndicator.classList.add('saving');
                setTimeout(() => {
                    autosaveIndicator.classList.remove('saving');
                }, 1000);
            } else {
                autosaveStatus.textContent = '自动保存: 已关闭';
                autosaveIndicator.style.color = '';
                autosaveIndicator.title = '自动保存已关闭';
            }
        }

        // 设置自动保存
        function startAutoSave() {
            // 如果已存在自动保存，先清除
            stopAutoSave();
            
            // 更新状态显示
            updateAutoSaveStatus(true);
            
            // 设置自动保存间隔（毫秒）
            autoSaveInterval = setInterval(() => {
                const content = editor.getValue();
                if (content.trim()) { // 只保存非空内容
                    // 保存当前内容到历史记录
                    addToAutoSaveHistory(content);
                    
                    // 保存到 localStorage
                    localStorage.setItem('markdownContent', content);
                    
                    lastAutoSaveTime = new Date();
                    updateAutoSaveStatus(true, lastAutoSaveTime);
                    
                    // 更新侧边栏历史记录
                    updateSidebarHistoryList();
                }
            }, AUTO_SAVE_INTERVAL_SECONDS * 1000);
        }

        // 添加内容到自动保存历史记录
        function addToAutoSaveHistory(content) {
            // 创建新的历史记录项
            const historyItem = {
                content: content,
                date: new Date().toISOString()
            };
            
            // 添加到历史记录
            autoSaveHistory.push(historyItem);
            
            // 只保留最新的 MAX_HISTORY_ITEMS 条记录
            if (autoSaveHistory.length > MAX_HISTORY_ITEMS) {
                autoSaveHistory = autoSaveHistory.slice(-MAX_HISTORY_ITEMS);
            }
            
            // 保存到本地存储
            localStorage.setItem('autoSaveHistory', JSON.stringify(autoSaveHistory));
        }

        // 停止自动保存
        function stopAutoSave() {
            if (autoSaveInterval) {
                clearInterval(autoSaveInterval);
                autoSaveInterval = null;
                updateAutoSaveStatus(false);
            }
        }

        // 更新字数和字符统计
        function updateWordCount() {
            const wordCountElement = document.getElementById('word-count');
            const charCountElement = document.getElementById('character-count');
            
            if (!editor || !wordCountElement || !charCountElement) return;
            
            const content = editor.getValue();
            
            // 改进的字数计数 - 处理中英文混合文本
            let wordCount = 0;
            
            // 中文字符计数（每个中文字符算一个字）
            const chineseChars = content.match(/[\u4e00-\u9fa5]/g) || [];
            wordCount += chineseChars.length;
            
            // 英文单词计数
            const englishContent = content.replace(/[\u4e00-\u9fa5]/g, ' '); // 去掉中文
            const englishWords = englishContent.match(/\b\w+\b/g) || [];
            wordCount += englishWords.length;
            
            // 字符数（包含所有字符，包括空格和标点）
            const characters = content.length;
            
            wordCountElement.textContent = `${wordCount} 字`;
            charCountElement.textContent = `${characters} 字符`;
        }

        // 初始化自动保存历史记录
        function initAutoSaveHistory() {
            // 从本地存储加载历史记录
            const savedHistory = localStorage.getItem('autoSaveHistory');
            if (savedHistory) {
                try {
                    autoSaveHistory = JSON.parse(savedHistory);
                } catch (e) {
                    console.error('解析自动保存历史记录失败:', e);
                    autoSaveHistory = [];
                }
            }
            
            // 设置自动保存历史对话框事件
            const closeHistoryBtn = document.getElementById('close-history-btn');
            if (closeHistoryBtn) {
                closeHistoryBtn.addEventListener('click', closeHistoryModal);
            }
            
            // 修改自动保存状态点击事件 - 点击显示历史记录
            const autosaveIndicator = document.getElementById('autosave-indicator');
            if (autosaveIndicator) {
                autosaveIndicator.addEventListener('click', openHistoryModal);
            }
            
            // 初始化侧边栏历史记录
            updateSidebarHistoryList();
        }

        // 更新侧边栏中的历史记录列表
        function updateSidebarHistoryList() {
            const historyList = document.getElementById('sidebar-history-list');
            if (!historyList) return;
            
            // 清空现有列表
            historyList.innerHTML = '';
            
            if (autoSaveHistory.length === 0) {
                historyList.innerHTML = '<div class="empty-history">没有历史记录</div>';
                return;
            }
            
            // 从最新的记录开始显示
            autoSaveHistory.slice().reverse().forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'sidebar-history-item';
                
                // 提取内容的前30个字符作为预览
                let contentPreview = item.content.substring(0, 30).trim();
                if (item.content.length > 30) contentPreview += '...';
                
                // 创建信息容器
                const itemInfo = document.createElement('div');
                itemInfo.className = 'history-item-info';
                
                // 创建日期显示
                const itemDate = document.createElement('div');
                itemDate.className = 'history-item-date';
                
                let dateDisplay = '未知时间';
                if (item.date) {
                    try {
                        const date = new Date(item.date);
                        dateDisplay = date.toLocaleTimeString('zh-CN');
                    } catch (e) {
                        console.error('日期解析错误:', e);
                    }
                }
                
                itemDate.textContent = dateDisplay;
                
                // 内容预览
                const itemPreview = document.createElement('div');
                itemPreview.className = 'history-item-preview';
                itemPreview.textContent = contentPreview;
                
                // 组合信息
                itemInfo.appendChild(itemDate);
                itemInfo.appendChild(itemPreview);
                
                // 恢复按钮
                const restoreBtn = document.createElement('button');
                restoreBtn.className = 'btn btn-icon btn-sm';
                restoreBtn.innerHTML = '<i class="fas fa-undo"></i>';
                restoreBtn.title = '恢复此版本';
                restoreBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    restoreFromHistory(autoSaveHistory.length - 1 - index);
                });
                
                // 组装项目
                historyItem.appendChild(itemInfo);
                historyItem.appendChild(restoreBtn);
                
                // 点击整个项目可以恢复内容
                historyItem.addEventListener('click', () => {
                    restoreFromHistory(autoSaveHistory.length - 1 - index);
                });
                
                historyList.appendChild(historyItem);
            });
        }

        // 清空全部历史记录
        function clearAllHistory() {
            if (autoSaveHistory.length === 0) {
                showNotification('没有历史记录可清除');
                return;
            }
            
            if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
                autoSaveHistory = [];
                localStorage.setItem('autoSaveHistory', JSON.stringify(autoSaveHistory));
                updateSidebarHistoryList();
                updateHistoryList(); // 更新弹窗中的历史记录列表
                showNotification('历史记录已清空');
            }
        }

        // 更新自动保存历史列表（弹窗）
        function updateHistoryList() {
            const historyList = document.getElementById('autosave-history-list');
            if (!historyList) return;
            
            // 清空现有列表
            historyList.innerHTML = '';
            
            if (autoSaveHistory.length === 0) {
                historyList.innerHTML = '<div style="padding: 1rem; text-align: center; color: #718096;">没有历史记录</div>';
                return;
            }
            
            // 从最新的记录开始显示
            autoSaveHistory.slice().reverse().forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'autosave-history-item';
                
                // 提取内容的前30个字符作为预览
                let contentPreview = item.content.substring(0, 30).trim();
                if (item.content.length > 30) contentPreview += '...';
                
                // 创建日期显示
                const itemDate = document.createElement('div');
                itemDate.className = 'autosave-history-date';
                
                let dateDisplay = '未知时间';
                if (item.date) {
                    try {
                        const date = new Date(item.date);
                        dateDisplay = date.toLocaleString('zh-CN');
                    } catch (e) {
                        console.error('日期解析错误:', e);
                    }
                }
                
                itemDate.textContent = dateDisplay;
                
                // 内容预览
                const itemPreview = document.createElement('div');
                itemPreview.className = 'autosave-history-preview';
                itemPreview.textContent = contentPreview;
                
                // 操作按钮
                const itemActions = document.createElement('div');
                itemActions.className = 'autosave-history-actions';
                
                const restoreBtn = document.createElement('button');
                restoreBtn.className = 'btn btn-primary btn-sm';
                restoreBtn.innerHTML = '<i class="fas fa-undo"></i> 恢复';
                restoreBtn.title = '恢复此版本';
                restoreBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    restoreFromHistory(autoSaveHistory.length - 1 - index);
                });
                
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'btn btn-danger btn-sm';
                deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i> 删除';
                deleteBtn.title = '删除此记录';
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    deleteHistoryItem(autoSaveHistory.length - 1 - index);
                    
                    // 更新侧边栏历史记录
                    updateSidebarHistoryList();
                });
                
                itemActions.appendChild(restoreBtn);
                itemActions.appendChild(deleteBtn);
                
                // 组合项目
                const infoContainer = document.createElement('div');
                infoContainer.appendChild(itemDate);
                infoContainer.appendChild(itemPreview);
                
                historyItem.appendChild(infoContainer);
                historyItem.appendChild(itemActions);
                
                // 点击整个项目可以恢复内容
                historyItem.addEventListener('click', () => {
                    restoreFromHistory(autoSaveHistory.length - 1 - index);
                });
                
                historyList.appendChild(historyItem);
            });
        }

        // 格式化时间间隔显示
        function formatTimeInterval(seconds) {
            if (seconds < 60) {
                return `${seconds}秒`;
            } else if (seconds == 60) {
                return "1分钟";
            } else if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return remainingSeconds > 0 
                    ? `${minutes}分${remainingSeconds}秒`
                    : `${minutes}分钟`;
            } else {
                const hours = Math.floor(seconds / 3600);
                const remainingMinutes = Math.floor((seconds % 3600) / 60);
                return remainingMinutes > 0 
                    ? `${hours}小时${remainingMinutes}分钟`
                    : `${hours}小时`;
            }
        }

        // 打开自动保存历史对话框
        function openHistoryModal() {
            const historyModal = document.getElementById('autosave-history-modal');
            if (!historyModal || autoSaveHistory.length === 0) {
                showNotification('没有可用的自动保存历史记录');
                return;
            }
            updateHistoryList();
            updateSidebarHistoryList();
            historyModal.classList.add('active');
        }

        // 关闭自动保存历史对话框
        function closeHistoryModal() {
            const historyModal = document.getElementById('autosave-history-modal');
            if (historyModal) {
                historyModal.classList.remove('active');
            }
        }

        // 从历史记录恢复内容
        function restoreFromHistory(index) {
            if (index < 0 || index >= autoSaveHistory.length || !editor) return;
            
            const item = autoSaveHistory[index];
            editor.setValue(item.content);
            
            showNotification(`已恢复 ${new Date(item.date).toLocaleString('zh-CN')} 的版本`);
            closeHistoryModal();
        }

        // 删除历史记录项
        function deleteHistoryItem(index) {
            if (index < 0 || index >= autoSaveHistory.length) return;
            
            autoSaveHistory.splice(index, 1);
            localStorage.setItem('autoSaveHistory', JSON.stringify(autoSaveHistory));
            updateHistoryList();
            updateSidebarHistoryList();
            showNotification('已删除历史记录');
        }

        // 更新当前时间
        function updateCurrentTime() {
            const currentTimeElement = document.getElementById('current-time');
            if (!currentTimeElement) return;
            
            const now = new Date();
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit', 
                minute: '2-digit'
            };
            currentTimeElement.textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 显示加载指示器
        function showLoading() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('visible');
            }
        }

        // 隐藏加载指示器
        function hideLoading() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.remove('visible');
            }
        }

        // 切换暗黑模式
        function toggleDarkMode() {
            darkMode = !darkMode;
            document.body.setAttribute('data-theme', darkMode ? 'dark' : 'light');
            
            // 更新主题切换图标
            const themeSwitch = document.getElementById('theme-switch');
            if (themeSwitch) {
                themeSwitch.innerHTML = darkMode ? 
                    '<i class="fas fa-sun"></i>' : 
                    '<i class="fas fa-moon"></i>';
            }
            
            // 更新暗色模式开关状态
            const darkModeToggle = document.getElementById('dark-mode-toggle');
            if (darkModeToggle) {
                darkModeToggle.checked = darkMode;
            }
            
            // 更新 Mermaid 主题
            if (typeof mermaid !== 'undefined') {
                mermaid.initialize({
                    theme: darkMode ? 'dark' : 'default'
                });
                
                // 重新渲染现有图表
                try {
                    mermaid.init(undefined, document.querySelectorAll('.mermaid'));
                } catch (e) {
                    console.warn('Mermaid 重新渲染错误:', e);
                }
            }
        }

        // 设置视图模式（仅编辑器，仅预览，或分屏）
        function setView(view) {
            const editorContainer = document.getElementById('editor-container');
            const previewContainer = document.getElementById('preview-container');
            const viewModeBtn = document.getElementById('view-mode-btn');
            
            if (!editorContainer || !previewContainer) return;
            
            currentView = view;
            
            // 首先移除所有视图相关的类
            editorContainer.classList.remove('hidden', 'fullwidth', 'centered');
            previewContainer.classList.remove('hidden', 'fullwidth', 'centered');
            
            switch (view) {
                case 'editor': // Alt+1
                    editorContainer.classList.remove('hidden');
                    previewContainer.classList.add('hidden');
                    editorContainer.classList.add('fullwidth');
                    break;
                case 'editor-centered': // Alt+2
                    editorContainer.classList.remove('hidden');
                    previewContainer.classList.add('hidden');
                    editorContainer.classList.add('centered');
                    break;
                case 'split': // Alt+3
                    editorContainer.classList.remove('hidden');
                    previewContainer.classList.remove('hidden');
                    break;
                case 'preview': // Alt+4
                    editorContainer.classList.add('hidden');
                    previewContainer.classList.remove('hidden');
                    previewContainer.classList.add('fullwidth');
                    renderMarkdown();
                    break;
                case 'preview-centered': // Alt+5
                    editorContainer.classList.add('hidden');
                    previewContainer.classList.remove('hidden');
                    previewContainer.classList.add('centered');
                    renderMarkdown();
                    break;
            }
            
            // 更新视图按钮图标
            if (viewModeBtn) {
                let viewIcon = 'fa-columns'; // 默认分屏图标
                let viewText = '视图';
                
                if (view === 'editor' || view === 'editor-centered') {
                    viewIcon = 'fa-edit';
                } else if (view === 'preview' || view === 'preview-centered') {
                    viewIcon = 'fa-eye';
                }
                
                if (view.includes('centered')) {
                    viewText += ' (居中)';
                }
                
                viewModeBtn.innerHTML = `<i class="fas ${viewIcon}"></i> ${viewText}`;
            }
            
            // 刷新 CodeMirror 以修复渲染问题
            setTimeout(() => {
                if (editor) {
                    editor.refresh();
                }
            }, 10);
        }

        // 保存到本地存储
        function saveToLocal(silent = false) {
            if (!editor) return;
            
            const content = editor.getValue();
            localStorage.setItem('markdownContent', content);
            
            if (!silent) {
                showNotification('内容已保存到本地存储');
            } else {
                // 更新自动保存状态
                lastAutoSaveTime = new Date();
                updateAutoSaveStatus(true, lastAutoSaveTime);
            }
        }

        // 从本地存储加载
        function loadFromLocal() {
            if (!editor) return;
            
            const content = localStorage.getItem('markdownContent');
            if (content) {
                editor.setValue(content);
                showNotification('内容已从本地存储加载');
            } else {
                showNotification('本地存储中没有找到内容', true);
            }
        }

        // 导出 Markdown 文件
        function exportMarkdownFile() {
            if (!editor) return;
            
            const content = editor.getValue();
            const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
            saveAs(blob, `markdown_${getFormattedDate()}.md`);
            showNotification('Markdown 文件已导出');
        }

        // 导出 HTML 文件
        function exportHtmlFile() {
            const previewElement = document.getElementById('preview');
            if (!editor || !previewElement) return;
            
            // 确保预览内容是最新的
            renderMarkdown();
            
            // 创建一个预览内容的深拷贝，这样我们可以修改它而不影响原始DOM
            const contentClone = previewElement.cloneNode(true);
            
            // 移除所有代码块和数学公式中的复制按钮
            contentClone.querySelectorAll('.code-copy-btn, .math-copy-btn').forEach(btn => btn.remove());
            
            const content = `
        <!DOCTYPE html>
        <html lang="zh">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Markdown 导出文档</title>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/katex/0.16.8/katex.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
            <style>
                body {
                    box-sizing: border-box;
                    min-width: 200px;
                    max-width: 980px;
                    margin: 0 auto;
                    padding: 45px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                }
                
                @media (max-width: 767px) {
                    body {
                        padding: 15px;
                    }
                }
                
                .katex-display {
                    overflow-x: auto;
                    overflow-y: hidden;
                    padding: 0.5em 0;
                    position: relative;  /* 确保公式块正确显示 */
                }
                
                pre {
                    position: relative;
                    overflow-x: auto;
                }
                
                pre code {
                    display: block;
                    overflow-x: auto;
                    padding: 1em;
                    background: #f6f8fa;
                    border-radius: 5px;
                }
                
                img, video {
                    max-width: 100%;
                    height: auto;
                }
            </style>
        </head>
        <body class="markdown-body">
            ${contentClone.innerHTML}
        </body>
        </html>`;
            
            const blob = new Blob([content], { type: 'text/html;charset=utf-8' });
            saveAs(blob, `html_${getFormattedDate()}.html`);
            
            showNotification('HTML文件已导出');
        }

        // 导出 PDF 文件
        function exportPdfFile() {
            showNotification('PDF 导出功能需要额外的库支持，请使用 HTML 导出后再转换为 PDF', true);
            // 先导出 HTML
            exportHtmlFile();
            // 显示转换提示
            showNotification('HTML 已导出。您可以使用浏览器的打印功能将其保存为 PDF（Ctrl+P，选择"另存为 PDF"）', false);
        }

        // 导入 Markdown 文件
        function importMarkdownFile(e) {
            const file = e.target.files[0];
            if (file) {
                readAndLoadFile(file);
                e.target.value = ''; // 重置以允许重新导入相同文件
            }
        }

        // 读取并加载文件内容
        function readAndLoadFile(file) {
            if (!editor) return;
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                editor.setValue(e.target.result);
                showNotification(`文件 "${file.name}" 已导入`);
            };
            
            reader.onerror = function() {
                showNotification('文件读取失败', true);
            };
            
            reader.readAsText(file);
        }

        // 显示通知
        function showNotification(message, isError = false) {
            // 移除已存在的通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                document.body.removeChild(existingNotification);
            }
            
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'notification' + (isError ? ' error' : '');
            notification.textContent = message;
            
            // 添加到 body
            document.body.appendChild(notification);
            
            // 触发动画
            setTimeout(() => {
                notification.classList.add('visible');
            }, 10);
            
            // 自动移除
            setTimeout(() => {
                notification.classList.remove('visible');
                setTimeout(() => {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 插入 Markdown 语法
        function insertMarkdown(before, after, defaultText = '') {
            if (!editor) return;
            
            const selection = editor.getSelection();
            const text = selection || defaultText;
            editor.replaceSelection(before + text + after);
            
            if (!selection) {
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch - after.length });
            }
            
            editor.focus();
        }

        // 插入链接
        function insertLink() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            const text = selection || '链接文本';
            const url = 'https://example.com';
            
            editor.replaceSelection(`[${text}](${url})`);
            
            if (!selection) {
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch - 1 });
            }
            
            editor.focus();
        }

        // 插入图片
        function insertImage() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            const alt = selection || '图片描述';
            const url = 'your_image_url';
            
            editor.replaceSelection(`![${alt}](${url})`);
            
            if (!selection) {
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch - 1 });
            }
            
            editor.focus();
        }

        // 插入数学公式
        function insertMath() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            const text = selection || 'E = mc^2';
            
            if (selection && selection.includes('\n')) {
                editor.replaceSelection(`$$\n${text}\n$$`);
            } else {
                editor.replaceSelection(`$${text}$`);
                
                if (!selection) {
                    const cursor = editor.getCursor();
                    editor.setCursor({ line: cursor.line, ch: cursor.ch - 1 });
                }
            }
            
            editor.focus();
        }

        // 插入表格
        function insertTable() {
            if (!editor) return;
            
            const table = `
| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 单元格 | 单元格 | 单元格 |
| 单元格 | 单元格 | 单元格 |`;
            
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);
            
            // 如果当前行不为空，则在表格前添加空行
            if (line.trim() !== '') {
                editor.replaceSelection('\n\n' + table);
            } else {
                editor.replaceSelection(table);
            }
            
            editor.focus();
        }

        // 插入无序列表
        function insertUnorderedList() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            
            if (selection) {
                const lines = selection.split('\n');
                const list = lines.map(line => `- ${line}`).join('\n');
                editor.replaceSelection(list);
            } else {
                editor.replaceSelection(
`
- 列表项1
- 列表项2
- 列表项3
`);
                
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch });
            }
            
            editor.focus();
        }

        // 插入有序列表
        function insertOrderedList() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            
            if (selection) {
                const lines = selection.split('\n');
                const list = lines.map((line, i) => `${i + 1}. ${line}`).join('\n');
                editor.replaceSelection(list);
            } else {
                editor.replaceSelection(
`
1. 列表项
2. 列表项
3. 列表项
`);
                
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch });
            }
            
            editor.focus();
        }

        // 插入任务列表
        function insertTaskList() {
            if (!editor) return;
            
            const taskList = `
- [ ] 任务1
- [ ] 任务2
- [x] 已完成任务`;
            
            editor.replaceSelection(taskList);
            editor.focus();
        }

        // 插入引用
        function insertQuote() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            
            if (selection) {
                const lines = selection.split('\n');
                const quote = lines.map(line => `> ${line}`).join('\n');
                editor.replaceSelection(quote);
            } else {
                editor.replaceSelection('> 引用文本');
                
                const cursor = editor.getCursor();
                editor.setCursor({ line: cursor.line, ch: cursor.ch });
            }
            
            editor.focus();
        }

        // 插入水平线
        function insertHorizontalRule() {
            if (!editor) return;
            
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);
            
            // 如果当前行不为空，则在水平线前后添加空行
            if (line.trim() !== '') {
                editor.replaceSelection('\n\n---\n\n');
            } else {
                editor.replaceSelection('\n---\n\n');
            }
            
            editor.focus();
        }

        // 插入脚注
        function insertFootnote() {
            if (!editor) return;
            
            const selection = editor.getSelection();
            const text = selection || '脚注';
            const footnoteId = 'footnote' + Math.floor(Math.random() * 1000);
            
            editor.replaceSelection(`${text}[^${footnoteId}]`);
            
            // 移动到文档末尾并添加脚注定义
            const lastLine = editor.lineCount() - 1;
            const lastLineContent = editor.getLine(lastLine);
            
            if (lastLineContent.trim() !== '') {
                editor.replaceRange(`\n\n[^${footnoteId}]: 这里是脚注内容\n`, { line: lastLine + 1, ch: 0 });
            } else {
                editor.replaceRange(`\n[^${footnoteId}]: 这里是脚注内容\n`, { line: lastLine, ch: 0 });
            }
            
            editor.focus();
            
            // 回到引用点
            const cursor = editor.getCursor();
            editor.setCursor({ line: cursor.line - 3, ch: cursor.ch });
        }

        // 插入 Mermaid 图表
        function insertMermaidDiagram() {
            if (!editor) return;
            
            const mermaidTemplate = `\`\`\`mermaid
sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: 你好，Bob!
    B-->>A: 你好，Alice!
    Note right of B: Bob 响应消息
    loop 每天
        A->>B: 今天过得怎么样？
        B-->>A: 还不错！
    end
\`\`\``;
            
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);
            
            // 如果当前行不为空，则在图表前添加空行
            if (line.trim() !== '') {
                editor.replaceSelection('\n\n' + mermaidTemplate + '\n');
            } else {
                editor.replaceSelection(mermaidTemplate + '\n');
            }
            
            editor.focus();
        }

        // 获取格式化日期，用于文件命名
        function getFormattedDate() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // FileSaver 不存在时的简单替代方案
        function saveAs(blob, fileName) {
            if (window.FileSaver && window.FileSaver.saveAs) {
                // 使用 FileSaver 库的方法
                window.FileSaver.saveAs(blob, fileName);
            } else {
                // 自定义实现
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 0);
            }
        }

        // 初始化代码块和公式块复制按钮
        function initCodeCopyButtons() {
            // 先移除所有已存在的复制按钮
            document.querySelectorAll('.code-copy-btn').forEach(btn => btn.remove());
            
            // 获取所有代码块
            const codeBlocks = document.querySelectorAll('.markdown-body pre');
            
            // 为每个代码块添加复制按钮
            codeBlocks.forEach(pre => {
                // 创建复制按钮
                const button = document.createElement('button');
                button.className = 'code-copy-btn';
                button.textContent = '复制';
                button.title = '复制代码';
                
                // 添加点击事件
                button.addEventListener('click', async () => {
                    try {
                        const code = pre.querySelector('code').textContent;
                        await navigator.clipboard.writeText(code);
                        
                        // 复制成功的视觉反馈
                        button.textContent = '已复制!';
                        button.classList.add('copied');
                        
                        setTimeout(() => {
                            button.textContent = '复制';
                            button.classList.remove('copied');
                        }, 2000);
                    } catch (err) {
                        console.error('复制失败:', err);
                        button.textContent = '复制失败';
                        
                        setTimeout(() => {
                            button.textContent = '复制';
                        }, 2000);
                    }
                });
                
                // 将按钮添加到代码块中
                pre.appendChild(button);
            });
            
            // 为公式块添加复制按钮
            const mathBlocks = document.querySelectorAll('.katex-display');
            mathBlocks.forEach(block => {
                // 首先检查是否有原始公式数据
                const originalFormula = block.getAttribute('data-original-formula');
                
                // 只有当存在原始公式数据时才添加复制按钮
                if (originalFormula) {
                    // 确保公式块有相对定位，用于正确放置按钮
                    block.style.position = 'relative';
                    
                    // 创建复制按钮
                    const button = document.createElement('button');
                    button.className = 'code-copy-btn math-copy-btn';  // 添加特定的math-copy-btn类名
                    button.textContent = '复制';
                    button.title = '复制原始公式';
                    
                    // 添加点击事件
                    button.addEventListener('click', async (e) => {
                        // 阻止事件冒泡，防止触发其他事件
                        e.stopPropagation();
                        
                        try {
                            // 获取并解码原始公式
                            const decodedFormula = decodeURIComponent(originalFormula);
                            await navigator.clipboard.writeText(decodedFormula);
                            
                            // 复制成功的视觉反馈
                            button.textContent = '已复制!';
                            button.classList.add('copied');
                            
                            setTimeout(() => {
                                button.textContent = '复制';
                                button.classList.remove('copied');
                            }, 2000);
                        } catch (err) {
                            console.error('复制失败:', err);
                            button.textContent = '复制失败';
                            
                            setTimeout(() => {
                                button.textContent = '复制';
                            }, 2000);
                        }
                    });
                    
                    // 直接将按钮添加到公式块上，而不是嵌入内部
                    block.appendChild(button);
                }
            });
        }
            
        // 确保代码块内的滚动不会影响复制按钮位置
        document.querySelectorAll('.markdown-body pre').forEach(pre => {
            pre.addEventListener('scroll', function(e) {
                // 复制按钮应该保持在相对于pre元素的固定位置
                const copyBtn = this.querySelector('.code-copy-btn');
                if (copyBtn) {
                    copyBtn.style.right = '0.5rem'; // 保持固定位置
                }
            });
        });

        // 打开快捷键帮助弹窗
        function openShortcutsHelp() {
            const shortcutsModal = document.getElementById('shortcuts-help-modal');
            if (!shortcutsModal) return;
            
            shortcutsModal.classList.add('active');
        }

        // 关闭快捷键帮助弹窗
        function closeShortcutsHelp() {
            const shortcutsModal = document.getElementById('shortcuts-help-modal');
            if (!shortcutsModal) return;
            
            shortcutsModal.classList.remove('active');
        }

        // 初始化快捷键帮助
        function initShortcutsHelp() {
            const closeBtn = document.getElementById('close-shortcuts-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', closeShortcutsHelp);
            }
            
            // 添加帮助按钮的点击事件
            const shortcutsHelpBtn = document.getElementById('shortcuts-help-btn');
            if (shortcutsHelpBtn) {
                shortcutsHelpBtn.addEventListener('click', openShortcutsHelp);
            }
            
            // 点击模态框背景关闭
            const shortcutsModal = document.getElementById('shortcuts-help-modal');
            if (shortcutsModal) {
                shortcutsModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeShortcutsHelp();
                    }
                });
            }
            
            // 添加ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && shortcutsModal && shortcutsModal.classList.contains('active')) {
                    closeShortcutsHelp();
                }
            });
            
        }

        // 用于切换 Mermaid 代码和图表视图
        function toggleMermaidView(id) {
            const codeView = document.getElementById(`${id}-code`);
            const diagramView = document.getElementById(`${id}-diagram`);
            const toggleBtn = document.querySelector(`.mermaid-wrapper button.mermaid-toggle-btn`);
            
            if (!codeView || !diagramView) return;
            
            if (codeView.style.display === 'none') {
                codeView.style.display = 'block';
                diagramView.style.display = 'none';
                if (toggleBtn) toggleBtn.innerHTML = '<i class="fas fa-eye"></i> 显示图表';
            } else {
                codeView.style.display = 'none';
                diagramView.style.display = 'block';
                if (toggleBtn) toggleBtn.innerHTML = '<i class="fas fa-code"></i> 显示代码';
                
                // 延迟一点时间再尝试初始化，确保元素可见
                setTimeout(() => {
                    try {
                        mermaid.init(undefined, diagramView);
                    } catch (e) {
                        console.error('Mermaid 渲染错误:', e);
                        diagramView.innerHTML = `<div class="error-message">图表渲染失败: ${e.message}</div>`;
                    }
                }, 10);
            }
        }

        // 复制到剪贴板函数
        function copyToClipboard(button, text) {
            navigator.clipboard.writeText(text)
                .then(() => {
                    button.textContent = '已复制!';
                    button.classList.add('copied');
                    setTimeout(() => {
                        button.textContent = '复制代码';
                        button.classList.remove('copied');
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                    button.textContent = '复制失败';
                    setTimeout(() => {
                        button.textContent = '复制代码';
                    }, 2000);
                });
        }

    </script>
</body>
</html>
    