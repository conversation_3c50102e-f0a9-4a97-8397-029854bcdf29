下面是一份**面向 New API「OpenAI Responses」接口**的中文说明文档（Markdown），围绕**请求体的所有可能参数**逐一说明其**必填性、类型与可选值**，并在文末给出**覆盖字段尽可能全面**的响应示例（示意合成，为了教学完整性包含多场景条目；实际一次请求通常不会同时出现这么多输出项）。

> 注：本文基于 New API 官方文档页面内容整理，并对各字段的“可选值/默认值/层级结构”进行了系统化编排；行文中的关键处提供来源标注，便于核对。([New API][1])

---

# OpenAI Responses 请求体参数完整说明

## 端点与鉴权（概览）

* **HTTP 方法**：`POST /v1/responses`。([New API][1])
* **鉴权**：请求头 `Authorization: Bearer $NEWAPI_API_KEY`。([New API][1])

> 下文起，重点仅聚焦**请求体**参数。

---

## 参数总览速查表

| 参数名                    | 必填 | 类型              | 默认值        | 说明/可选值（概览）                                                                                                                                             |
| ---------------------- | -- | --------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `model`                | 是  | string          | —          | 模型 ID（如 `gpt-4.1`, `o3` 等）。([New API][1])                                                                                                              |
| `input`                | 是  | string 或 array  | —          | 文本或**输入项数组**（支持文本/图片/文件）；亦可按**消息对象**形式提供。([New API][1])                                                                                                |
| `instructions`         | 否  | string 或 null   | —          | 作为**系统/开发者**消息插入上下文首位；结合 `previous_response_id` 使用可替换系统指令。([New API][1])                                                                               |
| `include`              | 否  | array 或 null    | —          | 扩展输出内容：`file_search_call.results`、`message.input_image.image_url`、`computer_call_output.output.image_url`、`reasoning.encrypted_content`。([New API][1]) |
| `max_output_tokens`    | 否  | integer 或 null  | —          | 限制生成的最大 token 数（含可见与推理 token）。([New API][1])                                                                                                           |
| `metadata`             | 否  | object          | —          | 最多 16 对键值；键≤64 字符，值≤512 字符。([New API][1])                                                                                                              |
| `parallel_tool_calls`  | 否  | boolean 或 null  | `true`     | 允许并行调用工具。([New API][1])                                                                                                                                |
| `previous_response_id` | 否  | string 或 null   | —          | 用于建立**多轮会话**（串联上一个响应）。([New API][1])                                                                                                                   |
| `reasoning`            | 否  | object 或 null   | —          | 适用于 **o-series**；`effort`: `low`/`medium`/`high`；`summary`: `auto`/`concise`/`detailed`；`generate_summary` 已弃用。([New API][1])                          |
| `service_tier`         | 否  | string 或 null   | `auto`     | `auto` / `default` / `flex`（启用时响应体会回传使用的层级）。([New API][1])                                                                                             |
| `store`                | 否  | boolean 或 null  | `true`     | 是否存储可供后续检索。([New API][1])                                                                                                                              |
| `stream`               | 否  | boolean 或 null  | `false`    | 是否以 **SSE** 流式返回。([New API][1])                                                                                                                        |
| `temperature`          | 否  | number 或 null   | `1`        | 采样温度 $0,2$；建议与 `top_p` 二选一调整。([New API][1])                                                                                                            |
| `text`                 | 否  | object          | —          | **文本/结构化输出**配置；`format` 支持 `text` / `json_schema` /（不推荐新模型用）`json_object`。([New API][1])                                                               |
| `tool_choice`          | 否  | string 或 object | —          | `none` / `auto` / `required`，或**对象**指定 `hosted tool`/`function`。([New API][1])                                                                         |
| `tools`                | 否  | array           | —          | 可用工具清单：`file_search`/`function`/`web_search_preview`/`computer_use_preview` 等及其子配置。([New API][1])                                                      |
| `top_p`                | 否  | number 或 null   | `1`        | Nucleus 采样阈值；建议与 `temperature` 二选一。([New API][1])                                                                                                      |
| `truncation`           | 否  | string 或 null   | `disabled` | `auto` / `disabled`（超长上下文的截断策略）。([New API][1])                                                                                                         |
| `user`                 | 否  | string          | —          | 最终用户标识，用于监控/滥用检测。([New API][1])                                                                                                                        |

---

## 参数详解

### 1) `input`（必填）

* **类型**：`string` 或 **输入项数组**（支持**消息对象**）。([New API][1])
* **消息对象**字段：

  * `role`: `user` / `assistant` / `system` / `developer`（必填）
  * `content`: `string` 或 **内容项数组**（必填）
  * `type`: 固定 `message`（可省略） ([New API][1])

**内容项类型**：

* **文本** `input_text`：`{ "type": "input_text", "text": "..." }`（`text` 必填）。([New API][1])
* **图片** `input_image`：

  * `detail`: `high` / `low` / `auto`（默认 `auto`）
  * 可二选一提供 `image_url`（完整 URL 或 `data:` Base64）或 `file_id`。([New API][1])
* **文件** `input_file`：

  * 固定 `type`=`input_file`；可给 `file_id` / `file_data` / `filename`。([New API][1])

**（模型输出中的）输出项类型**（帮助理解 `include` 与注释引用）：

* `output_text`：`text`（必填） + `annotations`（数组，可含**文件引用**`file_citation`、**URL 引用**`url_citation`、**文件路径**`file_path` 三类）。([New API][1])

**工具调用项（出现在输出数组里）**：

* `file_search_call`：字段含 `id`、`queries`、`status`(`in_progress`/`searching`/`incomplete`/`failed`)、`type` 固定、`results`(可 null)。([New API][1])
* `web_search_call`：`id`、`status`、`type` 固定。([New API][1])
* `function_call`：`name`、`arguments`(JSON 字符串)、`call_id`，可含 `id`、`status`(`in_progress`/`completed`/`incomplete`)。([New API][1])
* `computer_call`：`action`(如 click/drag/keypress/move/screenshot/scroll/type/wait)、`call_id`、`id`、`pending_safety_checks`、`status`(`in_progress`/`completed`/`incomplete`)、`type` 固定。([New API][1])
* `computer_call_output`：`call_id`、`output`(屏幕截图对象)、可含 `acknowledged_safety_checks`、`id`、`status`。([New API][1])
* `function_call_output`：`call_id`、`output`(JSON 字符串)、可含 `id`、`status`。([New API][1])

**推理相关项（o-series）**：

* `reasoning` 项：`id`、`type`=`reasoning`、`summary`（数组，内部有 `summary_text`）、可含 `encrypted_content`、`status`。([New API][1])

**条目引用**：`item_reference`（`id` 必填，`type` 可省略固定为该字面）。([New API][1])

---

### 2) `model`（必填）

* **类型**：string（如 `gpt-4.1`、`o3`）。([New API][1])

---

### 3) `include`

* **类型**：array 或 null；**可选值**：

  * `file_search_call.results`
  * `message.input_image.image_url`
  * `computer_call_output.output.image_url`
  * `reasoning.encrypted_content`（保留推理 token 的加密版本用于多轮传递）。([New API][1], [Microsoft Learn][2])

---

### 4) `instructions`

* **类型**：string 或 null；插入为首条系统/开发者消息；与 `previous_response_id` 合用可切换系统指令。([New API][1])

---

### 5) `max_output_tokens`

* **类型**：integer 或 null；包括**可见与推理** token。([New API][1])

---

### 6) `metadata`

* **类型**：object；最多 **16** 对键值；**键**≤64、**值**≤512 字符。([New API][1])

---

### 7) `parallel_tool_calls`

* **类型**：boolean 或 null；默认 `true`；允许并行调用工具。([New API][1])

---

### 8) `previous_response_id`

* **类型**：string 或 null；用于多轮会话的**响应串联**。([New API][1])

---

### 9) `reasoning`（o-series）

* **类型**：object 或 null；

  * `effort`: `low` / `medium` / `high`（默认 `medium`）
  * `summary`: `auto` / `concise` / `detailed`（建议用此项；`generate_summary` 已弃用）([New API][1])

---

### 10) `service_tier`

* **类型**：string 或 null；默认 `auto`；可选：`auto` / `default` / `flex`；设置后响应体会回传 `service_tier`。([New API][1])

---

### 11) `store`

* **类型**：boolean 或 null；默认 `true`。([New API][1])

---

### 12) `stream`

* **类型**：boolean 或 null；默认 `false`；为 `true` 时以 SSE 流式事件返回。([New API][1])

---

### 13) `temperature`

* **类型**：number 或 null；默认 `1`；范围 $0, 2$；通常与 `top_p` 二选一调整。([New API][1])

---

### 14) `text`（结构化输出/文本输出配置）

* **类型**：object；字段：`format`（object）。
* **`format.type` 可选**：

  * `text`（纯文本）
  * `json_schema`（结构化输出，建议用于新模型；可含 `name`、`schema`、`description`、`strict`（布尔/空，严格模式仅支持 JSON Schema 子集））
  * `json_object`（旧 JSON 模式；**不推荐** gpt-4o 及更高使用）([New API][1])

---

### 15) `tool_choice`

* **类型**：`string` 或 `object`：

  * **String 模式**：`none` / `auto` / `required`。([New API][1])
  * **Object（托管/函数）**：

    * **Hosted tool**：`{ "type": "file_search" | "web_search_preview" | "computer_use_preview" }`。([New API][1])
    * **Function tool**：`{ "type": "function", "name": "<函数名>" }`。([New API][1])

> 说明：部分平台（如某些 Azure 版本）对 `tool_choice: "required"` 的支持节奏不同，以官方对应平台文档为准。([Microsoft Learn][3], [GitHub][4])

---

### 16) `tools`

* **类型**：array；列出**可供模型调用**的工具定义。([New API][1])

**A. File search（文件检索）**：

* `type`: 固定 `file_search`（必填）
* `vector_store_ids`: array（必填）
* `filters`: object（可选，见下）
* `max_num_results`: integer（1–50）
* `ranking_options`: object（`ranker`、`score_threshold`∈$0,1$）([New API][1])

**filters 类型**：

* **Comparison**：`{ key, type: "eq"|"ne"|"gt"|"gte"|"lt"|"lte", value }`（值可为 string/number/boolean）
* **Compound**：`{ type: "and"|"or", filters: [ ... ] }`。([New API][1])

**B. Function（函数工具/自定义工具）**：

* `type`: 固定 `function`（必填）
* `name`: string（必填）
* `parameters`: JSON Schema（必填）
* `strict`: boolean（默认 `true`）
* `description`: string（可选）([New API][1])

**C. Web search（预览版）**：

* `type`: `web_search_preview` 或 `web_search_preview_2025_03_11`
* `search_context_size`: `low` / `medium` / `high`（默认 `medium`）
* `user_location`: object（见下）
* `domains`: array（限制搜索域名）([New API][1])

`user_location` 对象：

* `type`: 固定 `approximate`（必填）
* 可选：`city`、`country`（两位 ISO）`region`、`timezone`（IANA）。([New API][1])

**D. Computer use（虚拟电脑操作，预览）**：

* `type`: 固定 `computer_use_preview`
* `display_height`、`display_width`（必填）
* `environment`（必填）([New API][1])

---

### 17) `top_p`

* **类型**：number 或 null；默认 `1`；Nucleus 采样（例如 `0.1` 表示仅考虑累计概率 10% 的候选）。**与 `temperature` 二选一**。([New API][1])

---

### 18) `truncation`

* **类型**：string 或 null；默认 `disabled`；可选：

  * `auto`：上下文超限时，**从会话中部**删减输入项以适配窗口；
  * `disabled`：超限直接 400。([New API][1], [OpenAI Community][5])

---

### 19) `user`

* **类型**：string；最终用户标识。([New API][1])

---

# 最全响应示例（示意合成）

> 说明：为了覆盖尽可能多的字段，下面示例**合并了多类输出项**（消息、函数调用、文件检索、电脑使用等）与常见顶层字段。实际一次请求通常只会出现与该请求相符的子集。字段名、类型与可选值均来源于上文对应段落。([New API][1])

```json
{
  "id": "resp_abc123",
  "object": "response",
  "created_at": 1744200000,
  "status": "completed",
  "error": null,
  "incomplete_details": null,

  "instructions": "You are a helpful assistant.",
  "max_output_tokens": 2048,
  "model": "gpt-4.1",

  "output": [
    {
      "type": "file_search_call",
      "id": "fs_001",
      "status": "completed",
      "queries": ["Summarize the internal design doc about project X"],
      "results": null
    },
    {
      "type": "function_call",
      "id": "fc_001",
      "call_id": "call_001",
      "name": "get_current_weather",
      "arguments": "{\"location\":\"Boston, MA\",\"unit\":\"celsius\"}",
      "status": "completed"
    },
    {
      "type": "computer_call",
      "id": "cc_001",
      "call_id": "call_cc_001",
      "pending_safety_checks": [],
      "status": "completed",
      "action": {
        "type": "click",
        "x": 320,
        "y": 240
      }
    },
    {
      "type": "computer_call_output",
      "id": "cco_001",
      "call_id": "call_cc_001",
      "output": {
        "image_url": "https://example.com/screenshot.png"
      },
      "acknowledged_safety_checks": []
    },
    {
      "type": "message",
      "id": "msg_001",
      "status": "completed",
      "role": "assistant",
      "content": [
        {
          "type": "output_text",
          "text": "Here is your summary... (and the current weather).",
          "annotations": [
            {
              "type": "file_citation",
              "index": 128,
              "file_id": "file-xyz",
              "filename": "design.pdf"
            },
            {
              "type": "url_citation",
              "start_index": 200,
              "end_index": 230,
              "title": "Boston Weather",
              "url": "https://weather.example.com"
            },
            {
              "type": "file_path",
              "index": 0,
              "file_id": "file-xyz"
            }
          ]
        }
      ]
    },
    {
      "type": "function_call_output",
      "id": "fco_001",
      "call_id": "call_001",
      "output": "{\"temp_c\": 22, \"condition\": \"Sunny\"}"
    }
  ],

  "parallel_tool_calls": true,
  "previous_response_id": "resp_prev_000",
  "reasoning": {
    "effort": "medium",
    "summary": "auto"
  },

  "store": true,
  "temperature": 0.7,

  "text": {
    "format": {
      "type": "json_schema",
      "name": "weather_summary",
      "description": "Strict JSON schema for downstream consumption",
      "strict": true,
      "schema": {
        "type": "object",
        "properties": {
          "summary": { "type": "string" },
          "temp_c": { "type": "number" }
        },
        "required": ["summary", "temp_c"],
        "additionalProperties": false
      }
    }
  },

  "tool_choice": "auto",

  "tools": [
    {
      "type": "file_search",
      "vector_store_ids": ["vs_123456"],
      "filters": {
        "type": "and",
        "filters": [
          { "type": "eq", "key": "project", "value": "X" },
          { "type": "gte", "key": "version", "value": 2 }
        ]
      },
      "max_num_results": 20,
      "ranking_options": {
        "ranker": "auto",
        "score_threshold": 0.5
      }
    },
    {
      "type": "function",
      "name": "get_current_weather",
      "description": "Get current weather",
      "strict": true,
      "parameters": {
        "type": "object",
        "properties": {
          "location": { "type": "string" },
          "unit": { "type": "string", "enum": ["celsius", "fahrenheit"] }
        },
        "required": ["location", "unit"]
      }
    },
    {
      "type": "web_search_preview",
      "search_context_size": "medium",
      "domains": ["example.com"],
      "user_location": {
        "type": "approximate",
        "country": "US",
        "city": "Boston",
        "region": "MA",
        "timezone": "America/New_York"
      }
    },
    {
      "type": "computer_use_preview",
      "display_height": 900,
      "display_width": 1440,
      "environment": "browser"
    }
  ],

  "top_p": 1.0,
  "truncation": "auto",

  "usage": {
    "input_tokens": 1234,
    "input_tokens_details": { "cached_tokens": 0 },
    "output_tokens": 456,
    "output_tokens_details": { "reasoning_tokens": 120 },
    "total_tokens": 1690
  },

  "user": "user-123",
  "metadata": {}
}
```

---

## 备忘与实践要点

* `temperature` 与 `top_p` **不建议同时调整**；择其一更稳定。([New API][1])
* 需要在后续轮次保留并传递推理痕迹时，可在 `include` 中加入 `reasoning.encrypted_content` 并沿用到下一次请求。([Microsoft Learn][2])
* `truncation: auto` 会在**上下文中部**丢弃项以适配窗口；若不希望隐式截断，可保持 `disabled`。([New API][1], [OpenAI Community][5])

---

### 参考来源

* New API 文档：**OpenAI Responses**（英/中）各参数与子类型定义。([New API][1])
* Azure/OpenAI 补充说明（可选阅读）：关于 `include.reasoning.encrypted_content`、`tool_choice` 等在不同平台/版本下的支持与实践讨论。([Microsoft Learn][2], [GitHub][4])

> 如果你需要，我可以把上面的参数“总览表”导出为一份 CSV / Excel 速查清单，或生成一套 Postman/OpenAPI 规范方便团队协作～

[1]: https://docs.newapi.pro/en/api/openai-responses/ "OpenAI Responses - New API"
[2]: https://learn.microsoft.com/en-us/azure/ai-foundry/openai/how-to/responses?utm_source=chatgpt.com "Azure OpenAI Responses API - Azure OpenAI | Microsoft Learn"
[3]: https://learn.microsoft.com/en-us/answers/questions/1690891/support-for-tool-choice-required-in-azure-openai-s?utm_source=chatgpt.com "Support for `\"tool_choice\": \"required\"` in Azure OpenAI service"
[4]: https://github.com/Azure/azure-rest-api-specs/issues/29844?utm_source=chatgpt.com "[BUG] `tool_choice=\"required\"` is still not supported in the latest ..."
[5]: https://community.openai.com/t/documentation-issues-responses-endpoint-storage-storage-persistence-truncation/1268533?utm_source=chatgpt.com "Documentation issues: Responses endpoint storage, storage persistence ..."
