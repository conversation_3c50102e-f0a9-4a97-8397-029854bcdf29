<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
</head>
<body>
    <h3>KaTeX 测试</h3>
    
    <h4>行内公式 (displayMode: false)</h4>
    <div id="inline"></div>
    
    <h4>行间公式 (displayMode: true)</h4>
    <div id="display"></div>
    
    <script>
        // 测试行内公式
        const inlineHtml = katex.renderToString("x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}", {
            displayMode: false,
            throwOnError: false
        });
        document.getElementById('inline').innerHTML = inlineHtml;
        console.log('行内公式 HTML:', inlineHtml);
        
        // 测试行间公式
        const displayHtml = katex.renderToString("x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}", {
            displayMode: true,
            throwOnError: false
        });
        document.getElementById('display').innerHTML = displayHtml;
        console.log('行间公式 HTML:', displayHtml);
    </script>
</body>
</html>
