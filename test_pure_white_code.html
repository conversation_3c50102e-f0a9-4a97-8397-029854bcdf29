<!DOCTYPE html>
<html lang="zh-CN" data-theme="pure-white">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纯白主题代码块样式测试</title>
    <style>
        /* 纯白主题变量 */
        :root {
            --primary: #000000;
            --primary-hover: #333333;
            --secondary: #666666;
            --accent: #000000;
            --success: #000000;
            --danger: #000000;
            --warning: #000000;

            --bg-main: #ffffff;
            --bg-secondary: #ffffff;
            --bg-tertiary: #ffffff;
            --bg-gradient: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);

            --text-primary: #000000;
            --text-secondary: #333333;
            --text-tertiary: #666666;
            --text-muted: #666666;
            --text-inverse: #ffffff;

            --border: #e0e0e0;
            --border-hover: #cccccc;
            --border-strong: #999999;

            --font-size-base: 14px;
            --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
            --radius: 8px;
        }

        body {
            font-family: Arial, sans-serif;
            background: var(--bg-main);
            color: var(--text-primary);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .theme-switcher {
            margin-bottom: 20px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 8px;
        }

        .theme-switcher button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .theme-switcher button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* 消息样式 */
        .message {
            margin: 20px 0;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }

        .message-bubble.user {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            color: #000000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .message-bubble.assistant {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            color: #000000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 通用代码块样式 */
        .message-bubble code:not(pre code) {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary);
            padding: calc(var(--font-size-base) * 0.1) calc(var(--font-size-base) * 0.3);
            border-radius: 4px;
            font-family: var(--font-mono);
            font-size: calc(var(--font-size-base) * 0.9);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .message.user .message-bubble code:not(pre code) {
            background: rgba(255, 152, 0, 0.15);
            color: #cc7a00;
            border-color: rgba(255, 152, 0, 0.3);
        }

        .message.user .message-bubble code {
            color: #cc7a00;
        }

        /* 纯白主题用户消息代码块样式 */
        [data-theme="pure-white"] .message.user .message-bubble code {
            color: #000000;
        }

        [data-theme="pure-white"] .message.user .message-bubble code:not(pre code) {
            background: #ffffff;
            color: #000000;
            border-color: #e0e0e0;
        }

        /* 纯白主题消息气泡 */
        [data-theme="pure-white"] .message-bubble.user {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            color: #000000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        [data-theme="pure-white"] .message-bubble.assistant {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            color: #000000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 代码块样式 */
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            overflow-x: auto;
            margin: 16px 0;
        }

        pre code {
            background: none;
            border: none;
            padding: 0;
            color: #333;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>纯白主题代码块样式测试</h1>
        
        <div class="theme-switcher">
            <strong>主题切换：</strong>
            <button onclick="setTheme('default')" id="defaultBtn">默认主题</button>
            <button onclick="setTheme('pure-white')" id="pureWhiteBtn" class="active">纯白主题</button>
        </div>

        <div class="test-section">
            <div class="test-title">用户消息中的内联代码</div>
            <div class="message user">
                <div class="message-bubble user">
                    请帮我解释一下这个 <code>console.log()</code> 函数的作用，以及 <code>document.getElementById()</code> 方法的用法。
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">AI回复中的内联代码</div>
            <div class="message assistant">
                <div class="message-bubble assistant">
                    <code>console.log()</code> 是JavaScript中用于在控制台输出信息的函数，而 <code>document.getElementById()</code> 用于通过ID获取DOM元素。
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">用户消息中的代码块</div>
            <div class="message user">
                <div class="message-bubble user">
                    这是我的代码：
                    <pre><code>function greet(name) {
    console.log("Hello, " + name);
}

greet("World");</code></pre>
                    请帮我优化一下。
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">AI回复中的代码块</div>
            <div class="message assistant">
                <div class="message-bubble assistant">
                    这是优化后的代码：
                    <pre><code>const greet = (name) => {
    console.log(`Hello, ${name}!`);
};

greet("World");</code></pre>
                    使用了箭头函数和模板字符串。
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">混合内容测试</div>
            <div class="message user">
                <div class="message-bubble user">
                    我想了解 <code>async/await</code> 的用法，特别是在处理 <code>fetch()</code> 请求时：
                    <pre><code>async function getData() {
    const response = await fetch('/api/data');
    const data = await response.json();
    return data;
}</code></pre>
                    这样写对吗？
                </div>
            </div>
        </div>
    </div>

    <script>
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            
            // 更新按钮状态
            document.querySelectorAll('.theme-switcher button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            if (theme === 'pure-white') {
                document.getElementById('pureWhiteBtn').classList.add('active');
            } else {
                document.getElementById('defaultBtn').classList.add('active');
            }
        }

        // 页面加载时设置为纯白主题
        window.onload = function() {
            setTheme('pure-white');
        };
    </script>
</body>
</html>
