# Perplexity 聚合模型批量添加完成

## 📋 添加的模型列表

已成功添加 **18个** Perplexity 聚合模型到 config.json 中：

### 🔍 Gemini 系列 (2个)
1. **PPLX-Gemini-2.5-Pro-06-05** (`char_pplx001`)
   - Model ID: `pplx-gemini-2.5-pro-06-05`
   - 价格: 1.25/10.0 (输入/输出 每百万token)
   - 图片: Gemini 官方图标

2. **PPLX-Gemini-2.5-Pro-06-05-Search** (`char_pplx002`)
   - Model ID: `pplx-gemini-2.5-pro-06-05-search`
   - 价格: 1.25/10.0 (输入/输出 每百万token)
   - 图片: Gemini 官方图标

### 🚀 Grok 系列 (2个)
3. **PPLX-Grok4** (`char_pplx003`)
   - Model ID: `pplx-grok4`
   - 价格: 3.0/15.0 (输入/输出 每百万token)
   - 图片: Grok 官方图标

4. **PPLX-Grok4-Search** (`char_pplx004`)
   - Model ID: `pplx-grok4-search`
   - 价格: 3.0/15.0 (输入/输出 每百万token)
   - 图片: Grok 官方图标

### 🧠 O3 系列 (2个)
5. **PPLX-O3** (`char_pplx005`)
   - Model ID: `pplx-o3`
   - 价格: 15.0/60.0 (输入/输出 每百万token)
   - 图片: GPT 官方图标

6. **PPLX-O3-Search** (`char_pplx006`)
   - Model ID: `pplx-o3-search`
   - 价格: 15.0/60.0 (输入/输出 每百万token)
   - 图片: GPT 官方图标

### 🎭 Claude 4.0 Sonnet 系列 (4个)
7. **PPLX-Claude-4.0-Sonnet** (`char_pplx007`)
   - Model ID: `pplx-claude-4.0-sonnet`
   - 价格: 3.0/15.0 (输入/输出 每百万token)
   - 图片: Claude 官方图标

8. **PPLX-Claude-4.0-Sonnet-Search** (`char_pplx008`)
   - Model ID: `pplx-claude-4.0-sonnet-search`
   - 价格: 3.0/15.0 (输入/输出 每百万token)
   - 图片: Claude 官方图标

9. **PPLX-Claude-4.0-Sonnet-Think** (`char_pplx009`)
   - Model ID: `pplx-claude-4.0-sonnet-think`
   - 价格: 3.0/15.0 (输入/输出 每百万token)
   - 图片: Claude 官方图标

10. **PPLX-Claude-4.0-Sonnet-Think-Search** (`char_pplx010`)
    - Model ID: `pplx-claude-4.0-sonnet-think-search`
    - 价格: 3.0/15.0 (输入/输出 每百万token)
    - 图片: Claude 官方图标

### 🔬 DeepSeek R1 系列 (2个)
11. **PPLX-DeepSeek-R1** (`char_pplx011`)
    - Model ID: `pplx-deepseek-r1`
    - 价格: 0.55/2.19 (输入/输出 每百万token)
    - 图片: DeepSeek 官方图标

12. **PPLX-DeepSeek-R1-Search** (`char_pplx012`)
    - Model ID: `pplx-deepseek-r1-search`
    - 价格: 0.55/2.19 (输入/输出 每百万token)
    - 图片: DeepSeek 官方图标

### 🤖 GPT 系列 (6个)
13. **PPLX-O4-Mini** (`char_pplx013`)
    - Model ID: `pplx-o4-mini`
    - 价格: 0.4/1.6 (输入/输出 每百万token)
    - 图片: GPT 官方图标

14. **PPLX-O4-Mini-Search** (`char_pplx014`)
    - Model ID: `pplx-o4-mini-search`
    - 价格: 0.4/1.6 (输入/输出 每百万token)
    - 图片: GPT 官方图标

15. **PPLX-GPT-4o** (`char_pplx015`)
    - Model ID: `pplx-gpt-4o`
    - 价格: 2.5/10.0 (输入/输出 每百万token)
    - 图片: GPT 官方图标

16. **PPLX-GPT-4o-Search** (`char_pplx016`)
    - Model ID: `pplx-gpt-4o-search`
    - 价格: 2.5/10.0 (输入/输出 每百万token)
    - 图片: GPT 官方图标

17. **PPLX-GPT-4.1** (`char_pplx017`)
    - Model ID: `pplx-gpt-4.1`
    - 价格: 2.5/10.0 (输入/输出 每百万token)
    - 图片: GPT 官方图标

18. **PPLX-GPT-4.1-Search** (`char_pplx018`)
    - Model ID: `pplx-gpt-4.1-search`
    - 价格: 2.5/10.0 (输入/输出 每百万token)
    - 图片: GPT 官方图标

## ⚙️ 统一配置

所有模型都使用以下统一配置：
- **类别**: Perplexity
- **标签**: 逆向
- **系统提示词**: `prompt_4b072e60`
- **状态**: 已激活 (is_active: true)
- **默认**: 非默认 (is_default: false)
- **作者**: 系统
- **创建时间**: 2025-07-27T00:00:00.000000+00:00

## 🎯 ID 生成规律

采用了有序的ID生成规律：
- 格式: `char_pplx{序号:03d}`
- 范围: `char_pplx001` 到 `char_pplx018`
- 便于后续管理和扩展

## 📊 价格策略

根据原始模型的官方定价进行匹配：
- **DeepSeek R1**: 最便宜 (0.55/2.19)
- **O4-Mini**: 经济型 (0.4/1.6)
- **Gemini/GPT-4o/GPT-4.1**: 中等价位 (1.25-2.5/10.0)
- **Grok4/Claude-4.0**: 高端价位 (3.0/15.0)
- **O3**: 顶级价位 (15.0/60.0)

## ✅ 完成状态

- ✅ 所有18个模型已成功添加
- ✅ JSON语法验证通过
- ✅ 价格匹配现有模型标准
- ✅ 图片URL匹配对应品牌
- ✅ 配置格式统一规范

现在您可以在前端界面中看到这些新添加的 Perplexity 聚合模型了！
