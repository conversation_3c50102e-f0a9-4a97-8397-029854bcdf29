#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对话备份系统
用于根据会话ID分类存储所有用户的请求，作为服务器本地对话备份
"""

import os
import json
import hashlib
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# 配置日志
logger = logging.getLogger(__name__)

class ConversationBackup:
    """对话备份管理器"""
    
    def __init__(self, backup_dir: str = "conversation_backups"):
        """
        初始化对话备份管理器
        
        Args:
            backup_dir: 备份目录路径
        """
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # 创建子目录结构
        self.users_dir = self.backup_dir / "users"
        self.sessions_dir = self.backup_dir / "sessions"
        self.daily_dir = self.backup_dir / "daily"
        
        for directory in [self.users_dir, self.sessions_dir, self.daily_dir]:
            directory.mkdir(exist_ok=True)
    
    def get_user_hash(self, user_identifier: str) -> str:
        """
        生成用户标识的哈希值，保护用户隐私
        
        Args:
            user_identifier: 用户标识（如IP、用户ID等）
            
        Returns:
            用户哈希值
        """
        return hashlib.sha256(user_identifier.encode('utf-8')).hexdigest()[:16]
    
    def get_session_file_path(self, session_id: str, user_hash: str) -> Path:
        """
        获取会话文件路径
        
        Args:
            session_id: 会话ID
            user_hash: 用户哈希值
            
        Returns:
            会话文件路径
        """
        # 按用户组织目录结构
        user_dir = self.users_dir / user_hash
        user_dir.mkdir(exist_ok=True)
        
        return user_dir / f"session_{session_id}.json"
    
    def save_request(self, 
                    session_id: str,
                    user_identifier: str,
                    request_data: Dict[str, Any],
                    response_data: Optional[Dict[str, Any]] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存请求到备份文件
        
        Args:
            session_id: 会话ID
            user_identifier: 用户标识
            request_data: 请求数据
            response_data: 响应数据（可选）
            metadata: 元数据（如IP、时间戳等）
            
        Returns:
            是否保存成功
        """
        try:
            user_hash = self.get_user_hash(user_identifier)
            session_file = self.get_session_file_path(session_id, user_hash)
            
            # 准备记录数据
            timestamp = datetime.now(timezone.utc).isoformat()
            record = {
                "timestamp": timestamp,
                "session_id": session_id,
                "user_hash": user_hash,
                "request": request_data,
                "response": response_data,
                "metadata": metadata or {}
            }
            
            # 读取现有会话数据
            session_data = []
            if session_file.exists():
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                except (json.JSONDecodeError, Exception) as e:
                    logger.warning(f"读取会话文件失败，创建新文件: {e}")
                    session_data = []
            
            # 添加新记录
            session_data.append(record)
            
            # 保存到会话文件
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            # 同时保存到会话索引
            self._update_session_index(session_id, user_hash, timestamp)
            
            # 保存到日常备份
            self._save_daily_backup(record, timestamp)
            
            logger.debug(f"成功保存对话记录: session={session_id}, user={user_hash[:8]}...")
            return True
            
        except Exception as e:
            logger.error(f"保存对话记录失败: {e}", exc_info=True)
            return False
    
    def _update_session_index(self, session_id: str, user_hash: str, timestamp: str):
        """更新会话索引"""
        try:
            index_file = self.sessions_dir / "index.json"
            
            # 读取现有索引
            index_data = {}
            if index_file.exists():
                try:
                    with open(index_file, 'r', encoding='utf-8') as f:
                        index_data = json.load(f)
                except (json.JSONDecodeError, Exception):
                    index_data = {}
            
            # 更新索引
            index_data[session_id] = {
                "user_hash": user_hash,
                "last_activity": timestamp,
                "created_at": index_data.get(session_id, {}).get("created_at", timestamp)
            }
            
            # 保存索引
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"更新会话索引失败: {e}")
    
    def _save_daily_backup(self, record: Dict[str, Any], timestamp: str):
        """保存到日常备份"""
        try:
            # 按日期组织
            date_str = timestamp[:10]  # YYYY-MM-DD
            daily_file = self.daily_dir / f"{date_str}.json"
            
            # 读取当日数据
            daily_data = []
            if daily_file.exists():
                try:
                    with open(daily_file, 'r', encoding='utf-8') as f:
                        daily_data = json.load(f)
                except (json.JSONDecodeError, Exception):
                    daily_data = []
            
            # 添加记录（只保存必要信息）
            daily_record = {
                "timestamp": record["timestamp"],
                "session_id": record["session_id"],
                "user_hash": record["user_hash"],
                "model": record["request"].get("model", "unknown"),
                "message_count": len(record["request"].get("messages", [])),
                "has_response": record["response"] is not None
            }
            daily_data.append(daily_record)
            
            # 保存
            with open(daily_file, 'w', encoding='utf-8') as f:
                json.dump(daily_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存日常备份失败: {e}")
    
    def get_session_history(self, session_id: str, user_identifier: str) -> Optional[list]:
        """
        获取会话历史记录
        
        Args:
            session_id: 会话ID
            user_identifier: 用户标识
            
        Returns:
            会话历史记录列表，如果不存在返回None
        """
        try:
            user_hash = self.get_user_hash(user_identifier)
            session_file = self.get_session_file_path(session_id, user_hash)
            
            if not session_file.exists():
                return None
            
            with open(session_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"获取会话历史失败: {e}")
            return None
    
    def get_user_sessions(self, user_identifier: str, limit: int = 50) -> list:
        """
        获取用户的所有会话
        
        Args:
            user_identifier: 用户标识
            limit: 返回的最大会话数量
            
        Returns:
            用户会话列表
        """
        try:
            user_hash = self.get_user_hash(user_identifier)
            user_dir = self.users_dir / user_hash
            
            if not user_dir.exists():
                return []
            
            sessions = []
            for session_file in user_dir.glob("session_*.json"):
                try:
                    session_id = session_file.stem.replace("session_", "")
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    if session_data:
                        last_record = session_data[-1]
                        sessions.append({
                            "session_id": session_id,
                            "last_activity": last_record["timestamp"],
                            "message_count": len(session_data),
                            "first_timestamp": session_data[0]["timestamp"]
                        })
                        
                except Exception as e:
                    logger.warning(f"读取会话文件失败: {session_file}, {e}")
                    continue
            
            # 按最后活动时间排序
            sessions.sort(key=lambda x: x["last_activity"], reverse=True)
            return sessions[:limit]
            
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []
    
    def get_daily_stats(self, date_str: str) -> Dict[str, Any]:
        """
        获取指定日期的统计信息
        
        Args:
            date_str: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            统计信息字典
        """
        try:
            daily_file = self.daily_dir / f"{date_str}.json"
            
            if not daily_file.exists():
                return {
                    "date": date_str,
                    "total_requests": 0,
                    "unique_users": 0,
                    "unique_sessions": 0,
                    "models": {}
                }
            
            with open(daily_file, 'r', encoding='utf-8') as f:
                daily_data = json.load(f)
            
            # 统计
            unique_users = set()
            unique_sessions = set()
            models = {}
            
            for record in daily_data:
                unique_users.add(record["user_hash"])
                unique_sessions.add(record["session_id"])
                
                model = record.get("model", "unknown")
                models[model] = models.get(model, 0) + 1
            
            return {
                "date": date_str,
                "total_requests": len(daily_data),
                "unique_users": len(unique_users),
                "unique_sessions": len(unique_sessions),
                "models": models
            }
            
        except Exception as e:
            logger.error(f"获取日常统计失败: {e}")
            return {
                "date": date_str,
                "total_requests": 0,
                "unique_users": 0,
                "unique_sessions": 0,
                "models": {}
            }
    
    def cleanup_old_backups(self, days_to_keep: int = 30):
        """
        清理旧的备份文件
        
        Args:
            days_to_keep: 保留的天数
        """
        try:
            cutoff_date = datetime.now(timezone.utc).date()
            
            # 清理日常备份
            for daily_file in self.daily_dir.glob("*.json"):
                try:
                    file_date_str = daily_file.stem
                    file_date = datetime.strptime(file_date_str, "%Y-%m-%d").date()
                    
                    if (cutoff_date - file_date).days > days_to_keep:
                        daily_file.unlink()
                        logger.info(f"删除过期的日常备份: {daily_file}")
                        
                except Exception as e:
                    logger.warning(f"处理日常备份文件失败: {daily_file}, {e}")
            
            logger.info(f"清理备份完成，保留最近 {days_to_keep} 天的数据")
            
        except Exception as e:
            logger.error(f"清理备份失败: {e}")


# 全局备份实例
conversation_backup = ConversationBackup()


def backup_conversation_request(session_id: str, 
                               user_identifier: str,
                               request_data: Dict[str, Any],
                               response_data: Optional[Dict[str, Any]] = None,
                               client_ip: Optional[str] = None) -> bool:
    """
    备份对话请求的便捷函数
    
    Args:
        session_id: 会话ID
        user_identifier: 用户标识
        request_data: 请求数据
        response_data: 响应数据
        client_ip: 客户端IP
        
    Returns:
        是否备份成功
    """
    metadata = {}
    if client_ip:
        metadata["client_ip"] = client_ip
    
    return conversation_backup.save_request(
        session_id=session_id,
        user_identifier=user_identifier,
        request_data=request_data,
        response_data=response_data,
        metadata=metadata
    )


def get_conversation_history(session_id: str, user_identifier: str) -> Optional[list]:
    """
    获取对话历史的便捷函数
    
    Args:
        session_id: 会话ID
        user_identifier: 用户标识
        
    Returns:
        对话历史列表
    """
    return conversation_backup.get_session_history(session_id, user_identifier) 