#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能消息备份系统
高效、优雅、去重的对话备份解决方案
"""

import os
import json
import hashlib
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

class SmartConversationBackup:
    """智能对话备份管理器"""
    
    def __init__(self, backup_dir: str = "smart_conversation_backups"):
        """初始化智能备份管理器"""
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # 创建子目录结构
        self.conversations_dir = self.backup_dir / "conversations"
        self.index_dir = self.backup_dir / "index"
        self.daily_stats_dir = self.backup_dir / "daily_stats"
        
        for directory in [self.conversations_dir, self.index_dir, self.daily_stats_dir]:
            directory.mkdir(exist_ok=True)
    
    def get_user_hash(self, user_identifier: str) -> str:
        """生成用户标识的哈希值，保护用户隐私"""
        return hashlib.sha256(user_identifier.encode('utf-8')).hexdigest()[:16]
    
    def get_conversation_file_path(self, session_id: str, user_hash: str) -> Path:
        """获取对话文件路径"""
        user_dir = self.conversations_dir / user_hash
        user_dir.mkdir(exist_ok=True)
        return user_dir / f"conversation_{session_id}.json"
    
    def generate_message_hash(self, content: str, role: str, timestamp: str) -> str:
        """生成消息内容哈希，用于去重"""
        content_str = f"{role}:{content}:{timestamp[:19]}"  # 精确到秒
        return hashlib.md5(content_str.encode('utf-8')).hexdigest()[:12]
    
    def extract_new_messages(self, current_messages: List[Dict], last_conversation: List[Dict]) -> List[Dict]:
        """提取新增的消息（智能去重）"""
        if not last_conversation:
            return current_messages
        
        # 获取上次对话中的最后一条消息时间戳
        last_timestamp = None
        if last_conversation:
            last_round = last_conversation[-1]
            if 'assistant_message' in last_round:
                last_timestamp = last_round['assistant_message'].get('timestamp')
            elif 'user_message' in last_round:
                last_timestamp = last_round['user_message'].get('timestamp')
        
        # 如果没有上次时间戳，返回所有消息
        if not last_timestamp:
            return current_messages
        
        # 找到新增的消息
        new_messages = []
        for msg in current_messages:
            msg_timestamp = msg.get('timestamp', '')
            if msg_timestamp > last_timestamp:
                new_messages.append(msg)
        
        return new_messages
    
    def add_conversation_round(self, 
                             session_id: str,
                             user_identifier: str, 
                             user_message: str,
                             assistant_message: str,
                             model: str,
                             client_ip: Optional[str] = None) -> bool:
        """添加一轮完整对话（用户消息 + AI回复）"""
        try:
            user_hash = self.get_user_hash(user_identifier)
            conversation_file = self.get_conversation_file_path(session_id, user_hash)
            
            # 生成时间戳
            current_time = datetime.now(timezone.utc).isoformat()
            
            # 读取现有对话数据
            conversation_data = self._load_conversation_data(conversation_file)
            
            # 检查是否是重复消息（去重机制）
            if self._is_duplicate_round(conversation_data.get('conversation', []), 
                                     user_message, assistant_message):
                logger.debug(f"检测到重复对话轮次，跳过保存: session={session_id}")
                return True
            
            # 计算当前轮数
            current_round = len(conversation_data.get('conversation', [])) + 1
            
            # 构建新的对话轮次
            new_round = {
                "round": current_round,
                "timestamp": current_time,
                "user_message": {
                    "content": user_message,
                    "timestamp": current_time  # 简化：使用同一时间戳
                },
                "assistant_message": {
                    "content": assistant_message,
                    "model": model,
                    "timestamp": current_time
                }
            }
            
            # 更新会话元数据
            if 'session_meta' not in conversation_data:
                conversation_data['session_meta'] = {
                    "session_id": session_id,
                    "user_hash": user_hash,
                    "created_at": current_time,
                    "client_ip": client_ip
                }
            
            conversation_data['session_meta'].update({
                "last_updated": current_time,
                "total_rounds": current_round,
                "models_used": list(set(conversation_data['session_meta'].get('models_used', []) + [model]))
            })
            
            if client_ip:
                conversation_data['session_meta']['client_ip'] = client_ip
            
            # 添加新轮对话
            if 'conversation' not in conversation_data:
                conversation_data['conversation'] = []
            conversation_data['conversation'].append(new_round)
            
            # 保存到文件
            with open(conversation_file, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)
            
            # 更新索引和统计
            self._update_conversation_index(session_id, user_hash, current_time, current_round, model)
            self._update_daily_stats(session_id, user_hash, model, current_time)
            
            logger.debug(f"✅ 智能备份成功: session={session_id}, round={current_round}")
            return True
            
        except Exception as e:
            logger.error(f"智能备份失败: {e}", exc_info=True)
            return False
    
    def _load_conversation_data(self, conversation_file: Path) -> Dict:
        """加载现有对话数据"""
        if not conversation_file.exists():
            return {}
        
        try:
            with open(conversation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"读取对话文件失败，创建新文件: {e}")
            return {}
    
    def _is_duplicate_round(self, conversation: List[Dict], user_msg: str, assistant_msg: str) -> bool:
        """检查是否是重复的对话轮次"""
        if not conversation:
            return False
        
        # 检查最后一轮对话是否相同
        last_round = conversation[-1]
        if (last_round.get('user_message', {}).get('content') == user_msg and 
            last_round.get('assistant_message', {}).get('content') == assistant_msg):
            return True
        
        return False
    
    def _update_conversation_index(self, session_id: str, user_hash: str, 
                                 timestamp: str, total_rounds: int, model: str):
        """更新对话索引"""
        try:
            index_file = self.index_dir / "conversations.json"
            
            # 读取现有索引
            index_data = {}
            if index_file.exists():
                try:
                    with open(index_file, 'r', encoding='utf-8') as f:
                        index_data = json.load(f)
                except (json.JSONDecodeError, Exception):
                    index_data = {}
            
            # 更新索引
            session_key = f"{user_hash}_{session_id}"
            index_data[session_key] = {
                "session_id": session_id,
                "user_hash": user_hash,
                "last_activity": timestamp,
                "total_rounds": total_rounds,
                "latest_model": model,
                "created_at": index_data.get(session_key, {}).get("created_at", timestamp)
            }
            
            # 保存索引
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"更新对话索引失败: {e}")
    
    def _update_daily_stats(self, session_id: str, user_hash: str, model: str, timestamp: str):
        """更新每日统计"""
        try:
            date_str = timestamp[:10]  # YYYY-MM-DD
            daily_file = self.daily_stats_dir / f"{date_str}.json"
            
            # 读取当日数据
            daily_data = {"date": date_str, "conversations": {}, "models": {}, "total_rounds": 0}
            if daily_file.exists():
                try:
                    with open(daily_file, 'r', encoding='utf-8') as f:
                        daily_data = json.load(f)
                except (json.JSONDecodeError, Exception):
                    pass
            
            # 更新统计
            session_key = f"{user_hash}_{session_id}"
            if session_key not in daily_data["conversations"]:
                daily_data["conversations"][session_key] = 0
            daily_data["conversations"][session_key] += 1
            
            daily_data["models"][model] = daily_data["models"].get(model, 0) + 1
            daily_data["total_rounds"] += 1
            
            # 保存统计
            with open(daily_file, 'w', encoding='utf-8') as f:
                json.dump(daily_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"更新每日统计失败: {e}")
    
    def get_conversation_data(self, session_id: str, user_identifier: str) -> Optional[Dict]:
        """获取完整对话数据"""
        try:
            user_hash = self.get_user_hash(user_identifier)
            conversation_file = self.get_conversation_file_path(session_id, user_hash)
            
            if not conversation_file.exists():
                return None
            
            with open(conversation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"获取对话数据失败: {e}")
            return None
    
    def get_conversation_stats(self, session_id: str, user_identifier: str) -> Dict:
        """获取对话统计信息"""
        conversation_data = self.get_conversation_data(session_id, user_identifier)
        
        if not conversation_data:
            return {"total_rounds": 0, "models_used": [], "duration": None}
        
        meta = conversation_data.get('session_meta', {})
        conversation = conversation_data.get('conversation', [])
        
        # 计算对话时长
        duration = None
        if conversation and len(conversation) > 0:
            start_time = conversation[0]['timestamp']
            end_time = conversation[-1]['timestamp']
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                duration = str(end_dt - start_dt)
            except:
                pass
        
        return {
            "total_rounds": meta.get('total_rounds', len(conversation)),
            "models_used": meta.get('models_used', []),
            "duration": duration,
            "created_at": meta.get('created_at'),
            "last_updated": meta.get('last_updated')
        }
    
    def cleanup_old_conversations(self, days_to_keep: int = 30):
        """清理旧的对话记录"""
        try:
            cutoff_date = datetime.now(timezone.utc).date()
            cleaned_count = 0
            
            # 清理对话文件
            for user_dir in self.conversations_dir.iterdir():
                if not user_dir.is_dir():
                    continue
                
                for conversation_file in user_dir.glob("conversation_*.json"):
                    try:
                        # 获取文件修改时间
                        file_mtime = datetime.fromtimestamp(conversation_file.stat().st_mtime).date()
                        
                        if (cutoff_date - file_mtime).days > days_to_keep:
                            conversation_file.unlink()
                            cleaned_count += 1
                            
                    except Exception as e:
                        logger.warning(f"处理对话文件失败: {conversation_file}, {e}")
            
            # 清理每日统计
            for daily_file in self.daily_stats_dir.glob("*.json"):
                try:
                    file_date_str = daily_file.stem
                    file_date = datetime.strptime(file_date_str, "%Y-%m-%d").date()
                    
                    if (cutoff_date - file_date).days > days_to_keep:
                        daily_file.unlink()
                        
                except Exception as e:
                    logger.warning(f"处理统计文件失败: {daily_file}, {e}")
            
            logger.info(f"清理完成，删除了 {cleaned_count} 个旧对话，保留最近 {days_to_keep} 天的数据")
            
        except Exception as e:
            logger.error(f"清理旧对话失败: {e}")


# 全局智能备份实例
smart_backup = SmartConversationBackup()


def backup_conversation_round(session_id: str,
                            user_identifier: str,
                            user_message: str,
                            assistant_message: str,
                            model: str,
                            client_ip: Optional[str] = None) -> bool:
    """
    🚀 智能备份一轮完整对话
    
    Args:
        session_id: 会话ID
        user_identifier: 用户标识
        user_message: 用户消息内容
        assistant_message: AI回复内容
        model: 使用的模型
        client_ip: 客户端IP
        
    Returns:
        是否备份成功
    """
    return smart_backup.add_conversation_round(
        session_id=session_id,
        user_identifier=user_identifier,
        user_message=user_message,
        assistant_message=assistant_message,
        model=model,
        client_ip=client_ip
    )


def get_smart_conversation_data(session_id: str, user_identifier: str) -> Optional[Dict]:
    """获取智能备份的对话数据"""
    return smart_backup.get_conversation_data(session_id, user_identifier)


def get_smart_conversation_stats(session_id: str, user_identifier: str) -> Dict:
    """获取智能备份的对话统计"""
    return smart_backup.get_conversation_stats(session_id, user_identifier) 