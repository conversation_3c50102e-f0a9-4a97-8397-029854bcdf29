import json
import time
import uuid
import random
import asyncio
import httpx
import mimetypes
import base64
from typing import Optional, Dict, List, Any
from pathlib import Path
from fastapi import HTTPException, Request
from fastapi.responses import StreamingResponse
from models import ChatCompletionRequest, ChatMessage
from utils import trim_context_messages, calculate_messages_tokens, extract_text_from_content, estimate_tokens
from config import get_timestamp
from conversation_backup import backup_conversation_request

async def retry_with_exponential_backoff(func, config: Dict, *args, **kwargs) -> Any:
    """带指数退避的重试装饰器"""
    # 从配置中获取重试参数
    retry_config = config.get('retry_config', {
        "max_retries": 3,
        "base_delay": 1.0,
        "max_delay": 10.0,
        "exponential_base": 2,
        "jitter": True,
        "retryable_status_codes": [429, 500, 502, 503, 504]
    })
    
    # 将列表转换为集合（因为JSON不支持集合，所以配置中存储为列表）
    retryable_status_codes = set(retry_config.get("retryable_status_codes", [429, 500, 502, 503, 504]))
    
    for attempt in range(retry_config.get("max_retries", 3) + 1):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # 检查是否是HTTPException且状态码可重试
            if isinstance(e, HTTPException) and e.status_code in retryable_status_codes:
                if attempt < retry_config.get("max_retries", 3):
                    # 计算延迟时间（指数退避 + 随机抖动）
                    delay = min(
                        retry_config.get("base_delay", 1.0) * 
                        (retry_config.get("exponential_base", 2) ** attempt),
                        retry_config.get("max_delay", 10.0)
                    )
                    
                    if retry_config.get("jitter", True):
                        delay = delay * (0.5 + random.random())  # 添加0.5-1.5倍随机抖动
                    
                    print(f"API请求失败 (尝试 {attempt + 1}/{retry_config.get('max_retries', 3) + 1}), {delay:.2f}秒后重试: {e.detail}")
                    await asyncio.sleep(delay)
                    continue
            
            # 如果不是可重试的错误，或者已达到最大重试次数，则抛出异常
            raise e

async def review_content(messages: List[ChatMessage], config: Dict) -> str:
    """意图审查函数（带重试）"""
    if not config.get('review_enabled', False):
        return "可"
    
    async def _make_review_request():
        try:
            # 构建审查请求 - 只审查文本内容，忽略图片
            content_to_review = ""
            recent_messages = messages[-3:]  # 只审查最近3条消息
            
            for msg in recent_messages:
                if msg.role in ['user', 'assistant']:
                    # 提取文本内容，忽略图片
                    text_content = extract_text_from_content(msg.content)
                    if text_content.strip():
                        # 如果是总结请求，特殊标记
                        if (msg.role == 'user' and text_content.strip() == '总结以往对话'):
                            content_to_review += "[历史对话总结请求]\n"
                        else:
                            content_to_review += f"{msg.role}: {text_content}\n"
            
            review_messages = [
                {"role": "system", "content": config['review_prompt']},
                {"role": "user", "content": content_to_review}
            ]
            
            review_request = {
                "model": config['review_api']['model'],
                "messages": review_messages,
                "temperature": 0.1,
                "max_tokens": 50
            }
            
            async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                headers = {
                    "Authorization": f"Bearer {config['review_api']['api_key']}",
                    "Content-Type": "application/json"
                }
                
                response = await client.post(
                    f"{config['review_api']['base_url']}/chat/completions",
                    headers=headers,
                    json=review_request
                )
                
                if response.status_code == 200:
                    result = response.json()
                    review_result = result['choices'][0]['message']['content'].strip()
                    print(f"审查结果: {review_result}")
                    
                    if review_result != "可":
                        return review_result
                    else:
                        return "可"
                else:
                    # 审查API错误，抛出HTTPException以便重试
                    try:
                        error_data = response.json()
                        error_msg = error_data.get('error', {}).get('message', response.text)
                    except:
                        error_msg = response.text
                    
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"审查API错误: {error_msg}"
                    )
                    
        except HTTPException:
            raise
        except Exception as e:
            print(f"意图审查异常: {e}")
            # 将其他异常转换为HTTPException，以便统一处理
            raise HTTPException(status_code=500, detail=f"审查服务异常: {str(e)}")
    
    try:
        return await retry_with_exponential_backoff(_make_review_request, config)
    except Exception as e:
        print(f"审查服务最终失败: {e}")
        return "可"  # 审查服务彻底失败时默认通过

async def process_message_content(content, config: Dict):
    """处理消息内容，支持 filename: 格式和 base64 图片"""
    if isinstance(content, str):
        return content
    elif isinstance(content, list):
        processed_content = []
        for item in content:
            if isinstance(item, dict):
                if item.get('type') == 'text':
                    processed_content.append(item)
                elif item.get('type') == 'image_url':
                    # 处理图片URL
                    image_url_data = item.get('image_url', {})
                    image_url = image_url_data.get('url', '')
                    
                    # 处理 filename: 格式（兼容旧版本）
                    if image_url.startswith('filename:'):
                        filename = image_url[9:]  # 去掉 'filename:' 前缀
                        
                        try:
                            # 从本地文件获取 base64 数据
                            from image_manager import ensure_upload_directory
                            from utils import image_to_base64
                            
                            upload_dir = ensure_upload_directory(config)
                            file_path = upload_dir / filename
                            
                            if file_path.exists() and file_path.is_file():
                                # 转换为纯 base64 数据供 API 使用
                                pure_base64 = image_to_base64(str(file_path), data_url=False)
                                if pure_base64:
                                    # 确定 MIME 类型
                                    mime_type, _ = mimetypes.guess_type(str(file_path))
                                    if not mime_type or not mime_type.startswith('image/'):
                                        mime_type = 'image/jpeg'
                                    
                                    final_url = f"data:{mime_type};base64,{pure_base64}"
                                    
                                    processed_item = {
                                        'type': 'image_url',
                                        'image_url': {
                                            'url': final_url,
                                            'detail': image_url_data.get('detail', 'auto')
                                        }
                                    }
                                    processed_content.append(processed_item)
                                else:
                                    print(f"[{get_timestamp()}] ❌ 文件转换失败: {filename}")
                                    continue
                            else:
                                print(f"[{get_timestamp()}] ❌ 文件不存在: {filename}")
                                continue
                                
                        except Exception as e:
                            print(f"[{get_timestamp()}] ❌ 处理文件名引用失败 {filename}: {e}")
                            continue
                    
                    # 处理 image_ref: 格式（新云端存储格式）
                    elif image_url.startswith('image_ref:'):
                        image_id = image_url[10:]  # 去掉 'image_ref:' 前缀
                        
                        try:
                            # 从本地缓存获取图片
                            from image_manager import ensure_upload_directory
                            from utils import image_to_base64
                            
                            upload_dir = ensure_upload_directory(config)
                            file_path = upload_dir / image_id
                            
                            if file_path.exists() and file_path.is_file():
                                # 转换为纯 base64 数据供 API 使用
                                pure_base64 = image_to_base64(str(file_path), data_url=False)
                                if pure_base64:
                                    # 确定 MIME 类型
                                    mime_type, _ = mimetypes.guess_type(str(file_path))
                                    if not mime_type or not mime_type.startswith('image/'):
                                        mime_type = 'image/jpeg'
                                    
                                    final_url = f"data:{mime_type};base64,{pure_base64}"
                                    
                                    processed_item = {
                                        'type': 'image_url',
                                        'image_url': {
                                            'url': final_url,
                                            'detail': image_url_data.get('detail', 'auto')
                                        }
                                    }
                                    processed_content.append(processed_item)
                                else:
                                    print(f"[{get_timestamp()}] ❌ 图片引用转换失败: {image_id}")
                                    continue
                            else:
                                print(f"[{get_timestamp()}] ❌ 图片引用文件不存在: {image_id}")
                                continue
                                
                        except Exception as e:
                            print(f"[{get_timestamp()}] ❌ 处理图片引用失败 {image_id}: {e}")
                            continue
                    
                    # 处理 base64 图片数据
                    elif image_url.startswith('data:'):
                        try:
                            header, data = image_url.split(',', 1)
                            mime_part = header.split(';')[0].replace('data:', '')
                            
                            # 确保是支持的图片MIME类型
                            supported_mimes = [
                                'image/jpeg', 'image/jpg', 'image/png', 
                                'image/webp', 'image/gif', 'image/bmp'
                            ]
                            
                            if mime_part not in supported_mimes:
                                mime_part = 'image/jpeg'
                                image_url = f"data:{mime_part};base64,{data}"
                            
                            processed_item = {
                                'type': 'image_url',
                                'image_url': {
                                    **image_url_data,
                                    'url': image_url
                                }
                            }
                            processed_content.append(processed_item)
                            
                        except Exception as e:
                            print(f"[{get_timestamp()}] ❌ 处理base64图片失败: {e}")
                            continue
                    else:
                        # 普通URL或其他格式，直接保留
                        processed_content.append(item)
                else:
                    processed_content.append(item)
            else:
                processed_content.append(item)
        
        return processed_content
    else:
        return content

async def process_responses_input_images(input_messages: list, config: Dict) -> list:
    """处理Responses API输入中的图片引用"""
    processed_messages = []

    for message in input_messages:
        if not isinstance(message, dict):
            processed_messages.append(message)
            continue

        processed_message = message.copy()

        # 处理content数组中的图片
        if 'content' in processed_message and isinstance(processed_message['content'], list):
            processed_content = []

            for item in processed_message['content']:
                if not isinstance(item, dict):
                    processed_content.append(item)
                    continue

                processed_item = item.copy()

                # 处理input_image类型的图片
                if item.get('type') == 'input_image':
                    if 'file_id' in item:
                        # 如果已经是file_id格式，直接使用
                        processed_content.append(processed_item)
                    elif 'image_url' in item:
                        image_url = item['image_url']

                        if isinstance(image_url, str):
                            if image_url.startswith('image_ref:') or image_url.startswith('filename:'):
                                # 转换为base64格式
                                image_id = image_url[10:] if image_url.startswith('image_ref:') else image_url[9:]

                                # 读取图片文件并转换为base64
                                try:
                                    base64_url = await convert_image_to_base64(image_id, config)
                                    if base64_url:
                                        processed_item['image_url'] = base64_url
                                        processed_item.pop('file_id', None)  # 确保移除file_id字段
                                    else:
                                        # 如果转换失败，跳过这个图片
                                        print(f"[{get_timestamp()}] 跳过无法转换的图片: {image_id}")
                                        continue
                                except Exception as e:
                                    print(f"[{get_timestamp()}] 图片转换失败: {e}")
                                    # 转换失败，跳过这个图片
                                    continue
                            # 其他格式（HTTP URL或base64）保持不变

                        processed_content.append(processed_item)
                    else:
                        processed_content.append(processed_item)
                else:
                    processed_content.append(processed_item)

            processed_message['content'] = processed_content

        processed_messages.append(processed_message)

    return processed_messages

async def convert_image_to_base64(image_id: str, config: Dict) -> Optional[str]:
    """将图片ID转换为base64格式的data URL"""
    try:
        from image_manager import get_image_file

        # 获取图片文件内容
        image_data = get_image_file(image_id, format="base64", config=config)

        if image_data and image_data.get('success') and image_data.get('base64_data'):
            return image_data['base64_data']

        return None

    except Exception as e:
        print(f"[{get_timestamp()}] 转换图片为base64失败: {e}")
        return None

async def proxy_openai_request(request: ChatCompletionRequest, config: Dict, original_request_body: Optional[dict] = None, context_history_turns: Optional[int] = None, original_model_id: Optional[str] = None, session_info: Optional[dict] = None, is_responses_api: bool = False):
    """代理OpenAI请求，现在接受原始请求体和上下文限制作为可选参数"""
    async def _make_request():
        try:
            # 获取请求信息
            requested_model = original_model_id or request.model  # 使用原始模型ID（角色ID）
            backend_model = request.model  # 角色系统已经在调用前映射了后端模型
            
            # 获取main_api配置，确保安全访问
            from config import DEFAULT_CONFIG
            main_api = config.get('main_api', {})
            if not main_api:
                # 如果main_api配置为空，使用默认配置
                main_api = DEFAULT_CONFIG.get('main_api', {
                    'base_url': 'https://api.killerbest.com/v1',
                    'api_key': 'sk-46BpPGmCYirz7sJ5uuMLizcDWA3E1xcSKGQhnz8F3APW05MB',
                    'model': 'gemini-2.5-pro-preview-06-05'
                })
            
            print(f"[{get_timestamp()}] 使用角色映射后的后端模型: {backend_model}")
            
            # 处理消息：确保content格式正确，同时检查系统提示词是否为空
            messages = []
            has_empty_system = False
            
            for msg in request.messages:
                if msg.role == "system":
                    # 对于系统消息，保持角色设置的提示词内容，只确保格式正确
                    if isinstance(msg.content, str):
                        content = msg.content
                    elif isinstance(msg.content, list):
                        # 如果是数组，提取文本部分
                        content = extract_text_from_content(msg.content)
                    else:
                        content = str(msg.content) if msg.content else ""
                    
                    # 检查系统提示词是否为空
                    if not content.strip():
                        has_empty_system = True
                        print(f"[{get_timestamp()}] 检测到空白系统提示词，跳过添加系统消息")
                    else:
                        messages.append({"role": "system", "content": content})
                else:
                    # 处理用户和助手消息的content，确保MIME类型正确
                    processed_content = await process_message_content(msg.content, config)
                    messages.append({"role": msg.role, "content": processed_content})
            
            # 只有在没有系统消息且不是空白系统提示词的情况下，才添加默认系统提示词
            if not any(msg["role"] == "system" for msg in messages) and not has_empty_system:
                default_prompt = config.get('system_prompt', "")
                if default_prompt.strip():  # 只有当默认提示词不为空时才添加
                    messages.insert(0, {"role": "system", "content": default_prompt})
            
            # 应用上下文限制
            context_config = config.get('context_config', DEFAULT_CONFIG['context_config'])
            
            # 精确计算原始token数
            original_tokens = calculate_messages_tokens(messages, backend_model)
            print(f"[{get_timestamp()}] 原始消息token数: {original_tokens}")
            
            # 检查是否启用上下文裁剪
            client_trimmed = original_request_body.get('client_trimmed', False) if original_request_body else False
            
            if context_config.get('enable_context_trim', True) and not client_trimmed:
                # 使用客户端传递的对话条数限制，如果没有则使用服务器配置
                if context_history_turns and context_history_turns > 0:
                    # 🐛 修复：直接使用消息条数，不再转换为轮数
                    max_messages = context_history_turns
                    max_turns = context_config.get('max_history_turns', 10)  # 服务器配置作为后备
                    print(f"[{get_timestamp()}] 使用客户端指定的对话条数限制: {max_messages}条消息")
                else:
                    max_messages = None
                    max_turns = context_config.get('max_history_turns', 10)
                    print(f"[{get_timestamp()}] 使用服务器配置的对话轮数限制: {max_turns}轮")
                
                max_tokens = context_config.get('max_context_tokens', 8000)
                
                # 裁剪上下文（传入后端模型名以便精确计算）
                original_count = len(messages)
                messages = trim_context_messages(messages, max_turns, max_tokens, backend_model, max_messages)
                
                if len(messages) < original_count:
                    final_tokens = calculate_messages_tokens(messages, backend_model)
                    print(f"[{get_timestamp()}] 上下文已裁剪：从{original_count}条消息({original_tokens} tokens)减少到{len(messages)}条消息({final_tokens} tokens)")
                else:
                    print(f"[{get_timestamp()}] 上下文无需裁剪：{len(messages)}条消息，{original_tokens} tokens")
            elif client_trimmed:
                print(f"[{get_timestamp()}] 🚀 前端已优化裁剪，跳过后端裁剪：{len(messages)}条消息，{original_tokens} tokens")
            else:
                print(f"[{get_timestamp()}] 上下文裁剪已禁用，保持原始消息数量: {len(messages)}条")
            
            # 使用原始请求体作为基础，确保保留所有原始参数
            if original_request_body:
                # 创建原始请求的深拷贝
                proxy_request = original_request_body.copy()
                # 只替换必要的字段
                proxy_request["model"] = backend_model
                proxy_request["messages"] = messages
            else:
                # 如果没有原始请求体，构建最小请求
                proxy_request = {
                    "model": backend_model,
                    "messages": messages,
                }
                
                # 仅添加请求中明确提供的字段
                for param in ["stream", "temperature", "max_tokens", "top_p", "frequency_penalty", "presence_penalty", "thinking_budget", "enable_thinking"]:
                    if hasattr(request, param) and getattr(request, param) is not None:
                        proxy_request[param] = getattr(request, param)
            
            headers = {
                "Authorization": f"Bearer {main_api.get('api_key', '')}",
                "Content-Type": "application/json"
            }
            
                    # 请求体日志已移除，避免日志过多

            if request.stream:
                # 流式响应：实时转发并替换模型ID，同时收集完整回复用于智能备份
                async def stream_response():
                    collected_content = ""  # 收集完整的AI回复内容
                    user_message = ""  # 获取用户消息
                    
                    # 从消息中提取最后一条用户消息
                    for msg in reversed(messages):
                        if msg.get('role') == 'user':
                            if isinstance(msg.get('content'), str):
                                user_message = msg.get('content', '')
                            elif isinstance(msg.get('content'), list):
                                # 提取文本内容
                                user_message = extract_text_from_content(msg.get('content', []))
                            break
                    
                    try:
                        async with httpx.AsyncClient(timeout=600.0, follow_redirects=True, verify=False) as client:
                            async with client.stream(
                                method="POST",
                                url=f"{main_api.get('base_url', 'https://api.killerbest.com/v1')}/{'responses' if is_responses_api else 'chat/completions'}",
                                headers=headers,
                                json=proxy_request
                            ) as response:
                                
                                if response.status_code != 200:
                                    error_content = await response.aread()
                                    try:
                                        error_data = json.loads(error_content.decode())
                                        error_msg = error_data.get('error', {}).get('message', error_content.decode())
                                    except:
                                        error_msg = error_content.decode()
                                    
                                    raise HTTPException(
                                        status_code=response.status_code,
                                        detail=f"API请求失败: {error_msg}"
                                    )
                                
                                async for chunk in response.aiter_bytes():
                                    if chunk:
                                        try:
                                            # 解码数据块
                                            chunk_str = chunk.decode('utf-8', errors='ignore')
                                            
                                            # 处理每一行数据
                                            lines = chunk_str.split('\n')
                                            processed_lines = []
                                            
                                            for line in lines:
                                                if line.startswith('data: '):
                                                    if line == 'data: [DONE]':
                                                        # 🚀 流式响应结束，检查备份配置并执行智能备份
                                                        if collected_content.strip() and user_message.strip():
                                                            # 检查备份配置
                                                            backup_config = config.get('conversation_backup', {})
                                                            backup_enabled = backup_config.get('enabled', True)
                                                            backup_type = backup_config.get('backup_type', 'smart')
                                                            
                                                            if backup_enabled and backup_type != 'disabled':
                                                                try:
                                                                    if backup_type == 'smart':
                                                                        from smart_conversation_backup import backup_conversation_round
                                                                    else:
                                                                        from conversation_backup import backup_conversation_request
                                                                    
                                                                    # 从传递的会话信息获取数据
                                                                    session_id = session_info.get('conversation_id') if session_info else 'unknown'
                                                                    user_identifier = session_info.get('user_identifier') if session_info else f"session_{session_id}"
                                                                    client_ip = session_info.get('client_ip') if session_info else "unknown"
                                                                    
                                                                    if backup_type == 'smart':
                                                                        backup_conversation_round(
                                                                            session_id=session_id,
                                                                            user_identifier=user_identifier,
                                                                            user_message=user_message,
                                                                            assistant_message=collected_content,
                                                                            model=proxy_request.get('model', 'unknown'),
                                                                            client_ip=client_ip
                                                                        )
                                                                        print(f"✅ 流式智能备份完成: session={session_id[:8] if len(session_id) > 8 else session_id}...")
                                                                    else:
                                                                        # 传统备份
                                                                        request_data = {
                                                                            "model": proxy_request.get('model', 'unknown'),
                                                                            "messages": [{"role": "user", "content": user_message}]
                                                                        }
                                                                        response_data = {
                                                                            "choices": [{"message": {"role": "assistant", "content": collected_content}}]
                                                                        }
                                                                        backup_conversation_request(
                                                                            session_id=session_id,
                                                                            user_identifier=user_identifier,
                                                                            request_data=request_data,
                                                                            response_data=response_data,
                                                                            client_ip=client_ip
                                                                        )
                                                                        print(f"✅ 流式传统备份完成: session={session_id[:8] if len(session_id) > 8 else session_id}...")
                                                                    
                                                                    # 统计流式响应信息
                                                                    try:
                                                                        completion_tokens = estimate_tokens(collected_content, backend_model)
                                                                        user_tokens = estimate_tokens(user_message, backend_model)
                                                                        print(f"[STREAM STATS] 用户输入: {user_tokens} tokens, AI输出: {completion_tokens} tokens, 响应字符数: {len(collected_content)}")
                                                                    except Exception as stats_error:
                                                                        print(f"[WARNING] 流式统计计算失败: {stats_error}")
                                                                    
                                                                except Exception as backup_error:
                                                                    print(f"[WARNING] 流式备份失败: {backup_error}")
                                                            else:
                                                                print(f"[INFO] 备份已禁用，跳过消息记录")
                                                        
                                                        processed_lines.append(line)
                                                    else:
                                                        try:
                                                            # 提取JSON数据
                                                            json_str = line[6:]  # 去掉 'data: ' 前缀
                                                            if json_str.strip():
                                                                # 解析JSON
                                                                data = json.loads(json_str)
                                                                
                                                                # 🚀 收集AI回复内容用于智能备份
                                                                delta = data.get('choices', [{}])[0].get('delta', {})
                                                                if 'content' in delta and delta['content']:
                                                                    collected_content += delta['content']
                                                                
                                                                # 替换模型字段
                                                                if 'model' in data:
                                                                    data['model'] = requested_model
                                                                # 重新序列化
                                                                processed_line = 'data: ' + json.dumps(data, ensure_ascii=False)
                                                                processed_lines.append(processed_line)
                                                            else:
                                                                processed_lines.append(line)
                                                        except json.JSONDecodeError:
                                                            # 如果不是有效JSON，直接添加原行
                                                            processed_lines.append(line)
                                                else:
                                                    # 非数据行，直接添加
                                                    processed_lines.append(line)
                                            
                                            # 重新组合并发送
                                            processed_chunk = '\n'.join(processed_lines)
                                            yield processed_chunk.encode('utf-8')
                                            
                                        except Exception as e:
                                            print(f"处理流式数据块时出错: {e}")
                                            # 如果处理失败，发送原始数据块
                                            yield chunk
                                            
                    except Exception as e:
                        print(f"流式响应错误: {e}")
                        # 发送错误信息
                        error_chunk = {
                            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                            "object": "chat.completion.chunk",
                            "created": int(time.time()),
                            "model": requested_model,
                            "choices": [{
                                "index": 0,
                                "delta": {"content": f"抱歉，服务暂时不可用: {str(e)}"},
                                "finish_reason": "stop"
                            }]
                        }
                        yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n".encode('utf-8')
                        yield "data: [DONE]\n\n".encode('utf-8')
                
                return StreamingResponse(
                    stream_response(), 
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                        "Access-Control-Allow-Methods": "*",
                        "Access-Control-Expose-Headers": "*"
                    }
                )
            else:
                # 非流式响应
                print(f"[{get_timestamp()}] 🔄 开始非流式请求到: {main_api.get('base_url', 'https://api.killerbest.com/v1')}/{'responses' if is_responses_api else 'chat/completions'}")
                print(f"[{get_timestamp()}] 📤 请求数据: {json.dumps(proxy_request, ensure_ascii=False, indent=2)}")

                async with httpx.AsyncClient(timeout=600.0, follow_redirects=True, verify=False) as client:
                    response = await client.post(
                        f"{main_api.get('base_url', 'https://api.killerbest.com/v1')}/{'responses' if is_responses_api else 'chat/completions'}",
                        headers=headers,
                        json=proxy_request
                    )

                    print(f"[{get_timestamp()}] 📥 收到响应状态码: {response.status_code}")
                    print(f"[{get_timestamp()}] 📥 响应头: {dict(response.headers)}")

                    if response.status_code != 200:
                        try:
                            error_data = response.json()
                            error_msg = error_data.get('error', {}).get('message', response.text)
                        except:
                            error_msg = response.text

                        print(f"[{get_timestamp()}] ❌ API请求失败: {error_msg}")
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"API请求失败: {error_msg}"
                        )

                    # 获取原始响应文本
                    response_text = response.text
                    print(f"[{get_timestamp()}] 📥 原始响应文本: {response_text}")

                    try:
                        result = response.json()
                        print(f"[{get_timestamp()}] 📥 解析后的JSON: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    except Exception as e:
                        print(f"[{get_timestamp()}] ❌ JSON解析失败: {e}")
                        print(f"[{get_timestamp()}] ❌ 原始文本: {response_text}")
                        raise HTTPException(status_code=500, detail=f"响应JSON解析失败: {e}")

                    # 替换模型ID为请求的模型ID
                    if 'model' in result:
                        result['model'] = requested_model

                    # 输出非流式响应统计信息
                    try:
                        if 'usage' in result:
                            usage = result['usage']
                            print(f"[NON-STREAM STATS] 输入: {usage.get('prompt_tokens', 0)} tokens, 输出: {usage.get('completion_tokens', 0)} tokens, 总计: {usage.get('total_tokens', 0)} tokens")

                        # 获取响应内容长度
                        response_content = ""
                        if 'choices' in result and result['choices']:
                            response_content = result['choices'][0].get('message', {}).get('content', '')
                            print(f"[NON-STREAM INFO] 响应字符数: {len(response_content)}, 后端模型: {backend_model}")
                    except Exception as e:
                        print(f"[WARNING] 非流式统计计算失败: {e}")

                    print(f"[{get_timestamp()}] ✅ 返回最终结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return result
                    
        except HTTPException:
            raise
        except Exception as e:
            print(f"代理请求异常: {e}")
            raise HTTPException(status_code=500, detail=f"代理请求失败: {str(e)}")
    
    # 对于流式请求，我们需要特殊处理重试逻辑
    if request.stream:
        # 流式请求直接执行，不重试（因为流已经开始）
        return await _make_request()

async def proxy_responses_request(request_body: dict, config: Dict, session_info: Optional[dict] = None, original_model_id: Optional[str] = None):
    """代理 Responses API 请求"""
    async def _make_request():
        try:
            # 获取main_api配置
            from config import DEFAULT_CONFIG
            main_api = config.get('main_api', {})

            if not main_api.get('api_key'):
                raise HTTPException(status_code=500, detail="主API密钥未配置")

            headers = {
                "Authorization": f"Bearer {main_api.get('api_key', '')}",
                "Content-Type": "application/json"
            }

            # 处理请求体，特别是图片引用
            proxy_request = request_body.copy()

            # 处理input中的图片引用
            if 'input' in proxy_request and isinstance(proxy_request['input'], list):
                proxy_request['input'] = await process_responses_input_images(proxy_request['input'], config)

            print(f"[{get_timestamp()}] Responses API 请求: 模型={proxy_request.get('model')}, 流式={proxy_request.get('stream')}")

            if proxy_request.get('stream'):
                # 流式响应
                async def stream_response():
                    try:
                        async with httpx.AsyncClient(timeout=600.0, follow_redirects=True, verify=False) as client:
                            async with client.stream(
                                method="POST",
                                url=f"{main_api.get('base_url', 'https://api.killerbest.com/v1')}/responses",
                                headers=headers,
                                json=proxy_request
                            ) as response:

                                if response.status_code != 200:
                                    error_content = await response.aread()
                                    error_msg = error_content.decode()
                                    print(f"[{get_timestamp()}] Responses API 错误: {response.status_code} - {error_msg}")
                                    # 流式响应中不能抛出 HTTPException，直接返回错误事件
                                    yield f"event: error\n"
                                    yield f"data: {json.dumps({'error': {'message': error_msg, 'code': response.status_code}}, ensure_ascii=False)}\n\n"
                                    return

                                async for chunk in response.aiter_bytes():
                                    if chunk:
                                        # 直接转发响应，不做修改
                                        yield chunk

                    except httpx.TimeoutException:
                        print(f"[{get_timestamp()}] Responses API 超时")
                        yield f"event: error\n"
                        yield f"data: {json.dumps({'error': {'message': '请求超时', 'code': 504}}, ensure_ascii=False)}\n\n"
                    except Exception as e:
                        print(f"[{get_timestamp()}] Responses API 流式请求异常: {str(e)}")
                        yield f"event: error\n"
                        yield f"data: {json.dumps({'error': {'message': f'请求失败: {str(e)}', 'code': 500}}, ensure_ascii=False)}\n\n"

                return StreamingResponse(
                    stream_response(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                        "Access-Control-Allow-Methods": "*",
                        "Access-Control-Expose-Headers": "*"
                    }
                )
            else:
                # 非流式响应
                async with httpx.AsyncClient(timeout=600.0, follow_redirects=True, verify=False) as client:
                    response = await client.post(
                        f"{main_api.get('base_url', 'https://api.killerbest.com/v1')}/responses",
                        headers=headers,
                        json=proxy_request
                    )

                    if response.status_code != 200:
                        error_content = response.text
                        print(f"[{get_timestamp()}] Responses API 错误: {response.status_code} - {error_content}")
                        raise HTTPException(status_code=response.status_code, detail=f"上游API错误: {error_content}")

                    result = response.json()

                    # 智能备份（如果需要）
                    if session_info and isinstance(result, dict):
                        try:
                            from smart_conversation_backup import backup_conversation_round

                            # 从 input 中提取用户消息
                            user_message = ""
                            input_data = request_body.get('input')
                            if isinstance(input_data, str):
                                user_message = input_data
                            elif isinstance(input_data, list):
                                for item in input_data:
                                    if isinstance(item, dict) and item.get('role') == 'user':
                                        content = item.get('content', '')
                                        if isinstance(content, str):
                                            user_message = content
                                        elif isinstance(content, list):
                                            # 提取文本内容
                                            for c in content:
                                                if isinstance(c, dict) and c.get('type') == 'input_text':
                                                    user_message = c.get('text', '')
                                                    break
                                        break

                            # 从 output 中提取AI回复
                            assistant_message = ""
                            output = result.get('output', [])
                            for item in output:
                                if isinstance(item, dict) and item.get('type') == 'message' and item.get('role') == 'assistant':
                                    content = item.get('content', [])
                                    for c in content:
                                        if isinstance(c, dict) and c.get('type') == 'output_text':
                                            assistant_message = c.get('text', '')
                                            break
                                    if assistant_message:
                                        break

                            # 智能备份
                            if user_message and assistant_message:
                                backup_conversation_round(
                                    session_id=session_info.get('conversation_id', 'unknown'),
                                    user_identifier=session_info.get('user_identifier', 'unknown'),
                                    user_message=user_message,
                                    assistant_message=assistant_message,
                                    model=original_model_id or request_body.get('model', 'unknown'),
                                    client_ip=session_info.get('client_ip', 'unknown')
                                )
                                print(f"✅ Responses API 智能备份完成")
                        except Exception as e:
                            print(f"[WARNING] Responses API 智能备份失败: {e}")

                    return result

        except HTTPException:
            raise
        except Exception as e:
            print(f"[{get_timestamp()}] Responses API 请求异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

    return await _make_request()